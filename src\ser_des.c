#include "tp6806.h"
#include "mi2c.h"
#include "config.h"
#include "tick.h"
#include "state.h"



#define MAX_DES_SALVE_ADDR	0x50
#define MAX_SER_SALVE_ADDR	0x80 //0x84
#define MAX96705_SALVE_ADDE 0x80
#define MAX_DES_DEVICE_ID 	0xCA
#define MAX_SER_DEVICE_ID	0xC8
#define MAX96706_SALVE_ADDE	0x90
#define USE_NEED_CHECK_SERDES 0
static uint8_t g_ser_salve_addr_list[2]={0x80,0x84};
typedef enum
{
	SSC_PPM_268 = 0x00,
	SSC_PPM_580,
	SSC_PPM_970,
	SSC_PPM_1750,
	SSC_PPM_2530
};
typedef enum
{
	SSC_PPM_REG_64 = 0,
	SSC_PPM_REG_70,
	SSC_PPM_REG_71,
	SSC_PPM_REG_72,
	SSC_PPM_REG_73,
	SSC_PPM_REG_74,
	SSC_PPM_REG_75,
	SSC_PPM_REG_76,
	SSC_PPM_REG_77
};
static uint8_t ssc_ppm_list_type = 0;

static uint8_t ssc_ppm_list[5][9]=
{
		{0x03,0x07,0x02,0xc9,0x02,0xF9,0x01,0x00,0x00},
		{0x03,0x06,0x02,0xab,0x00,0x63,0x07,0x00,0x00},
		{0x03,0x03,0x02,0xab,0x00,0x63,0x07,0x00,0x00},
		{0x03,0x01,0x02,0xf9,0x00,0x2c,0x05,0x00,0x00},
		{0x03,0x01,0x02,0xab,0x00,0x63,0x07,0x00,0x00}
};

#define msleep(x) { unsigned int _dcnt = (x*500); \
      while(_dcnt-- > 0);\
}
static uint8_t max96706_table[] =
{
	0x00, 0x80,
	//0x0d,0xB6,
	//0x06,0xef,
	//0x07,0x86,
	//0x04,0x88,

};
uint8_t check_max96705_state(void)
{
	uint8_t ret = 0;
	uint8_t value = 0;
	write_p0(0xDD, (read_p0(0xDD) & 0xF8));//0xFC));
	ret = mi2c_rd_byte(MAX96705_SALVE_ADDE, 0x1E, &value);
	if (value == 0x45)
		return 1;
	else
		return 0;

}

void max96706_init(void)
{
	uint8_t value;
	uint8_t ret;
	uint8_t retry;

	//I2C speed 100k
	//write_p0(0xD7, (read_p0(0xd7) | 0xC0));
	write_p0(0xDD, (read_p0(0xDD) & 0xF8));//0xFC));
	for (retry = 0; retry < 5; retry++)
	{
		ret = mi2c_rd_byte(MAX96706_SALVE_ADDE, 0x1E, &value);
		DEBUG_LOGI("ret = 0x%x, MAX96706 id = 0x%x\r\n", ret, value);
		if (value == 0x4a)
		{
#if 0			
			if (mi2c_load_tbl(MAX96706_SALVE_ADDE, max96706_table, sizeof(max96706_table)) == EXIT_FAILURE)
			{
				//DBGMSG(("max96706_init failed!\r\n"));
			}
#endif
			value = 0;
			msleep(20);
			ret = mi2c_rd_byte(MAX96705_SALVE_ADDE, 0x1E, &value);
			DEBUG_LOGI("ret = 0x%x, MAX96705 id = 0x%x\r\n", ret, value);
			if (value == 0x45) //0x41)
			{
				ret = mi2c_wr_byte(MAX96706_SALVE_ADDE, 0x0D, 0xAD);
				if (ret == EXIT_FAILURE)
				{
					DEBUG_LOGI("max96706_init 1failed!\r\n");
					continue;
				}
				msleep(20);
#if 0 
				ret = mi2c_wr_byte(MAX96705_SALVE_ADDE, 0x04, 0x43);
				if (ret == EXIT_FAILURE)
				{
					DEBUG_LOGI("max96706_init 2failed!\r\n");
					continue;
				}
				msleep(20);
#endif
				ret = mi2c_wr_byte(MAX96705_SALVE_ADDE, 0x4d, 0xc0);
				if (ret == EXIT_FAILURE)
				{
					DEBUG_LOGI("max96706_init 3failed!\r\n");
					continue;
				}
				msleep(20);
				ret = mi2c_wr_byte(MAX96706_SALVE_ADDE, 0x06, 0xef);
				if (ret == EXIT_FAILURE)
				{
					DEBUG_LOGI("max96706_init 4failed!\r\n");
					continue;
				}
				msleep(20);
				ret = mi2c_wr_byte(MAX96705_SALVE_ADDE, 0x07, 0x84);
				if (ret == EXIT_FAILURE)
				{
					DEBUG_LOGI("max96706_init 4failed!\r\n");
					continue;
				}
				msleep(20);
				ret = mi2c_wr_byte(MAX96706_SALVE_ADDE, 0x07, 0x86);
				if (ret == EXIT_FAILURE)
				{
					DEBUG_LOGI("max96706_init 6failed!\r\n");
					continue;
				}
				msleep(20);
				ret = mi2c_wr_byte(MAX96706_SALVE_ADDE, 0x04, 0x83);
				if (ret == EXIT_FAILURE)
				{
					DEBUG_LOGI("max96706_init 7failed!\r\n");
					continue;
				}
				DEBUG_LOGI("max96706_init ok!\r\n");
				return;
			}
		}
		msleep(20);
	}
	//app_info.error.DIAG_BITS.ERROR_MAX96706_I2C = 1;
}

void max_des_ser_init(void)
{
	uint8_t value = 0;
	uint8_t ret;
	uint8_t retry;
	uint8_t i;

  /*
	for(retry = 0;retry<0xFF;retry++)
	{
		ret = bsp_sim_i2c_read16_bytes(4,retry, 0x000D, &value,1);
		if ((value == MAX_SER_DEVICE_ID ) && (ret ==1))
		{
			DEBUG_LOGI("addr = 0x%0x\r\n", retry);
		}
		WDOG_Feed();
		KEA_Delay(100);
	}
	return;
	*/
	uint8_t t_max_ser_salve_addr = g_ser_salve_addr_list[0];

	for (retry = 0; retry < 5; retry++)
	{
#if (USE_NEED_CHECK_SERDES==1)
		for (i = 0;i <1; i++)
		{
			ret = bsp_sim_i2c_read16_bytes(4,g_ser_salve_addr_list[i], 0x000D, &value,1);
			if ((ret == 1) && (value == MAX_SER_DEVICE_ID))
					t_max_ser_salve_addr = g_ser_salve_addr_list[i];
		}
#endif
		//ret = bsp_sim_i2c_read8_bytes(4,0xB8,0xFE,&value,1);
		//ret = bsp_sim_i2c_read_reg(4,0xB8,0xFE,&value);//MAX_DES_SALVE_ADDE,0x0D,&value);
		//ret = mi2c_rd_byte(MAX_DES_SALVE_ADDE, 0x0D, &value);
		ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x000D, &value, 1);
		DEBUG_LOGI("ret = 0x%x, MAX_DES_id = 0x%x\r\n", ret, value);
		if (value == MAX_DES_DEVICE_ID)
		{
			value = 0;

#if (USE_NEED_CHECK_SERDES==1)
			KEA_Delay(10);
			ret = bsp_sim_i2c_read16_bytes(4,t_max_ser_salve_addr, 0x000D, &value,1);
			DEBUG_LOGI("ret = 0x%x, MAX_SER id = 0x%x\r\n", ret, value);
#endif
#if (USE_NEED_CHECK_SERDES==1)
			if (value == MAX_SER_DEVICE_ID)
#endif
			{
#if 0
				//disable csi output
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0313, 0x00);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x147F, 0x68);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x147E, 0xA8);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x14A3, 0x30);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x14D8, 0x07);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x14A5, 0x70);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0010, 0x31);
				if(ret == 0)
					continue;
				KEA_Delay(100);
				//Set MAX96717F
#if 0
				ret = bsp_sim_i2c_write_reg(4,MAX_SER_SALVE_ADDR, 0x0331, 0x30,1);
				if(ret == 0)
					continue;
				KEA_Delay(10);
				//disable Ser video pipe
				ret = bsp_sim_i2c_write_reg(4,MAX_SER_SALVE_ADDR, 0x0002, 0x03,1);
				if(ret == 0)
					continue;
				KEA_Delay(5);
				//enable Des video pipe
				ret = bsp_sim_i2c_write_reg(4,MAX_SER_SALVE_ADDR, 0x0002, 0x43,1);
				if(ret == 0)
					continue;
				KEA_Delay(10);
#endif
				//Enable CSI Output
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0313, 0x02);
				if(ret == 0)
					continue;


#if 0
				//Turn on BITLEN_MAN_CFG0, UART datarate=115200
				ret = bsp_sim_i2c_write_reg(4,MAX_SER_SALVE_ADDR, 0x004F, 0x0C,1);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_SER_SALVE_ADDR, 0x548, 0x16,1);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_SER_SALVE_ADDR, 0x0549, 0x05,1);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x004F, 0xCC,1);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x548, 0x16,1);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0549, 0x05,1);
				if(ret == 0)
					continue;
				//enable UART1

				ret = bsp_sim_i2c_write_reg(4,MAX_SER_SALVE_ADDR, 0x0003, 0x10,1);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0003, 0x10,1);
				if(ret == 0)
					continue;
#endif
#endif
#if 0
				ret = bsp_sim_i2c_write_reg(4,MAX_SER_SALVE_ADDR, 0x0383, 0x80,1);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0474, 0x19,1);
				if(ret == 0)
					continue;

#endif
#if (USE_SSC_PPM_REG==1)
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1471, 0x00);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1470, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_70]);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1471, 0x02);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1472, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_72]);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1473, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_73]);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1474, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_74]);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1475, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_75]);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1476, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_76]);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1477, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_77]);
				if(ret == 0)
					continue;
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1464, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_64]);
				if(ret == 0)
					continue;
#endif
#if (SENSOR_TYPE == SENSOR_TYPE_HIK_TYPE_3)
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0320, 0x02);
#else
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0320, 0x03);
#endif				
				if(ret == 0)
					continue;
				KEA_Delay(10);
				DEBUG_LOGI("SER_DES_INIT_OK\r\n");
				return;
			}
		}
		KEA_Delay(20);
	}
}
void max_des_ser_init_only_des(void)
{
	uint8_t value = 0;
	uint8_t ret;
	uint8_t retry;
	uint8_t i;

	uint8_t t_max_ser_salve_addr = g_ser_salve_addr_list[0];

	for (retry = 0; retry < 3; retry++)
	{
		ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x000D, &value, 1);
		DEBUG_LOGI("ret = 0x%x, MAX_DES_id = 0x%x\r\n", ret, value);
		if (value == MAX_DES_DEVICE_ID)
		{
			value = 0;

#if (SENSOR_TYPE == SENSOR_TYPE_HIK_TYPE_3)

				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0320, 0x02);
#else
				ret = bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x0320, 0x03);
#endif
				if(ret == 0)
					continue;
				KEA_Delay(10);
				DEBUG_LOGI("SER_DES_INIT_DES_OK\r\n");
				return;
		}
		KEA_Delay(20);
	}
}

uint8_t check_ser_des_lock(void)
{
	uint8_t ret = 0;
	uint8_t value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x0013, &value, 1);
	//DEBUG_LOGI("ret = 0x%x, MAX_DES_id = 0x%x\r\n", ret, value);
	if(ret == 1)
	{
		if((value&0x0A)== 0x0A)
			ret = 1;
		else
			ret = 0;
	}
	return ret;
}

uint8_t check_ser_des_error(void)
{
	uint8_t ret = 0;
	uint8_t value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x0022, &value, 1);
	DEBUG_LOGI("ret = 0x%x, MAX_DES_id_22 = 0x%x\r\n", ret, value);
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x0023, &value, 1);
	DEBUG_LOGI("ret = 0x%x, MAX_DES_id_23 = 0x%x\r\n", ret, value);
	return ret;
}

void ser_des_process(void)
{
	uint8_t value = 0;
	uint8_t ret = 0;

	bsp_sim_i2c_read16_bytes(4, MAX_SER_SALVE_ADDR, 0x0110, &value, 1);
	if ((value&0x6A)==0x6A)
	{
		return;
	}
	//DEBUG_LOGI("ser_des_process in\r\n");
	value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x1470, &value, 1);
	if (value !=0x01)
	{
		bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1470, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_70]);
	}
	value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x1471, &value, 1);
	if(value !=0x03)
	{
		bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1471, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_71]);
	}
	value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x1472, &value, 1);
	if(value !=0xab)
	{
		bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1472, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_72]);
	}
	value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x1473, &value, 1);
	if(value !=0x00)
	{
		bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1473, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_73]);
	}
	value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x1474, &value, 1);
	if(value !=0x63)
	{
		bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1474, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_74]);
	}
	value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x1475, &value, 1);
	if(value !=0x07)
	{
		bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1475, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_75]);
	}
	value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x1476, &value, 1);
	if(value !=0x00)
	{
		bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1476, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_76]);
	}
	value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x1477, &value, 1);
	if(value !=0x00)
	{
		bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1477, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_77]);
	}
	value = 0;
	ret = bsp_sim_i2c_read16_bytes(4, MAX_DES_SALVE_ADDR, 0x1477, &value, 1);
	if(value !=0x00)
	{
		bsp_sim_i2c_write_reg(4,MAX_DES_SALVE_ADDR, 0x1464, ssc_ppm_list[ssc_ppm_list_type][SSC_PPM_REG_64]);
	}
}
