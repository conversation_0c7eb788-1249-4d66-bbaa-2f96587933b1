/*
 * @file	timing_det.h
 * @brief	
 * <AUTHOR>
 * @date	2020-06-12 12:59:50 created
 * $Id: timing_det.h 1.1 $
 */

#ifndef TIMING_DET_H_
#define TIMING_DET_H_

#include "tp6823.h"
#include "config.h"

#if !defined(_EX_MCU_VERSION)
//#define TD_INTERUPT_ISR
#endif

#define OWL_XCLK			27000000L
#define OWL_XCLK_DIV		(OWL_XCLK >> 10)
#define MPTH_HSZIE_REG1		0x02
#define MPTH_HSZIE_REG2		0x03
	#define H_SIZE_BITS			12
#define MPTH_VSZIE_REG1		0x06
#define MPTH_VSZIE_REG2		0x07
	#define V_SIZE_BITS			12
#define MPTH_SYNC_TYPE_REG	0x0c
	#define VIN_VS_TYPE(type)	(type << 6)
	#define VIN_VS_SYNC			VIN_VS_TYPE(0)
	#define VIN_VS_DE			VIN_VS_TYPE(1)
	#define VIN_VS_FIELD		VIN_VS_TYPE(2)
	#define VIN_HS_TYPE(type)	(type << 4)
	#define VIN_HS_SYNC			VIN_HS_TYPE(0)
	#define VIN_HS_DE			VIN_HS_TYPE(1)
	#define VIN_HS_FIELD		VIN_HS_TYPE(2)
#define MPTH_DET_GEN_REG	0x0D
	#define MPTH_VCLK_INV		BIT(0)
	#define VS_PULSE2T			BIT(3)
	#define SYNC_GEN_EN			BIT(6)
	#define DET_BTSIZE_EN		BIT(7)
#define MPTH_BLANE_REG		0x0F
	#define BYTE_LANE_BITS		2
	#define BYTE_LANE_MASK		0x30
	#define LANE_CORLOR_RGB		BIT(6)
#define MPTH_VIN_EN_REG		0x0F
	#define INTERLACE_INPUT		BIT(2)
	#define MPTH_ODD_INV		BIT(3)
	#define MPTH_HS_INV			BIT(4)
	#define MPTH_VS_INV			BIT(5)
	#define VIDEO_IN_EN			BIT(7)

#define TD_CTRL_REG			0x20
	#define HS_DELTA_DCLKI		BIT(0)
	#define VS_DETLA_LINE		BIT(1)
	#define DV_INPUT_SEL		BIT(4)
	#define DET_RAWSYNC			BIT(5)
	#define MONITOR_EN			BIT(6)
	#define TD_TRIG				BIT(7)
#define TD_F_XCNT_REG1		0x21
#define TD_F_XCNT_REG2		0x22
#define TD_F_XCNT_REG3		0x23
	#define TD_F_XCNT_BITS		21
#define TD_V_LCNT_REG1		0x21
#define TD_V_LCNT_REG2		0x22
	#define TD_V_LCNT_BITS		13
#define TD_H_XCNT_REG1		0x24
#define TD_H_XCNT_REG2		0x25
	#define TD_H_XCNT_BITS		16
#define TD_H_KCNT_REG1		0x24
#define TD_H_KCNT_REG2		0x25
	#define TD_H_KCNT_BITS		16
#define TD_HS_DELTA_REG		0x26
#define TD_VS_DELTA_REG		0x27
#define TD_S_VS_THLD_REG1	0x28
#define TD_S_VS_THLD_REG2	0x29
	#define TD_S_VS_THLD_BITS	12
#define TD_HV_POLARITY_REG	0x2A
	#define VS_CBE_ODD			BIT(2)
	#define VS_POLARITY			BIT(1)
	#define HS_POLARITY			BIT(0)
#define TD_HOLD_FR_REG		0x2A
#define TD_FR_MASK_REG		0x2B
	#define IN_FR_REQ			BIT(7)
#define TD_SHOW_INFO_REG	0x2C
	#define SHOW_HS_DCLKI		BIT(0)
	#define SHOW_VS_LINE		BIT(1)
#define TD_IM_REG			0x2E
#define TD_IS_REG			0x2F
	#define HS_MISSING			BIT(0)
	#define VS_MISSING			BIT(1)
	#define HS_TIME_CHG			BIT(2)
	#define VS_TIME_CHG			BIT(3)
	#define SHORT_VS			BIT(4)
	#define VS_LEADING			BIT(5)
	#define DET_DONE			BIT(6)
	#define TO_200MS			BIT(7)

#define H_CROP_START_REG1	0x30
#define H_CROP_START_REG2	0x31
#define V_CROP_START_REG1	0x32
#define V_CROP_START_REG2	0x33
#define HI_SIZE_REG1		0x34
#define HI_SIZE_REG2		0x35
#define VI_SIZE_REG1		0x36
#define VI_SIZE_REG2		0x37
#define CROP_CTRL_REG		0x3F
	#define CROP_VS_FOLLOW		BIT(5)
	#define INPUT_IS_INTERLACE  BIT(6)
	#define CROP_EN_REG			BIT(7)

#define YCbC22RGB_EN_REG	0x6F
	#define YCbCr2RGB_En		BIT(7)

#define TD_GET_BPP()  ((read_p0(MPTH_BLANE_REG) & BYTE_LANE_MASK) > 0x10? 1 : 2)

#define TD_SET_SHORT_VS_THD(v)  \
	do { \
		write_p0(TD_S_VS_THLD_REG1, ((v -1) & 0xff)); \
		write_p0(TD_S_VS_THLD_REG2, (((v -1) >> 8) & 0xff)); \
	} while(0);

#define TD_GET_STATUS(sta)  \
	do { \
		sta = read_p0(TD_IS_REG); \
		write_p0(TD_IS_REG, sta); \
	} while(0);  
#define TD_IS_BUSY()			(read_p0(TD_CTRL_REG) & TD_TRIG)
#define TD_IS_MONITOR()			(read_p0(TD_CTRL_REG) & MONITOR_EN)
#define TD_IS_DET_DONE(sta)		(sta & DET_DONE)
#define TD_IS_VS_LEADING(sta)	(sta & VS_LEADING)
#define TD_IS_VD_LOSS(sta)		(sta & TO_200MS)
#define TD_IS_VS_MISSING(sta)	(sta & VS_MISSING)
#define TD_IS_VS_TIME_CHG(sta)	(sta & VS_TIME_CHG)
#define TD_IS_TIME_CHG(sta)		(sta & (VS_TIME_CHG | HS_TIME_CHG))
#define TD_IS_SYNC_MISS(sta)	(sta & (VS_MISSING | HS_MISSING))
#define TD_IS_SHORT_VS(sta)		(sta & SHORT_VS)
#define TD_CLEAR_SHORT_VS(sta)	(sta &= (~SHORT_VS))
#define TD_CLEAR_STA_DONE(sta)	(sta &= (~DET_DONE))
#define TD_SET_IM(mask)			write_p0(TD_IM_REG, mask)
#define TD_EN_MONITOR(en)		write_p0(TD_CTRL_REG, (en? (MONITOR_EN) : 0))
#define TD_DET_TRIGE()			write_p0(TD_CTRL_REG, TD_TRIG)
#define TD_SHOW_FRAME_CNT()		write_p0(TD_SHOW_INFO_REG, 0)
#define TD_SHOW_HSVS_CNT()		write_p0(TD_SHOW_INFO_REG, (SHOW_VS_LINE | SHOW_HS_DCLKI))
#define TD_DET_CLOSE()	\
	do { \
		write_p0(TD_CTRL_REG, 0); \
		write_p0(TD_IM_REG, 0); \
	} while(0); 
#define TD_CHECK_VD_LOSS()		(read_p0(TD_IS_REG) & TO_200MS)
#define TD_CHECK_VS_LEADING()	(read_p0(TD_IS_REG) & VS_LEADING)

#define TD_CLEAR_TIME_CHG()		(write_p0(TD_IS_REG, 0x1c))

#define HTOTAL_DISTORTION	20
#define VTOTAL_DISTORTION	10
#define FRATE_DISTORTION	2
#define HTOTAL_IS_HIT(src, tar) (  src <= (tar + HTOTAL_DISTORTION) \
								&& src >= (tar - HTOTAL_DISTORTION))
#define VTOTAL_IS_HIT(src, tar) (  src <= (tar + VTOTAL_DISTORTION) \
								&& src >= (tar - VTOTAL_DISTORTION))
#define FRATE_IS_HIT(src, tar)  (  src <= (tar + FRATE_DISTORTION) \
								&& src >= (tar - FRATE_DISTORTION))

// NTSC
#define NTSC_HTOTAL			858
#define NTSC_990H_HTOTAL	1180
#define NTSC_1980H_HTOTAL	2360
#define NTSC_VTOTAL			262
#define HTOTAL_IS_NTSC(h)	HTOTAL_IS_HIT(h, NTSC_HTOTAL)
#define VTOTAL_IS_NTSC(v)	VTOTAL_IS_HIT(v, NTSC_VTOTAL)
// PAL
#define PAL_HTOTAL			864
#define PAL_990H_HTOTAL		1180
#define PAL_1980H_HTOTAL	2370
#define PAL_VTOTAL			312
#define HTOTAL_IS_PAL(h)	HTOTAL_IS_HIT(h, PAL_HTOTAL)
#define VTOTAL_IS_PAL(v)	VTOTAL_IS_HIT(v, PAL_VTOTAL)
// 480P
#define SD_480P_HTOTAL		858
#define SD_480P_VTOTAL		525
#define SD_480P_HACTIVE		720
#define SD_480P_VACTIVE		480
#define HTOTAL_IS_SD480P(h)	HTOTAL_IS_HIT(h, SD_480P_HTOTAL)
#define VTOTAL_IS_SD480P(v)	VTOTAL_IS_HIT(v, SD_480P_VTOTAL)
// 576P
#define SD_576P_HTOTAL		864
#define SD_576P_VTOTAL		625
#define SD_576P_HACTIVE		720
#define SD_576P_VACTIVE		576
#define HTOTAL_IS_SD576P(h)	HTOTAL_IS_HIT(h, SD_576P_HTOTAL)
#define VTOTAL_IS_SD576P(v)	VTOTAL_IS_HIT(v, SD_576P_VTOTAL)
// 720P
#define HD_720P60_HTOTAL	1650
#define HD_720P50_HTOTAL	1980
#define HD_720P_VTOTAL		750
#define HD_720P_HACTIVE		1280
#define HD_720P_VACTIVE		720
#define HTOTAL_IS_HD720P60(h)	HTOTAL_IS_HIT(h, HD_720P60_HTOTAL)
#define HTOTAL_IS_HD720P50(h)	HTOTAL_IS_HIT(h, HD_720P50_HTOTAL)
#define VTOTAL_IS_HD720P(v)		VTOTAL_IS_HIT(v, HD_720P_VTOTAL)
// 1080P
#define FHD_1080P60_HTOTAL	2200
#define FHD_1080P50_HTOTAL	2640
#define FHD_1080P_VTOTAL	1125
#define FHD_1080P_HACTIVE	1920
#define FHD_1080P_VACTIVE	1080
#define HTOTAL_IS_FHD1080P60(h)	HTOTAL_IS_HIT(h, FHD_1080P60_HTOTAL)
#define HTOTAL_IS_FHD1080P50(h)	HTOTAL_IS_HIT(h, FHD_1080P50_HTOTAL)
#define VTOTAL_IS_FHD1080P(v)	VTOTAL_IS_HIT(v, FHD_1080P_VTOTAL)
// 1080i
#define FHD_1080I60_HTOTAL	2200
#define FHD_1080I50_HTOTAL	2640
#define FHD_1080I_VTOTAL	562
#define FHD_1080I_HACTIVE	1920
#define FHD_1080I_VACTIVE	540
#define HTOTAL_IS_FHD1080I60(h)	HTOTAL_IS_HIT(h, FHD_1080I60_HTOTAL)
#define HTOTAL_IS_FHD1080I50(h)	HTOTAL_IS_HIT(h, FHD_1080I50_HTOTAL)
#define VTOTAL_IS_FHD1080I(v)	VTOTAL_IS_HIT(v, FHD_1080I_VTOTAL)
// 344i
#if (SENSOR_TYPE == SENSOR_TYPE_HIK_TYPE_3)
#define HD_1920x384P60_HTOTAL 2070//3105
#define HD_1920x384P50_HTOTAL 2070//3105
#define HD_1920x384P_VTOTAL 482 //484
#else
#define HD_1920x384P60_HTOTAL 3105
#define HD_1920x384P50_HTOTAL 3105
#define HD_1920x384P_VTOTAL 484
#endif
#define HD_1920x384P_HACTIVE 1920
#define HD_1920x384P_VACTIVE 384

#define HTOTAL_IS_HD384I60(h)	HTOTAL_IS_HIT(h, HD_1920x384P60_HTOTAL)
#define HTOTAL_IS_HD384I50(h)	HTOTAL_IS_HIT(h, HD_1920x384P50_HTOTAL)
#define VTOTAL_IS_HD384I(v)	VTOTAL_IS_HIT(v, HD_1920x384P_VTOTAL)

#define FRATE_IS_60(f)		FRATE_IS_HIT(f, 60)
#define FRATE_IS_50(f)		FRATE_IS_HIT(f, 50)
#define FRATE_IS_30(f)		FRATE_IS_HIT(f, 30)
#define FRATE_IS_25(f)		FRATE_IS_HIT(f, 25)

// TD Status
enum {
	TD_STATE_NO_INPUT = 0,
	TD_STATE_VS_LEADING,
	TD_STATE_REQ_TIMING,
	TD_STATE_CALC_TIMING,
	TD_STATE_VS_STABLE,
	TD_STATE_VS_LOSS,
};
// TD Support Timing
enum {
	// higher priority
	/* other timing... */
	TD_NTSC,
	TD_PAL,
	TD_720P60,
	TD_720P50,
	TD_1080P60,
	TD_1080P50,
	TD_480P,
	TD_576P,
	TD_1080I60,
	TD_1080I50,
	TD_384I,
	TD_UNKNOWN_TIMING,
	TD_TOTAL_TIMING
};

#define TD_CALC_TIMING_CNT_MAX	(5 * 2)
extern unsigned char td_det_status;
extern unsigned char td_det_format;
extern void td_init(void);
extern void td_isr(void);
extern void td_timing_detect(void);

#if !defined(_EX_MCU_VERSION)
//#define TD2_INTERUPT_ISR
#endif

#define TD2_CTRL_REG		 0x30
	#define MONITOR2_EN			BIT(2)
	#define TD2_TRIG			BIT(3)
#define TD2_F_XCNT_REG1		0x31
#define TD2_F_XCNT_REG2		0x32
#define TD2_F_XCNT_REG3		0x33
	#define TD2_F_XCNT_BITS		21
#define TD2_V_LCNT_REG1		0x31
#define TD2_V_LCNT_REG2		0x32
	#define TD2_V_LCNT_BITS		13
#define TD2_H_XCNT_REG1		0x31
#define TD2_H_XCNT_REG2		0x32
	#define TD2_H_XCNT_BITS		16
#define TD2_H_KCNT_REG1		0x31
#define TD2_H_KCNT_REG2		0x32
	#define TD2_H_KCNT_BITS		16
#define TD2_IM_REG			0x34
#define TD2_IS_REG			0x35
	#define PVDE_FAILING		BIT(0)
	#define VS2_MISSING			BIT(1)
	#define TD2_TIMING_CHG		BIT(2)
	#define TD2_SHORT_VS		BIT(3)
	#define TD2_STABLE			BIT(4)
	#define VS_RISING			BIT(5)
	#define VDE_FAILING			BIT(6)
	#define DET2_DONE			BIT(7)
#define TD2_S_VS_THLD_REG1	0x31
#define TD2_S_VS_THLD_REG2	0x32

#define TD2_SET_SHORT_VS_THD(v)  \
	do { \
		write_p0(TD2_S_VS_THLD_REG1, ((v -1) & 0xff)); \
		write_p0(TD2_S_VS_THLD_REG2, (((v -1) >> 8) & 0xff)); \
	} while(0);

#define TD2_GET_STATUS(sta)  \
	do { \
		sta = read_p0(TD2_IS_REG); \
		write_p0(TD2_IS_REG, sta); \
	} while(0);  
#define TD2_IS_BUSY()			(read_p0(TD2_CTRL_REG) & TD2_TRIG)
#define TD2_IS_MONITOR()		(read_p0(TD2_CTRL_REG) & MONITOR2_EN)
#define TD2_IS_DET_DONE(sta)	(sta & DET2_DONE)
#define TD2_IS_VS_LEADING(sta)	(sta & VS_RISING)
#define TD2_IS_VD_LOSS(sta)		(TD2_IS_VS_LEADING(sta) == 0)
#define TD2_IS_VS_MISSING(sta)	(sta & VS2_MISSING)
#define TD2_IS_TIME_CHG(sta)	(sta & TD2_TIMING_CHG)
#define TD2_IS_SYNC_MISS(sta)	TD2_IS_VS_MISSING(sta)
#define TD2_IS_SHORT_VS(sta)	(sta & TD2_SHORT_VS)
#define TD2_CLEAR_SHORT_VS(sta)	(sta &= (~TD2_SHORT_VS))
#define TD2_CLEAR_STA_DONE(sta)	(sta &= (~DET2_DONE))
#define TD2_SET_IM(mask)		write_p0(TD2_IM_REG, mask)
#define TD2_EN_MONITOR(en)		write_p0(TD2_CTRL_REG, (en? (read_p0(TD2_CTRL_REG) | MONITOR2_EN) : (read_p0(TD2_CTRL_REG) & (~MONITOR2_EN))))
#define TD2_DET_TRIGE()			write_p0(TD2_CTRL_REG, (read_p0(TD2_CTRL_REG) | TD2_TRIG))
#define TD2_SHOW_FRAME_CNT()	write_p0(TD2_CTRL_REG, (read_p0(TD2_CTRL_REG) & 0x0f) | 0x60)
#define TD2_SHOW_VS_CNT()		write_p0(TD2_CTRL_REG, (read_p0(TD2_CTRL_REG) & 0x0f) | 0x40)
#define TD2_SHOW_HS_CNT()		write_p0(TD2_CTRL_REG, (read_p0(TD2_CTRL_REG) & 0x0f) | 0x20)
#define TD2_DET_CLOSE()	\
	do { \
		write_p0(TD2_CTRL_REG, (read_p0(TD2_CTRL_REG) & 0xf3)); \
		write_p0(TD2_IM_REG, 0); \
	} while(0); 
#define TD2_CHECK_VD_LOSS()		(!(read_p0(TD2_IS_REG) & VS_RISING))
#define TD2_CHECK_VS_LEADING()	(read_p0(TD2_IS_REG) & VS_RISING)
#define TD2_IS_STABLE()			(read_p0(0x33) & 0x80)

#define TD2_CLEAR_TIME_CHG()	(write_p0(TD2_IS_REG, 0x0c))

extern unsigned char td2_det_status;
extern unsigned char td2_det_format;
extern void td2_init(unsigned char sel);
extern void td2_timing_detect(void);
extern void td2_isr(void);

#endif  /* TIMING_DET_H_ */
