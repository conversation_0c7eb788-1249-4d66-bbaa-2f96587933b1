/*---------GLOBAL DEFINITIONS-----------*/
LIN_description_file;
LIN_protocol_version             = "2.1";
LIN_language_version             = "2.1";
LIN_speed                        = 19.2 kbps;
/* ----------NODE DEFINITIONS---------- */
Nodes {
   Master:
      CEM,                                                   /* Master node name */
      5.0 ms,                                                /* Time base */
      1.0 ms;                                                /* Jitter */
   Slaves:
      IRMM,
      RLSM,
      WMM;
}
/* ----------SIGNAL DEFINITIONS---------- */
Signals {
   /* Signal_name                : <PERSON><PERSON>,       Init,            Publisher, Subscriber(s) */
   AmbIllmnFwdStsAmblillmn1:  9,        0x0,                 RLSM, CEM;
   AmbIllmnFwdStsAmblillmn2:  8,        0x0,                 RLSM, CEM;
   AmbIllmnFwdStsChks   :  8,        0x0,                 RLSM, CEM;
   AmbIllmnFwdStsCntr   :  4,        0x0,                 RLSM, CEM;
   AMBStsAmbLiIllmn     : 16,        0x0,                 RLSM, CEM;
   AMBStsChks           :  8,        0x0,                 RLSM, CEM;
   AMBStsCntr           :  4,        0x0,                 RLSM, CEM;
   AmbTForVisy          :  8,        0x0,                  CEM, RLSM;
   AutWinWipgCmd        :  3,        0x0,                 RLSM, CEM,WMM;
   CmptFrntWindDewT     : 11,      0x258,                 RLSM, CEM;
   CmptFrntWindT        : 11,      0x28a,                 RLSM, CEM;
   EnaOfflineMonitor    :  1,        0x0,                  CEM, RLSM;
   FrqCfg               :  4,        0x0,                  CEM, RLSM;
   FWStsAmbLiIllmn      : 16,        0x0,                 RLSM, CEM;
   FWStsChks            :  8,        0x0,                 RLSM, CEM;
   FWStsCntr            :  4,        0x0,                 RLSM, CEM;
   HomeLinkEna          :  2,        0x0,                  CEM, RLSM;
   HudSnsrErrParChk     :  1,        0x0,                 RLSM, CEM;
   HudSnsrErrSnsrErr    :  1,        0x0,                 RLSM, CEM;
   IntrlMirrDispSysSts  :  3,        0x0,                 IRMM, CEM;
   IntrMirrCmdDrvrSide  :  1,        0x0,                  CEM, IRMM;
   IntrMirrCmdIntrMirrAsyFanCmpMag:  1,        0x0,                  CEM, IRMM;
   IntrMirrCmdIntrMirrDiagcRst:  1,        0x1,                  CEM, IRMM;
   IntrMirrCmdIntrMirrDimSnvty:  2,        0x0,                  CEM, IRMM;
   IntrMirrCmdIntrMirrEna:  1,        0x0,                  CEM, IRMM;
   IntrMirrCmdIntrMirrInhbDim:  1,        0x0,                  CEM, IRMM;
   IntrMirrCmdIntrMirrWindHeatrCmpMag:  1,        0x0,                  CEM, IRMM;
   IntrMirrRespIntrMirrDimPerc:  8,        0x0,                 IRMM, CEM;
   IntrMirrRespIntrMirrIntFailr:  1,        0x0,                 IRMM, CEM;
   IntrMirrRespResdBoolean:  1,        0x0,                 IRMM, CEM;
   IntrMirrRespResdUInt6:  6,        0x0,                 IRMM, CEM;
   LiOprnMod            :  2,        0x0,                 RLSM, CEM;
   LiOprnMod1           :  2,        0x0,                 RLSM, CEM;
   OutdBriChks          :  8,        0x0,                 RLSM, CEM;
   OutdBriCntr          :  4,        0x0,                 RLSM, CEM;
   OutdBriSts           :  2,        0x0,                 RLSM, CEM;
   PartNo10IRMMEndSgn1  :  8,        0x0,                 IRMM, CEM;
   PartNo10IRMMEndSgn2  :  8,        0x0,                 IRMM, CEM;
   PartNo10IRMMEndSgn3  :  8,        0x0,                 IRMM, CEM;
   PartNo10IRMMNr1      :  8,        0x0,                 IRMM, CEM;
   PartNo10IRMMNr2      :  8,        0x0,                 IRMM, CEM;
   PartNo10IRMMNr3      :  8,        0x0,                 IRMM, CEM;
   PartNo10IRMMNr4      :  8,        0x0,                 IRMM, CEM;
   PartNo10IRMMNr5      :  8,        0x0,                 IRMM, CEM;
   PartNoIRMMEndSgn1    :  8,        0x0,                 IRMM, CEM;
   PartNoIRMMEndSgn2    :  8,        0x0,                 IRMM, CEM;
   PartNoIRMMEndSgn3    :  8,        0x0,                 IRMM, CEM;
   PartNoIRMMNr1        :  8,        0x0,                 IRMM, CEM;
   PartNoIRMMNr2        :  8,        0x0,                 IRMM, CEM;
   PartNoIRMMNr3        :  8,        0x0,                 IRMM, CEM;
   PartNoIRMMNr4        :  8,        0x0,                 IRMM, CEM;
   RainDetected         :  1,        0x0,                 RLSM, CEM;
   RainfallAmnt         :  4,        0xe,                 RLSM, CEM;
   RainLi               :  1,        0x0,                 RLSM, CEM;
   RainSensActvn        :  1,        0x0,                  CEM, RLSM,WMM;
   RainSnsrDiagcRainSnsrHiTDetd:  1,        0x0,                 RLSM, CEM;
   RainSnsrDiagcRainSnsrHiVoltDetd:  1,        0x0,                 RLSM, CEM;
   RainSnsrErrCalErr    :  1,        0x0,                 RLSM, CEM;
   RainSnsrErrCalErrActv:  1,        0x0,                 RLSM, CEM;
   RainSnsrErrRainDetnErr:  1,        0x0,                 RLSM, CEM;
   RainSnsrErrRainDetnErrActv:  1,        0x0,                 RLSM, CEM;
   RainSnsrLiThd        :  4,        0x8,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty0:  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty1:  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty2:  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty3:  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty4:  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty5:  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty6:  4,        0x0,                  CEM, RLSM;
   ReAdaptReq           :  1,        0x0,                  CEM, RLSM;
   RelHumSnsrErr        :  1,        0x0,                 RLSM, CEM;
   RelHumSnsrRelHum     :  8,       0x50,                 RLSM, CEM;
   RLSMPartNo10EndSgn1  :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10EndSgn2  :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10EndSgn3  :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10Nr1      :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10Nr2      :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10Nr3      :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10Nr4      :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10Nr5      :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoEndSgn1    :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoEndSgn2    :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoEndSgn3    :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoNr1        :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoNr2        :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoNr3        :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoNr4        :  8,        0x0,                 RLSM, CEM;
   RLSMSerNoNr1         :  8,        0x0,                 RLSM, CEM;
   RLSMSerNoNr2         :  8,        0x0,                 RLSM, CEM;
   RLSMSerNoNr3         :  8,        0x0,                 RLSM, CEM;
   RLSMSerNoNr4         :  8,        0x0,                 RLSM, CEM;
   SerNoIRMMNr1         :  8,        0x0,                 IRMM, CEM;
   SerNoIRMMNr2         :  8,        0x0,                 IRMM, CEM;
   SerNoIRMMNr3         :  8,        0x0,                 IRMM, CEM;
   SerNoIRMMNr4         :  8,        0x0,                 IRMM, CEM;
   SolarSnsrErr         :  1,        0x0,                 RLSM, CEM;
   SolarSnsrLeValue     :  8,       0x33,                 RLSM, CEM;
   SolarSnsrRiValue     :  8,       0x33,                 RLSM, CEM;
   StreamingMirrBri     :  4,        0xa,                 IRMM, CEM;
   StreamingMirrBriAdjmt:  4,        0xa,                  CEM, IRMM;
   StreamingMirrEnable  :  1,        0x1,                 IRMM, CEM;
   StreamingMirrImg     :  4,        0x0,                 IRMM, CEM;
   StreamingMirrImgAdjmt:  4,        0x0,                  CEM, IRMM;
   StreamingMirrModeSts :  4,        0x0,                 IRMM, CEM;
   StreamingMirrModeSwt :  4,        0x0,                  CEM, IRMM;
   StreamingMirrPosn    :  4,        0x3,                 IRMM, CEM;
   StreamingMirrPosnAdjmt:  4,        0x3,                  CEM, IRMM;
   StreamingMirrSwt     :  1,        0x1,                  CEM, IRMM;
   TwliBriRawQf         :  2,        0x1,                 RLSM, CEM;
   TwliBriRawTwliBriRaw : 14,        0x0,                 RLSM, CEM;
   VehCfgPrmBlkIDBytePosn1:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmCCPBytePosn2:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmCCPBytePosn3:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmCCPBytePosn4:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmCCPBytePosn5:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmCCPBytePosn6:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmCCPBytePosn7:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmCCPBytePosn8:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmExtBlkIDBytePosn1:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmExtCCPBytePosn2:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmExtCCPBytePosn3:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmExtCCPBytePosn4:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmExtCCPBytePosn5:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmExtCCPBytePosn6:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmExtCCPBytePosn7:  8,        0x0,                  CEM, IRMM;
   VehCfgPrmExtCCPBytePosn8:  8,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1CarModSts1:  3,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1CarModSubtypWdCarModSubtyp:  3,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1Chks:  8,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1Cntr:  4,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1EgyLvlElecMai:  4,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1EgyLvlElecSubtyp:  4,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1FltEgyCnsWdSts:  1,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1PwrLvlElecMai:  4,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1PwrLvlElecSubtyp:  4,        0x0,                  CEM, IRMM;
   VehModMngtGlbSafe1UsgModSts:  4,        0x1,                  CEM, IRMM;
   VehSpdForWipg        :  8,        0x0,                  CEM, RLSM,WMM;
   VehTyp               :  4,        0x0,                  CEM, RLSM;
   WindCorrnValAmb      :  8,        0x0,                  CEM, RLSM;
   WindCorrnValFrnt     :  8,        0x0,                  CEM, RLSM;
   WindCorrnValHud      :  8,        0x0,                  CEM, RLSM;
   WipgAutFrntMod       :  2,        0x0,                 RLSM, CEM,WMM;
   WipgPwrActvnSafeWipgPwrAcsyModSafe:  2,        0x1,                  CEM, WMM;
   WipgPwrActvnSafeWipgPwrDrvgModSafe:  2,        0x1,                  CEM, WMM;
   WiprActv             :  1,        0x0,                  CEM, RLSM;
   WiprActvFromWMM      :  1,        0x0,                  WMM, CEM;
   WiprInPrkgPosnLo     :  1,        0x0,                  CEM, RLSM;
   WiprInPrkgPosnLoFromWMM:  1,        0x0,                  WMM, CEM;
   WiprInWipgAr         :  1,        0x0,                  CEM, RLSM;
   WiprInWipgArFromWMM  :  1,        0x0,                  WMM, CEM;
   WiprMotCrkAg         :  8,        0x0,                  WMM, CEM;
   WiprMotDiagcWiprMotHiVoltDetd:  2,        0x1,                  WMM, CEM;
   WiprMotDiagcWiprMotLoVoltDetd:  2,        0x1,                  WMM, CEM;
   WiprMotDiagcWiprMotOvldDetd:  2,        0x1,                  WMM, CEM;
   WiprMotErrSafe       :  2,        0x1,                  WMM, CEM;
   WiprMotErrSafe2      :  2,        0x1,                  WMM, CEM;
   WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe:  2,        0x1,                  CEM, WMM;
   WiprMotFrntLvrCmdSafeLvrInIntlPosn:  1,        0x0,                  CEM, WMM;
   WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe:  2,        0x1,                  CEM, WMM;
   WiprMotFrntLvrCmdSafeLvrInSnglStrokePos:  1,        0x0,                  CEM, WMM;
   WiprMotFrntOffsAg    :  4,        0x0,                  CEM, WMM;
   WiprMotIntlCmd       :  3,        0x0,                  CEM, RLSM,WMM;
   WiprPosnForSrvReq    :  1,        0x0,                  CEM, WMM;
   WMMPartNo10EndSgn1   :  8,        0x0,                  WMM, CEM;
   WMMPartNo10EndSgn2   :  8,        0x0,                  WMM, CEM;
   WMMPartNo10EndSgn3   :  8,        0x0,                  WMM, CEM;
   WMMPartNo10Nr1       :  8,        0x0,                  WMM, CEM;
   WMMPartNo10Nr2       :  8,        0x0,                  WMM, CEM;
   WMMPartNo10Nr3       :  8,        0x0,                  WMM, CEM;
   WMMPartNo10Nr4       :  8,        0x0,                  WMM, CEM;
   WMMPartNo10Nr5       :  8,        0x0,                  WMM, CEM;
   WMMPartNoEndSgn1     :  8,        0x0,                  WMM, CEM;
   WMMPartNoEndSgn2     :  8,        0x0,                  WMM, CEM;
   WMMPartNoEndSgn3     :  8,        0x0,                  WMM, CEM;
   WMMPartNoNr1         :  8,        0x0,                  WMM, CEM;
   WMMPartNoNr2         :  8,        0x0,                  WMM, CEM;
   WMMPartNoNr3         :  8,        0x0,                  WMM, CEM;
   WMMPartNoNr4         :  8,        0x0,                  WMM, CEM;
   WMMSerNoNr1          :  8,        0x0,                  WMM, CEM;
   WMMSerNoNr2          :  8,        0x0,                  WMM, CEM;
   WMMSerNoNr3          :  8,        0x0,                  WMM, CEM;
   WMMSerNoNr4          :  8,        0x0,                  WMM, CEM;
   WshngCycActv         :  1,        0x0,                  CEM, RLSM;
   WshngCycActvFromWMM  :  1,        0x0,                  WMM, CEM;
   WshrLvrPosnSafe      :  2,        0x1,                  CEM, WMM;
   ErrRespIRMM          :  1,        0x0,                 IRMM, CEM;
   ErrRespRLSM          :  1,        0x0,                 RLSM, CEM;
   ErrRespWMM           :  1,        0x0,                  WMM, CEM;
}
/* ----------DIAGNOSTIC SIGNAL DEFINITIONS---------- */
Diagnostic_signals {
   /* MasterReq Reserved Signals */
   MasterReqB0         :    8,   0;
   MasterReqB1         :    8,   0;
   MasterReqB2         :    8,   0;
   MasterReqB3         :    8,   0;
   MasterReqB4         :    8,   0;
   MasterReqB5         :    8,   0;
   MasterReqB6         :    8,   0;
   MasterReqB7         :    8,   0;
   /* SlaveResp Reserved Signals */
   SlaveRespB0         :    8,   0;
   SlaveRespB1         :    8,   0;
   SlaveRespB2         :    8,   0;
   SlaveRespB3         :    8,   0;
   SlaveRespB4         :    8,   0;
   SlaveRespB5         :    8,   0;
   SlaveRespB6         :    8,   0;
   SlaveRespB7         :    8,   0;
}
/* ----------UNCONDITIONAL FRAME DEFINITIONS---------- */
Frames {
   CemCem_Lin1Fr01:  0x5, CEM                 ,    6 {
      VehSpdForWipg, 0;
      WipgPwrActvnSafeWipgPwrAcsyModSafe, 8;
      WipgPwrActvnSafeWipgPwrDrvgModSafe, 10;
      RainSensActvn, 12;
      WshrLvrPosnSafe, 13;
      WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe, 16;
      WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe, 18;
      WiprMotFrntLvrCmdSafeLvrInSnglStrokePos, 20;
      WiprMotFrntLvrCmdSafeLvrInIntlPosn, 21;
      HomeLinkEna, 22;
      WiprMotIntlCmd, 24;
      WiprPosnForSrvReq, 27;
      IntrMirrCmdIntrMirrDimSnvty, 32;
      IntrMirrCmdDrvrSide, 34;
      IntrMirrCmdIntrMirrWindHeatrCmpMag, 35;
      IntrMirrCmdIntrMirrEna, 36;
      IntrMirrCmdIntrMirrInhbDim, 37;
      IntrMirrCmdIntrMirrDiagcRst, 38;
      IntrMirrCmdIntrMirrAsyFanCmpMag, 39;
      WiprMotFrntOffsAg, 40;
   }
   CemCem_Lin1Fr02:  0x7, CEM                 ,    7 {
      AmbTForVisy, 0;
      StreamingMirrBriAdjmt, 8;
      StreamingMirrImgAdjmt, 12;
      StreamingMirrPosnAdjmt, 16;
      StreamingMirrSwt, 20;
   }
   CemCem_Lin1Fr03: 0x17, CEM                 ,    8 {
      RainSnsrSnvtyForUsrSnvty0, 0;
      RainSnsrSnvtyForUsrSnvty1, 4;
      RainSnsrSnvtyForUsrSnvty2, 8;
      RainSnsrSnvtyForUsrSnvty3, 12;
      RainSnsrSnvtyForUsrSnvty4, 16;
      RainSnsrSnvtyForUsrSnvty5, 20;
      RainSnsrSnvtyForUsrSnvty6, 24;
      VehTyp     , 28;
      WindCorrnValAmb, 32;
      WindCorrnValFrnt, 40;
      WindCorrnValHud, 48;
      FrqCfg     , 56;
      ReAdaptReq , 60;
   }
   CemCem_Lin1Fr04:  0x9, CEM                 ,    7 {
      StreamingMirrModeSwt, 0;
      RainSnsrLiThd, 44;
   }
   CemCem_Lin1Fr05:  0xa, CEM                 ,    7 {
      EnaOfflineMonitor, 17;
   }
   CemCem_Lin1Fr06: 0x27, CEM                 ,    1 {
      WiprActv   , 4;
      WiprInPrkgPosnLo, 5;
      WiprInWipgAr, 6;
      WshngCycActv, 7;
   }
   CemCem_Lin1Fr07: 0x11, CEM                 ,    8 {
      VehCfgPrmBlkIDBytePosn1, 0;
      VehCfgPrmCCPBytePosn2, 8;
      VehCfgPrmCCPBytePosn3, 16;
      VehCfgPrmCCPBytePosn4, 24;
      VehCfgPrmCCPBytePosn5, 32;
      VehCfgPrmCCPBytePosn6, 40;
      VehCfgPrmCCPBytePosn7, 48;
      VehCfgPrmCCPBytePosn8, 56;
   }
   CemCem_Lin1Fr08:  0xc, CEM                 ,    8 {
      VehCfgPrmExtBlkIDBytePosn1, 0;
      VehCfgPrmExtCCPBytePosn2, 8;
      VehCfgPrmExtCCPBytePosn3, 16;
      VehCfgPrmExtCCPBytePosn4, 24;
      VehCfgPrmExtCCPBytePosn5, 32;
      VehCfgPrmExtCCPBytePosn6, 40;
      VehCfgPrmExtCCPBytePosn7, 48;
      VehCfgPrmExtCCPBytePosn8, 56;
   }
   CemCem_Lin1Fr09:  0x1, CEM                 ,    8 {
      VehModMngtGlbSafe1CarModSts1, 0;
      VehModMngtGlbSafe1FltEgyCnsWdSts, 3;
      VehModMngtGlbSafe1PwrLvlElecMai, 4;
      VehModMngtGlbSafe1CarModSubtypWdCarModSubtyp, 8;
      VehModMngtGlbSafe1UsgModSts, 11;
      VehModMngtGlbSafe1Cntr, 15;
      VehModMngtGlbSafe1PwrLvlElecSubtyp, 19;
      VehModMngtGlbSafe1EgyLvlElecMai, 24;
      VehModMngtGlbSafe1EgyLvlElecSubtyp, 28;
      VehModMngtGlbSafe1Chks, 32;
   }
   IrmmCem_Lin1Fr01: 0x10, IRMM                ,    4 {
      IntrMirrRespIntrMirrDimPerc, 0;
      IntrMirrRespResdUInt6, 8;
      IntrMirrRespIntrMirrIntFailr, 14;
      IntrMirrRespResdBoolean, 15;
      IntrlMirrDispSysSts, 20;
      ErrRespIRMM, 23;
   }
   IrmmCem_Lin1Fr02: 0x13, IRMM                ,    8 {
      StreamingMirrBri, 0;
      StreamingMirrImg, 4;
      StreamingMirrPosn, 8;
      StreamingMirrEnable, 12;
      StreamingMirrModeSts, 56;
   }
   IrmmCem_Lin1PartNrFr01: 0x12, IRMM                ,    7 {
      PartNoIRMMNr1, 0;
      PartNoIRMMNr2, 8;
      PartNoIRMMNr3, 16;
      PartNoIRMMNr4, 24;
      PartNoIRMMEndSgn1, 32;
      PartNoIRMMEndSgn2, 40;
      PartNoIRMMEndSgn3, 48;
   }
   IrmmCem_Lin1PartNrFr02: 0x16, IRMM                ,    8 {
      PartNo10IRMMNr1, 0;
      PartNo10IRMMNr2, 8;
      PartNo10IRMMNr3, 16;
      PartNo10IRMMNr4, 24;
      PartNo10IRMMNr5, 32;
      PartNo10IRMMEndSgn1, 40;
      PartNo10IRMMEndSgn2, 48;
      PartNo10IRMMEndSgn3, 56;
   }
   IrmmCem_Lin1SerNrFr01: 0x14, IRMM                ,    4 {
      SerNoIRMMNr1, 0;
      SerNoIRMMNr2, 8;
      SerNoIRMMNr3, 16;
      SerNoIRMMNr4, 24;
   }
   RlsmCem_Lin1Fr01: 0x15, RLSM                ,    8 {
      AutWinWipgCmd, 0;
      HudSnsrErrParChk, 3;
      HudSnsrErrSnsrErr, 4;
      RainLi     , 7;
      RainSnsrErrCalErrActv, 8;
      RainSnsrErrCalErr, 9;
      RainSnsrErrRainDetnErr, 10;
      RainSnsrErrRainDetnErrActv, 11;
      WipgAutFrntMod, 12;
      ErrRespRLSM, 15;
      TwliBriRawQf, 24;
      TwliBriRawTwliBriRaw, 26;
      OutdBriChks, 48;
      OutdBriSts , 56;
      OutdBriCntr, 58;
   }
   RlsmCem_Lin1Fr02: 0x2c, RLSM                ,    8 {
      CmptFrntWindDewT, 0;
      AmbIllmnFwdStsCntr, 11;
      AmbIllmnFwdStsAmblillmn2, 16;
      AmbIllmnFwdStsChks, 24;
      AmbIllmnFwdStsAmblillmn1, 32;
      RelHumSnsrErr, 42;
      CmptFrntWindT, 45;
      RelHumSnsrRelHum, 56;
   }
   RlsmCem_Lin1Fr03:  0x2, RLSM                ,    8 {
      SolarSnsrErr, 0;
      RainSnsrDiagcRainSnsrHiVoltDetd, 3;
      RainSnsrDiagcRainSnsrHiTDetd, 4;
      LiOprnMod  , 5;
      RainDetected, 7;
      RainfallAmnt, 8;
      SolarSnsrLeValue, 16;
      SolarSnsrRiValue, 24;
   }
   RlsmCem_Lin1Fr04:  0x3, RLSM                ,    8 {
      LiOprnMod1 , 0;
      AMBStsCntr , 4;
      AMBStsAmbLiIllmn, 8;
      AMBStsChks , 24;
      FWStsCntr  , 36;
      FWStsAmbLiIllmn, 40;
      FWStsChks  , 56;
   }
   RlsmCem_Lin1PartNrFr01: 0x20, RLSM                ,    7 {
      RLSMPartNoNr1, 0;
      RLSMPartNoNr2, 8;
      RLSMPartNoNr3, 16;
      RLSMPartNoNr4, 24;
      RLSMPartNoEndSgn1, 32;
      RLSMPartNoEndSgn2, 40;
      RLSMPartNoEndSgn3, 48;
   }
   RlsmCem_Lin1PartNrFr02: 0x18, RLSM                ,    8 {
      RLSMPartNo10Nr1, 0;
      RLSMPartNo10Nr2, 8;
      RLSMPartNo10Nr3, 16;
      RLSMPartNo10Nr4, 24;
      RLSMPartNo10Nr5, 32;
      RLSMPartNo10EndSgn1, 40;
      RLSMPartNo10EndSgn2, 48;
      RLSMPartNo10EndSgn3, 56;
   }
   RlsmCem_SerNrLin1Fr01: 0x22, RLSM                ,    4 {
      RLSMSerNoNr1, 0;
      RLSMSerNoNr2, 8;
      RLSMSerNoNr3, 16;
      RLSMSerNoNr4, 24;
   }
   WmmCem_Lin1Fr01: 0x25, WMM                 ,    4 {
      WiprMotErrSafe2, 0;
      WiprActvFromWMM, 8;
      WiprInPrkgPosnLoFromWMM, 10;
      WiprInWipgArFromWMM, 11;
      WiprMotErrSafe, 13;
      ErrRespWMM , 15;
      WiprMotDiagcWiprMotHiVoltDetd, 16;
      WiprMotDiagcWiprMotLoVoltDetd, 18;
      WiprMotDiagcWiprMotOvldDetd, 20;
      WshngCycActvFromWMM, 22;
      WiprMotCrkAg, 24;
   }
   WmmCem_Lin1PartNrFr01:  0x8, WMM                 ,    7 {
      WMMPartNoNr1, 0;
      WMMPartNoNr2, 8;
      WMMPartNoNr3, 16;
      WMMPartNoNr4, 24;
      WMMPartNoEndSgn1, 32;
      WMMPartNoEndSgn2, 40;
      WMMPartNoEndSgn3, 48;
   }
   WmmCem_Lin1PartNrFr02: 0x19, WMM                 ,    8 {
      WMMPartNo10Nr1, 0;
      WMMPartNo10Nr2, 8;
      WMMPartNo10Nr3, 16;
      WMMPartNo10Nr4, 24;
      WMMPartNo10Nr5, 32;
      WMMPartNo10EndSgn1, 40;
      WMMPartNo10EndSgn2, 48;
      WMMPartNo10EndSgn3, 56;
   }
   WmmCem_Lin1SerNrFr01: 0x28, WMM                 ,    7 {
      WMMSerNoNr1, 0;
      WMMSerNoNr2, 8;
      WMMSerNoNr3, 16;
      WMMSerNoNr4, 24;
   }
}
/* ----------DIAGNOSTIC FRAME DEFINITIONS---------- */
Diagnostic_frames {
   MasterReq           : 0x3c {
      MasterReqB0         , 0;
      MasterReqB1         , 8;
      MasterReqB2         , 16;
      MasterReqB3         , 24;
      MasterReqB4         , 32;
      MasterReqB5         , 40;
      MasterReqB6         , 48;
      MasterReqB7         , 56;
   }
   SlaveResp           : 0x3d {
      SlaveRespB0         , 0;
      SlaveRespB1         , 8;
      SlaveRespB2         , 16;
      SlaveRespB3         , 24;
      SlaveRespB4         , 32;
      SlaveRespB5         , 40;
      SlaveRespB6         , 48;
      SlaveRespB7         , 56;
   }
}
/* ----------NODE ATTRIBUTE DEFINITIONS---------- */
Node_attributes {
   IRMM {
      LIN_protocol               = "2.1";                    /* Node protocol version */
      configured_NAD             = 0x13;                     /* configured NAD of node (1-125) */
      initial_NAD                = 0x13;                     /* initial NAD of node (1-125) */
      product_id                 =    0x0,    0x0,    0x0;   /* Product id */
      response_error             = ErrRespIRMM;     /* Response error signal */
      configurable_frames {
         CemCem_Lin1Fr01;
         CemCem_Lin1Fr07;
         CemCem_Lin1Fr08;
         CemCem_Lin1Fr09;
         IrmmCem_Lin1Fr01;
         IrmmCem_Lin1PartNrFr01;
         IrmmCem_Lin1PartNrFr02;
         IrmmCem_Lin1SerNrFr01;
         CemCem_Lin1Fr02;
         IrmmCem_Lin1Fr02;
         CemCem_Lin1Fr04;
      }
   }
   RLSM {
      LIN_protocol               = "2.1";                    /* Node protocol version */
      configured_NAD             = 0x7b;                     /* configured NAD of node (1-125) */
      initial_NAD                = 0x0;                      /* initial NAD of node (1-125) */
      product_id                 =    0x7,    0x0,    0x0;   /* Product id */
      response_error             = ErrRespRLSM;     /* Response error signal */
      configurable_frames {
         RlsmCem_Lin1Fr01;
         RlsmCem_Lin1PartNrFr01;
         RlsmCem_Lin1PartNrFr02;
         RlsmCem_SerNrLin1Fr01;
         CemCem_Lin1Fr01;
         CemCem_Lin1Fr02;
         CemCem_Lin1Fr03;
         CemCem_Lin1Fr04;
         CemCem_Lin1Fr05;
         CemCem_Lin1Fr06;
         RlsmCem_Lin1Fr02;
         RlsmCem_Lin1Fr03;
         RlsmCem_Lin1Fr04;
      }
   }
   WMM {
      LIN_protocol               = "2.1";                    /* Node protocol version */
      configured_NAD             = 0x12;                     /* configured NAD of node (1-125) */
      initial_NAD                = 0x12;                     /* initial NAD of node (1-125) */
      product_id                 =    0x7,    0x0,    0x0;   /* Product id */
      response_error             = ErrRespWMM;      /* Response error signal */
      configurable_frames {
         WmmCem_Lin1Fr01;
         WmmCem_Lin1PartNrFr01;
         WmmCem_Lin1PartNrFr02;
         WmmCem_Lin1SerNrFr01;
         CemCem_Lin1Fr01;
         RlsmCem_Lin1Fr01;
      }
   }
}
/* ----------SCHEDULE TABLE DEFINITIONS---------- */
Schedule_tables {
   Cem_Lin1DiagRequestSchedule {
      MasterReq                           delay 15.0 ms;
   }
   Cem_Lin1DiagResponseSchedule {
      SlaveResp                           delay 15.0 ms;
   }
   Cem_Lin1Schedule01 {
      CemCem_Lin1Fr01            delay 10.0 ms;
      RlsmCem_Lin1Fr01           delay 15.0 ms;
      CemCem_Lin1Fr02            delay 10.0 ms;
      CemCem_Lin1Fr03            delay 15.0 ms;
      CemCem_Lin1Fr04            delay 10.0 ms;
      CemCem_Lin1Fr05            delay 10.0 ms;
      RlsmCem_Lin1Fr04           delay 15.0 ms;
      WmmCem_Lin1Fr01            delay 10.0 ms;
      CemCem_Lin1Fr06            delay 5.0 ms;
      CemCem_Lin1Fr01            delay 10.0 ms;
      RlsmCem_Lin1Fr01           delay 15.0 ms;
      IrmmCem_Lin1Fr01           delay 10.0 ms;
      IrmmCem_Lin1Fr02           delay 15.0 ms;
      RlsmCem_Lin1Fr02           delay 15.0 ms;
      CemCem_Lin1Fr02            delay 10.0 ms;
      RlsmCem_Lin1Fr04           delay 15.0 ms;
      RlsmCem_Lin1Fr03           delay 15.0 ms;
      WmmCem_Lin1Fr01            delay 10.0 ms;
      CemCem_Lin1Fr06            delay 5.0 ms;
      CemCem_Lin1Fr07            delay 15.0 ms;
      CemCem_Lin1Fr08            delay 15.0 ms;
      CemCem_Lin1Fr09            delay 15.0 ms;
      RlsmCem_Lin1Fr04           delay 15.0 ms;
   }
   Cem_Lin1ScheduleSerNrPartNr {
      IrmmCem_Lin1PartNrFr02     delay 15.0 ms;
      IrmmCem_Lin1PartNrFr01     delay 10.0 ms;
      RlsmCem_Lin1PartNrFr01     delay 10.0 ms;
      WmmCem_Lin1PartNrFr01      delay 10.0 ms;
      WmmCem_Lin1PartNrFr02      delay 15.0 ms;
      WmmCem_Lin1SerNrFr01       delay 10.0 ms;
      RlsmCem_Lin1PartNrFr02     delay 15.0 ms;
      RlsmCem_SerNrLin1Fr01      delay 10.0 ms;
      IrmmCem_Lin1SerNrFr01      delay 10.0 ms;
   }
}
/* ----------SIGNAL ENCODDING DEFINITIONS---------- */
Signal_encoding_types {
   AmntSnsr {
      logical_value,0,"AmntSnsr_Amnt";
      logical_value,14,"AmntSnsr_InitValue";
      logical_value,15,"AmntSnsr_Error";
   }
   Boolean {
      logical_value,0,"Boolean_FALSE";
      logical_value,1,"Boolean_TRUE";
   }
   CarModSts1 {
      logical_value,0,"CarModSts1_CarModNorm";
      logical_value,1,"CarModSts1_CarModTrnsp";
      logical_value,2,"CarModSts1_CarModFcy";
      logical_value,3,"CarModSts1_CarModCrash";
      logical_value,5,"CarModSts1_CarModDyno";
   }
   Err1 {
      logical_value,0,"Err1_NoErr";
      logical_value,1,"Err1_Err";
   }
   FailrNoFailr1 {
      logical_value,0,"FailrNoFailr1_NoFailr";
      logical_value,1,"FailrNoFailr1_Failr";
   }
   Flg1 {
      logical_value,0,"Flg1_Rst";
      logical_value,1,"Flg1_Set";
   }
   FltEgyCns1 {
      logical_value,0,"FltEgyCns1_NoFlt";
      logical_value,1,"FltEgyCns1_Flt";
   }
   FltStsSlaveBasc {
      logical_value,0,"FltStsSlaveBasc_FltStsTestPassd";
      logical_value,1,"FltStsSlaveBasc_FltStsTestFaild";
   }
   GenQf1 {
      logical_value,0,"GenQf1_UndefindDataAccur";
      logical_value,1,"GenQf1_TmpUndefdData";
      logical_value,2,"GenQf1_DataAccurNotWithinSpcn";
      logical_value,3,"GenQf1_AccurData";
   }
   IdleEnaDis {
      logical_value,0,"IdleEnaDis_Idle";
      logical_value,1,"IdleEnaDis_Ena";
      logical_value,2,"IdleEnaDis_Dis";
   }
   IntrlMirrDispSysSts {
      logical_value,0,"IntrlMirrDispSysSts_Off";
      logical_value,1,"IntrlMirrDispSysSts_Standby";
      logical_value,2,"IntrlMirrDispSysSts_Active";
      logical_value,3,"IntrlMirrDispSysSts_Failure";
      logical_value,4,"IntrlMirrDispSysSts_CheckSelf";
      logical_value,5,"IntrlMirrDispSysSts_Reserved";
   }
   IntrMirrDimSnvtyTyp {
      logical_value,0,"IntrMirrDimSnvtyTyp_Normal";
      logical_value,1,"IntrMirrDimSnvtyTyp_Dark";
      logical_value,2,"IntrMirrDimSnvtyTyp_Light";
      logical_value,3,"IntrMirrDimSnvtyTyp_Inhibit";
   }
   LiIllmn {
      physical_value,0,65535,1.0,0.0,"Unitless";
   }
   Liner32 {
      physical_value,0,511,1.0,0.0,"Unitless";
   }
   LiOperMod {
      logical_value,0,"LiOperMod_Night";
      logical_value,1,"LiOperMod_Day";
      logical_value,2,"LiOperMod_Twli";
      logical_value,3,"LiOperMod_Tnl";
   }
   MvBattSwVersAct {
      physical_value,0,255,1.0,0.0,"Unitless";
   }
   Nr2 {
      physical_value,0,255,1.0,0.0,"Unitless";
   }
   Nr5 {
      physical_value,0,7,1.0,0.0,"Unitless";
   }
   OnOff1 {
      logical_value,0,"OnOff1_Off";
      logical_value,1,"OnOff1_On";
   }
   OnOffCrit1 {
      logical_value,0,"OnOffCrit1_NotVld1";
      logical_value,1,"OnOffCrit1_Off";
      logical_value,2,"OnOffCrit1_On";
      logical_value,3,"OnOffCrit1_NotVld2";
   }
   OutdBriSts {
      logical_value,0,"OutdBriSts_Ukwn";
      logical_value,1,"OutdBriSts_Night";
      logical_value,2,"OutdBriSts_Day";
      logical_value,3,"OutdBriSts_Invld";
   }
   ParChks1 {
      logical_value,0,"ParChks1_Unevennrof1";
      logical_value,1,"ParChks1_Evennrof1";
   }
   Perc2 {
      physical_value,0,200,0.5,0.0,"%";
   }
   Perc5 {
      physical_value,0,255,0.4,0.0,"%";
   }
   RainSnsrThd {
      physical_value,0,15,5.0,-40.0,"%";
   }
   RiLeTyp {
      logical_value,0,"RiLeTyp_Right";
      logical_value,1,"RiLeTyp_Left";
   }
   Sunload2D {
      physical_value,0,255,5.0,0.0,"W/m2";
   }
   TByCmptmtSnsrRelHum {
      physical_value,0,2047,0.1,-40.0,"degC";
   }
   TwliBriRaw1 {
      physical_value,0,16383,1.0,0.0,"Unitless";
   }
   UInt4 {
      physical_value,0,15,1.0,0.0,"Unitless";
   }
   UInt6 {
      physical_value,0,63,1.0,0.0,"Unitless";
   }
   UInt8 {
      physical_value,0,255,1.0,0.0,"Unitless";
   }
   UsgModSts1 {
      logical_value,0,"UsgModSts1_UsgModAbdnd";
      logical_value,1,"UsgModSts1_UsgModInActv";
      logical_value,2,"UsgModSts1_UsgModCnvinc";
      logical_value,11,"UsgModSts1_UsgModActv";
      logical_value,13,"UsgModSts1_UsgModDrvg";
   }
   WipAg {
      physical_value,0,255,1.0,0.0,"Unitless";
   }
   WipAgOffs {
      physical_value,0,15,1.0,0.0,"Deg";
   }
   WipgAutFrntMod {
      logical_value,0,"WipgAutFrntMod_Off";
      logical_value,1,"WipgAutFrntMod_ImdtMod";
      logical_value,2,"WipgAutFrntMod_IntlMod";
      logical_value,3,"WipgAutFrntMod_ContnsMod";
   }
   WipgSpd2 {
      logical_value,0,"WipgSpd2_WipgSpd0Rpm";
      logical_value,1,"WipgSpd2_WipgSpd40Rpm";
      logical_value,2,"WipgSpd2_WipgSpd43Rpm";
      logical_value,3,"WipgSpd2_WipgSpd46Rpm";
      logical_value,4,"WipgSpd2_WipgSpd50Rpm";
      logical_value,5,"WipgSpd2_WipgSpd54Rpm";
      logical_value,6,"WipgSpd2_WipgSpd57Rpm";
      logical_value,7,"WipgSpd2_WipgSpd60Rpm";
   }
   WipgSpdIntlFromHmi {
      logical_value,0,"WipgSpdIntlFromHmi_Posn0";
      logical_value,1,"WipgSpdIntlFromHmi_Posn1";
      logical_value,2,"WipgSpdIntlFromHmi_Posn2";
      logical_value,3,"WipgSpdIntlFromHmi_Posn3";
      logical_value,4,"WipgSpdIntlFromHmi_Posn4";
      logical_value,5,"WipgSpdIntlFromHmi_Posn5";
      logical_value,6,"WipgSpdIntlFromHmi_Posn6";
      logical_value,7,"WipgSpdIntlFromHmi_Posn7";
   }
}
/* ----------SIGNAL REPRESENTATION DEFINITIONS---------- */
Signal_representation {
   AmntSnsr   :RainfallAmnt;
   Boolean    :EnaOfflineMonitor, IntrMirrRespResdBoolean, RainDetected;
   CarModSts1 :VehModMngtGlbSafe1CarModSts1;
   Err1       :RelHumSnsrErr, SolarSnsrErr;
   FailrNoFailr1:IntrMirrRespIntrMirrIntFailr;
   Flg1       :IntrMirrCmdIntrMirrDiagcRst;
   FltEgyCns1 :VehModMngtGlbSafe1FltEgyCnsWdSts;
   FltStsSlaveBasc:HudSnsrErrSnsrErr;
   GenQf1     :TwliBriRawQf;
   IdleEnaDis :HomeLinkEna;
   IntrlMirrDispSysSts:IntrlMirrDispSysSts;
   IntrMirrDimSnvtyTyp:IntrMirrCmdIntrMirrDimSnvty;
   LiIllmn    :AMBStsAmbLiIllmn, FWStsAmbLiIllmn;
   Liner32    :AmbIllmnFwdStsAmblillmn1;
   LiOperMod  :LiOprnMod, LiOprnMod1;
   MvBattSwVersAct:AmbIllmnFwdStsAmblillmn2;
   Nr2        :VehCfgPrmBlkIDBytePosn1, VehCfgPrmCCPBytePosn2, VehCfgPrmCCPBytePosn3, VehCfgPrmCCPBytePosn4, VehCfgPrmCCPBytePosn5, VehCfgPrmCCPBytePosn6, VehCfgPrmCCPBytePosn7, VehCfgPrmCCPBytePosn8, VehCfgPrmExtBlkIDBytePosn1, VehCfgPrmExtCCPBytePosn2, VehCfgPrmExtCCPBytePosn3, VehCfgPrmExtCCPBytePosn4, VehCfgPrmExtCCPBytePosn5, VehCfgPrmExtCCPBytePosn6, VehCfgPrmExtCCPBytePosn7, VehCfgPrmExtCCPBytePosn8;
   Nr5        :VehModMngtGlbSafe1CarModSubtypWdCarModSubtyp;
   OnOff1     :IntrMirrCmdIntrMirrAsyFanCmpMag, IntrMirrCmdIntrMirrEna, IntrMirrCmdIntrMirrInhbDim, IntrMirrCmdIntrMirrWindHeatrCmpMag, RainLi, RainSensActvn, RainSnsrDiagcRainSnsrHiTDetd, RainSnsrDiagcRainSnsrHiVoltDetd, RainSnsrErrCalErr, RainSnsrErrCalErrActv, RainSnsrErrRainDetnErr, RainSnsrErrRainDetnErrActv, ReAdaptReq, StreamingMirrEnable, StreamingMirrSwt, WiprActv, WiprActvFromWMM, WiprInPrkgPosnLo, WiprInPrkgPosnLoFromWMM, WiprInWipgAr, WiprInWipgArFromWMM, WiprMotFrntLvrCmdSafeLvrInIntlPosn, WiprMotFrntLvrCmdSafeLvrInSnglStrokePos, WiprPosnForSrvReq, WshngCycActv, WshngCycActvFromWMM;
   OnOffCrit1 :WipgPwrActvnSafeWipgPwrAcsyModSafe, WipgPwrActvnSafeWipgPwrDrvgModSafe, WiprMotDiagcWiprMotHiVoltDetd, WiprMotDiagcWiprMotLoVoltDetd, WiprMotDiagcWiprMotOvldDetd, WiprMotErrSafe, WiprMotErrSafe2, WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe, WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe, WshrLvrPosnSafe;
   OutdBriSts :OutdBriSts;
   ParChks1   :HudSnsrErrParChk;
   Perc2      :RelHumSnsrRelHum;
   Perc5      :IntrMirrRespIntrMirrDimPerc;
   RainSnsrThd:RainSnsrLiThd;
   RiLeTyp    :IntrMirrCmdDrvrSide;
   Sunload2D  :SolarSnsrLeValue, SolarSnsrRiValue;
   TByCmptmtSnsrRelHum:CmptFrntWindDewT, CmptFrntWindT;
   TwliBriRaw1:TwliBriRawTwliBriRaw;
   UInt4      :AmbIllmnFwdStsCntr, AMBStsCntr, FrqCfg, FWStsCntr, OutdBriCntr, RainSnsrSnvtyForUsrSnvty0, RainSnsrSnvtyForUsrSnvty1, RainSnsrSnvtyForUsrSnvty2, RainSnsrSnvtyForUsrSnvty3, RainSnsrSnvtyForUsrSnvty4, RainSnsrSnvtyForUsrSnvty5, RainSnsrSnvtyForUsrSnvty6, StreamingMirrBri, StreamingMirrBriAdjmt, StreamingMirrImg, StreamingMirrImgAdjmt, StreamingMirrModeSts, StreamingMirrModeSwt, StreamingMirrPosn, StreamingMirrPosnAdjmt, VehModMngtGlbSafe1Cntr, VehModMngtGlbSafe1EgyLvlElecMai, VehModMngtGlbSafe1EgyLvlElecSubtyp, VehModMngtGlbSafe1PwrLvlElecMai, VehModMngtGlbSafe1PwrLvlElecSubtyp, VehTyp;
   UInt6      :IntrMirrRespResdUInt6;
   UInt8      :AmbIllmnFwdStsChks, AMBStsChks, AmbTForVisy, FWStsChks, OutdBriChks, PartNo10IRMMEndSgn1, PartNo10IRMMEndSgn2, PartNo10IRMMEndSgn3, PartNo10IRMMNr1, PartNo10IRMMNr2, PartNo10IRMMNr3, PartNo10IRMMNr4, PartNo10IRMMNr5, PartNoIRMMEndSgn1, PartNoIRMMEndSgn2, PartNoIRMMEndSgn3, PartNoIRMMNr1, PartNoIRMMNr2, PartNoIRMMNr3, PartNoIRMMNr4, RLSMPartNo10EndSgn1, RLSMPartNo10EndSgn2, RLSMPartNo10EndSgn3, RLSMPartNo10Nr1, RLSMPartNo10Nr2, RLSMPartNo10Nr3, RLSMPartNo10Nr4, RLSMPartNo10Nr5, RLSMPartNoEndSgn1, RLSMPartNoEndSgn2, RLSMPartNoEndSgn3, RLSMPartNoNr1, RLSMPartNoNr2, RLSMPartNoNr3, RLSMPartNoNr4, RLSMSerNoNr1, RLSMSerNoNr2, RLSMSerNoNr3, RLSMSerNoNr4, SerNoIRMMNr1, SerNoIRMMNr2, SerNoIRMMNr3, SerNoIRMMNr4, VehModMngtGlbSafe1Chks, VehSpdForWipg, WindCorrnValAmb, WindCorrnValFrnt, WindCorrnValHud, WMMPartNo10EndSgn1, WMMPartNo10EndSgn2, WMMPartNo10EndSgn3, WMMPartNo10Nr1, WMMPartNo10Nr2, WMMPartNo10Nr3, WMMPartNo10Nr4, WMMPartNo10Nr5, WMMPartNoEndSgn1, WMMPartNoEndSgn2, WMMPartNoEndSgn3, WMMPartNoNr1, WMMPartNoNr2, WMMPartNoNr3, WMMPartNoNr4, WMMSerNoNr1, WMMSerNoNr2, WMMSerNoNr3, WMMSerNoNr4;
   UsgModSts1 :VehModMngtGlbSafe1UsgModSts;
   WipAg      :WiprMotCrkAg;
   WipAgOffs  :WiprMotFrntOffsAg;
   WipgAutFrntMod:WipgAutFrntMod;
   WipgSpd2   :AutWinWipgCmd;
   WipgSpdIntlFromHmi:WiprMotIntlCmd;
}

