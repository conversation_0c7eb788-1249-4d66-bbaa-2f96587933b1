/*
 * @file     mpq3367_pwm.h
 * @brief     
 * <AUTHOR>
 * @date      2022-03-1 12:59:55 created
 * $Id:mpq3367_pwm.h 1.1 $
 */
 
#ifndef MPQ3367_PWM_H_
#define MPQ3367_PWM_H_
#include "config.h"
#include "gpio.h"
#define MPQ3367_PG_config()		CONFIG_PIN_AS_GPIO(PTF,PTF3,INPUT);ENABLE_INPUT(PTF,PTF3)
#define MPQ3367_PG_get()		(READ_INPUT(PTF, PTF3)? 1 : 0)
#define MPQ3367_EN_config()		CONFIG_PIN_AS_GPIO(PTF,PTF2,OUTPUT)
#define MPQ3367_EN_on()			OUTPUT_SET(PTF,PTF2)
#define MPQ3367_EN_off()		OUTPUT_CLEAR(PTF,PTF2)

void  mpq3367_enable(void);
void  mpq3367_disable(void);
void  mpq3367_pwm_init(void);
void  mpq3367_pwm_set(uint8_t frequency,uint8_t high,uint8_t duration);
void mpq3367_init(void);

#endif
