/**
* @file		freezed_frame.h
* <AUTHOR>
* @date		Tue Jun 4 16:34:44 2019 +0800
* @version	master
*/

#ifndef FREEZED_FRAME_H_
#define FREEZED_FRAME_H_

#include "stdint.h"
/* for debug out Frozen Data*/
//#define DBG_FRZ_RAM		1

/*
frozen screen data order 
0: orignal
1:invert X
2:invert Y
3:invert X and Y
*/
#define FRZRAM_ORD 		2

#define FRZRAM_HBLOCK_CNT       16
#define FRZRAM_VBLOCK_CNT       16
#define FRZRAM_BLOCK_CNT        FRZRAM_HBLOCK_CNT*FRZRAM_VBLOCK_CNT

// the input frame counted once then generates an interrupt by Time1
extern uint8_t VSi_Signal;
extern uint8_t frz_need_trans;
extern uint8_t *PRE_FRZ_RAM_ptr;

void freezed_init(void);
void frozen_task(void);

#endif  /* FREEZED_FRAME_H_ */

