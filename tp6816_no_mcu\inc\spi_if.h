/*
 * @file      spi_if.h
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:50 created
 * $Id: spi_if.h 1.1.develop.1 $
 */

#ifndef SPI_IF_H_
#define SPI_IF_H_

#include "tp6823.h"

/* NOR Command Set */
#define NOR_WREN_CMD		0x06
#define NOR_WRDI_CMD		0x04
#define NOR_RDSR_CMD		0x05
#define NOR_WRSR_CMD		0x01
#define NOR_NREAD_CMD		0x03
#define NOR_FREAD_CMD		0x0B
#define NOR_FREAD_DTR_CMD	0x0D
#define NOR_QFREAD_CMD		0xEB
#define NOR_QFREAD_DTR_CMD	0xED
#define NOR_PP_CMD			0x02
#define NOR_QPP_CMD			0x38
#define NOR_SE_CMD			0x20
#define NOR_BE_CMD			0xD8
#define NOR_CE_CMD			0xC7	// or 0x60
#define NOR_RDID_CMD		0x9F
#define NOR_QPI_EN_CMD	    0x35
#define NOR_QPI_EX_CMD	    0xF5
#define NOR_4B_EN_CMD	    0xB7
#define NOR_4B_EX_CMD	    0xE9
#define NOR_4B2_EX_CMD	    0x29
// Winbond's Specific Command
#define NOR_RDSR2_CMD       0x35	// must change P0_92h to other value from default 0x35
#define NOR_WRSR2_CMD		0x31    // new W25Q64J
#define NOR_RDSR2_CMD2      0x3F
#define NOR_WRSR2_CMD2      0x3E
#define NOR_QPI2_EN_CMD	    0x38
#define NOR_QPI2_EX_CMD	    0xFF
#define NOR_QPI3_EX_CMD	    0xF5

#define NOR_SECTOR_SIZE		(4 * 1024)
#define NOR_BLOCK_SIZE		(64 * 1024)

#define SPI_IF_INSTR_REG    0x80
#define SPI_IF_WRD1_REG     0x81
#define SPI_IF_WRD2_REG     0x82
#define SPI_IF_WRD3_REG     0x83
#define SPI_IF_WRD4_REG     0x84
#define SPI_IF_WRD5_REG     0x85
#define SPI_IF_RDD1_REG     0x81
#define SPI_IF_RDD2_REG     0x82
#define SPI_IF_RDD3_REG     0x83
#define SPI_IF_RDD4_REG     0x84
#define SPI_IF_RDD5_REG     0x85
#define SPI_IF_SUM1_REG     0x84
#define SPI_IF_SUM2_REG     0x85
#define SPI_IF_IDELAY_REG   0x86
    #define SPI_IF_IDELAY_MAX   31
    #define SPI_IF_IDELAY_MASK  BF_MASK(0, 5)
#define SPI_IF_MODE_REG     0x87
    #define SPI_IF_1I1O         0
    #define SPI_IF_1I4O         1
    #define SPI_IF_4I4O         2
    #define SPI_IF_QPI4I4O      3
    #define SPI_IF_MODE_MASK    BF_MASK(0, 2)
    #define SPI_IF_DTR_EN       BIT(4)
    #define SPI_IF_WREN         BIT(5)
    #define SPI_IF_RDSR         BIT(6)
    #define SPI_IF_PTR_RST      BIT(7)
#define SPI_IF_RD_CNT_REG   0x88    
    #define SPI_IF_RD_CNT_MAX   5
    #define SPI_IF_RD_BUFFER    6
    #define QPI_IF_4I4O         3
    #define SPI_IF_4I4O         2
    #define SPI_IF_1I4O         1
    #define SPI_IF_1I1O         0
#define SPI_IF_CTRL_REG     0x89
    #define SPI_IF_NON_XMODE    0 // & 1
    #define SPI_IF_ENTER_XMODE  2
    #define SPI_IF_EXIT_XMODE   3
    #define SPI_IF_XMODE_MASK   BF_MASK(0, 2)
    #define SPI_IF_BUFF_WR      BIT(2)
    #define SPI_IF_WR_CNT(x)    (x << 3)
    #define SPI_IF_WR_NCT_MAX   5
    #define SPI_IF_INSTR_YES    BIT(6)
    #define SPI_IF_ISSUE_BIT    BIT(7)
#define SPI_IF_ODELAY_REG   0x8A    
    #define SPI_IF_ODELAY_MAX   15
    #define SPI_IF_ODELAY_MASK  BF_MASK(0, 4)
    #define SPI_IF_OBIAS_BIT    BIT(4)
    #define SPI_IF_IBIAS_MAX    7
    #define SPI_IF_IBIAS_MASK   BF_MASK(5, 3)
#define SPI_FETCH_IDELAY_REG        0x8C
#define SPI_FETCH_INSTR_REG         0x8D
#define SPI_FETCH_DUMMY_REG         0x8E
    #define SPI_FETCH_SOFTS	        (1 << 3)
#define SPI_FETCH_QUAD_MODE_REG     0x8F
    #define SPI_FETCH_DTR_EN        (1 << 6)
#define SPI_IF_QPI_4B_STA_REG       0x91
    #define SPI_QPI_STA_BIT	        (1 << 7)
    #define SPI_4B_STA_BIT	        (1 << 3)
#define SPI_IF_INSTR_QPI_ENTER_REG  0x92
#define SPI_IF_INSTR_QPI_EXIT_REG   0x93
#define SPI_IF_INSTR_4B_ENTER_REG   0x94
#define SPI_IF_INSTR_4B_EXIT_REG    0x95
#define SPI_IF_INSTR_BRWR_REG       0x96
#define SPI_IF_RDBACK_SEL_REG       0x97
    #define TRIGE_SPI_RESET     (1 << 7)
    #define TRIGE_EXIT_XIP      (1 << 6)
    #define SPI_IF_RDBK_SEL     0
    #define SPI_IF_CFGWR_SEL    1
    #define SPI_IF_SUM_SEL      2
#define SPI_IF_XFERCNT_L_REG        0x98
#define SPI_IF_XFERCNT_M_REG        0x99
#define SPI_IF_XFERCNT_H_REG        0x9A
#define SPI_IF_DATA_PORT_REG        0x9B
#define SPI_IF_XMODE_CODE_REG       0x9C
    #define SPI_IF_XCODE(x)         (x << 4)

// 0: Done; 1: Busy
#define SPI_ISSUE_DONE_         (read_p0(SPI_IF_CTRL_REG) & SPI_IF_ISSUE_BIT)

#define SPI_TRIG_EXIT_XIP()     write_p0(SPI_IF_RDBACK_SEL_REG, TRIGE_EXIT_XIP)

#define SPI_GET_CTRL            read_p0(SPI_IF_CTRL_REG)
#define SPI_SET_CTRL(ctrl)      write_p0(SPI_IF_CTRL_REG, ctrl)
#define SPI_SET_CMD(cmd)        write_p0(SPI_IF_INSTR_REG, cmd)
#define SPI_SET_MODE(mode)      write_p0(SPI_IF_MODE_REG, mode)
#define SPI_ISSUE_CMD(issue)    write_p0(SPI_IF_CTRL_REG, (SPI_GET_CTRL | issue))

#define SPI_SET_WRD1(wr)        write_p0(SPI_IF_WRD1_REG, wr)
#define SPI_SET_WRD2(wr)        write_p0(SPI_IF_WRD2_REG, wr)
#define SPI_SET_WRD3(wr)        write_p0(SPI_IF_WRD3_REG, wr)
#define SPI_SET_WRD4(wr)        write_p0(SPI_IF_WRD4_REG, wr)
#define SPI_SET_WRD5(wr)        write_p0(SPI_IF_WRD5_REG, wr)
#define SPI_SET_WRD(n, wr)      write_p0(SPI_IF_WRD1_REG + n, wr)
#define SPI_GET_RDD1            read_p0(SPI_IF_RDD1_REG)
#define SPI_GET_RDD2            read_p0(SPI_IF_RDD2_REG)
#define SPI_GET_RDD3            read_p0(SPI_IF_RDD3_REG)
#define SPI_GET_RDD4            read_p0(SPI_IF_RDD4_REG)
#define SPI_GET_RDD5            read_p0(SPI_IF_RDD5_REG)
#define SPI_GET_RDD(n)          read_p0(SPI_IF_RDD1_REG + n)
#define SPI_SET_RDD_CNT(cnt)    write_p0(SPI_IF_RD_CNT_REG, cnt)

#define SPI_SET_FETCH(n, wr)    write_p0((SPI_FETCH_INSTR_REG + n), wr)

#define SPI_SET_NORADDR(addr)  \
    do { \
        write_p0(SPI_IF_WRD1_REG, ((addr >> 16) & 0xff)); \
        write_p0(SPI_IF_WRD2_REG, ((addr >> 8) & 0xff)); \
        write_p0(SPI_IF_WRD3_REG, (addr & 0xff)); \
    } while(0);
#define SPI_SET_NORADDR_4B(addr)  \
    do { \
        write_p0(SPI_IF_WRD1_REG, ((addr >> 24) & 0xff)); \
        write_p0(SPI_IF_WRD2_REG, ((addr >> 16) & 0xff)); \
        write_p0(SPI_IF_WRD3_REG, ((addr >> 8) & 0xff)); \
        write_p0(SPI_IF_WRD4_REG, (addr & 0xff)); \
    } while(0);
#define SPI_SET_PP_CNT(cnt)  \
    do { \
        write_p0(SPI_IF_XFERCNT_L_REG, (cnt & 0xff)); \
        write_p0(SPI_IF_XFERCNT_M_REG, ((cnt & 0x100)? 1 : 0)); \
        write_p0(SPI_IF_XFERCNT_H_REG, 0); \
    } while(0);
#define SPI_PUT_DPORT(dat)  write_p0(SPI_IF_DATA_PORT_REG, dat)

#define SPI_SET_INSTR_QPI(enter, exit)  \
    do { \
        write_p0(SPI_IF_INSTR_QPI_ENTER_REG, enter); \
        write_p0(SPI_IF_INSTR_QPI_EXIT_REG, exit); \
    } while(0);
#define SPI_SET_INSTR_4BA(enter, exit)  \
    do { \
        write_p0(SPI_IF_INSTR_4B_ENTER_REG, enter); \
        write_p0(SPI_IF_INSTR_4B_EXIT_REG, exit); \
    } while(0);
#define SPI_SET_INSTR_BRWR(instr)   write_p0(SPI_IF_INSTR_BRWR_REG, instr);

#define SPI_QPI_ENTER()  \
    do { \
        spi_cmd_wr(read_p0(SPI_IF_INSTR_QPI_ENTER_REG), \
            0, 0); \
    } while(0);
#define SPI_QPI_EXIT()  \
    do { \
        spi_cmd_wr(read_p0(SPI_IF_INSTR_QPI_EXIT_REG), \
            0, 0); \
    } while(0);
#define SPI_4B_ENTER()  \
    do { \
        spi_cmd_wr(read_p0(SPI_IF_INSTR_4B_ENTER_REG), \
            0, 0); \
    } while(0);
#define SPI_4B_EXIT()  \
    do { \
        spi_cmd_wr(read_p0(SPI_IF_INSTR_4B_EXIT_REG), \
            0, 0); \
    } while(0);
#define SPI_IN_QPI()    (read_p0(SPI_IF_QPI_4B_STA_REG) & SPI_QPI_STA_BIT)
#define SPI_IN_4B()     (read_p0(SPI_IF_QPI_4B_STA_REG) & SPI_QPI_4B_BIT)

#define SPI_SET_RDBK()        write_p0(SPI_IF_RDBACK_SEL_REG, SPI_IF_RDBK_SEL)
#define SPI_SET_CFGWR()       write_p0(SPI_IF_RDBACK_SEL_REG, SPI_IF_CFGWR_SEL)
#define SPI_SET_SUM()         write_p0(SPI_IF_RDBACK_SEL_REG, SPI_IF_SUM_SEL)

extern bit spi_issue_short(int time);
#define SPI_ISSUE_TIMEOUT       500 //ms
#define SPI_ISSUE()             spi_issue_short(SPI_ISSUE_TIMEOUT)
#define SPI_ISSUE_PREFETCH()    spi_issue_short(0)   // avoid cache miss
#define SPI_ISSUE_CQ()             spi_issue_cq(SPI_ISSUE_TIMEOUT)
#define SPI_ISSUE_CQ_PREFETCH()    spi_issue_short(0)   // avoid cache miss

#define SPI_SET_XFER_CNT(cnt)  \
    do { \
        write_p0(SPI_IF_XFERCNT_L_REG, (cnt & 0xff)); \
        write_p0(SPI_IF_XFERCNT_M_REG, ((cnt >> 8) & 0xff)); \
        write_p0(SPI_IF_XFERCNT_H_REG, ((cnt >> 16) & 0xff)); \
    } while(0);

typedef struct NOR_JEDEC_ID {
    unsigned char mf_id;
    unsigned short dev_id;
} NOR_ID;

enum spi_nor_qer_index {
	SNOR_QER_NONE,
	SNOR_QER_SR2_BIT1_BUGGY,
	SNOR_QER_SR1_BIT6,
	SNOR_QER_SR2_BIT7,
	SNOR_QER_SR2_BIT1_NO_RD,
	SNOR_QER_SR2_BIT1,
	SNOR_QER_SR2_BIT1_WR,
	SNOR_QER_MAX
};
#define SR1_QUAD_EN_BIT6    (1 << 6)
#define SR2_QUAD_EN_BIT7    (1 << 7)
#define SR2_QUAD_EN_BIT1    (1 << 1)

/* important! 5 is maximum counts of read/write unless used SPI buffer(256B) */
extern bit spi_cmd_rd(unsigned char cmd, unsigned char *buf, unsigned char cnt);
extern bit spi_cmd_wr(unsigned char cmd, unsigned char *buf, unsigned char cnt);
extern bit spi_rdid(NOR_ID *id);
extern bit spi_rdsr(unsigned char *sta_regs, unsigned char cnt);
extern bit spi_wrsr(unsigned char *sta_regs, unsigned char cnt);
extern bit spiif_init(void);

/* change dummy cycles function in spi_if_param.c */
extern void spi_default_fetch (void);
extern bit change_dc_MX25L3233F(unsigned char sta_reg);
extern bit change_dc_MX25L12845G(unsigned char sta_reg);
extern bit change_dc_IS25LP256D();
extern bit change_dc_S25FL064L();

/* support SPI Page Program */
extern bit spi_unprotect(void);
extern bit spi_protect(void);
extern bit spi_cmd_wr_cq(unsigned char cmd, unsigned char *buf, unsigned char cnt);
#define spi_wrsr_cq(buf, cnt)  spi_cmd_wr_cq(NOR_WRSR_CMD, buf, cnt)
extern bit spi_sector_erase(unsigned long addr);
extern bit spi_pp(unsigned long addr, unsigned char *buf, unsigned char size);

// spi_if_pram.c
extern bit spi_issue_cq(int time);
extern void spiif_configure_pram(void);

/* Enable
 * QPI Mode */
//#define SPIIF_EN_QPI_MODE

/* Enable
 * 4Byte Address Mode */
//#define SPIIF_EN_4B_ADDR


#endif  /* SPI_IF_H_ */
