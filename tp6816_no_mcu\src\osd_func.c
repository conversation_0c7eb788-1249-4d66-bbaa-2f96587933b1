/*
 * @file      osd_func.c
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:55 created
 * $Id: osd_func.c 1.1.develop.1 $
 */

#include "tp6823.h"
#include "osd_func.h"
#include "cmd_queue.h"
#include "config.h"

unsigned short cur_oram_ptr;

#if !defined(_EX_MCU_VERSION)
void osd_dump_oram(unsigned short ptr, unsigned int size)
{
	unsigned int i = 0;

	DEBUG_LOGI("dump oram %4x:\n", ptr);
	if(size > OSD_RAM_SIZE)
		size = OSD_RAM_SIZE;

	for(i = 0; i < size; i ++) {
		DEBUG_LOGI("%02bx, ", OSD_RAM[(ptr + i)]);
		if(!((i + 1) % 16)) {
			DEBUG_LOGI("\n");
		}
	}
	DEBUG_LOGI("\n");
}
#else
void osd_dump_oram(unsigned short ptr, unsigned int size)
{
	unsigned int i = 0;
	unsigned char buf[4];

	DEBUG_LOGI("dump oram %4x:\n", ptr);
	if(size > OSD_RAM_SIZE)
		size = OSD_RAM_SIZE;
	if(size % 4)
		size += 4;
	size /= 4;
	for(i = 0; i < size; i ++) {
		write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
		write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
		write_p2(OSD_ACCESS_TYPE, 0x40);
#ifdef SUPPORT_I2C_BURST_WR
		read_burst_p2(0xC8, buf, 4);

#else
		buf[0] = read_p2(0xC8);
		buf[1] = read_p2(0xC9);
		buf[2] = read_p2(0xCA);
		buf[3] = read_p2(0xCB);
#endif
		DEBUG_LOGI("%02bx, %02bx, %02bx, %02bx, ", buf[3], buf[2], buf[1], buf[0]);
		if(!((i + 1) % 4)) {
			DEBUG_LOGI("\n");
		}
		ptr += 4;
	}
	DEBUG_LOGI("\n");
}
#endif
void osd_clear_lut (void)
{
	short timeout;

	write_p2(ORAM_MANIPULATE_7_0, 0);
	write_p2(ORAM_MANIPULATE_15_8, 0);
	write_p2(ORAM_MANIPULATE_23_16, 0);
	write_p2(ORAM_MANIPULATE_31_24, 0);
	write_p2(OSD_ORAM_PTR_L, (OSD_MLUT_STA_PTR & 0xff));
	write_p2(OSD_ORAM_PTR_M, (OSD_MLUT_STA_PTR >> 8));
	write_p2(OSD_ACCESS_TYPE, OSD_BYTE_W_4 | OSD_ACCESS_LUT);
	// When BlockFill, unitis 4B/4B/6B for oRAM/LUT/FAtt
	write_p2(OSD_SPI_LOAD_CNT_L, (OSD_TOTAL_LUT_SIZE & 0xff));
	write_p2(OSD_SPI_LOAD_CNT_H, OSD_GO_BLOCK_F | (OSD_TOTAL_LUT_SIZE >> 8));
	timeout = read_cpu_count() + 1000;  // 1s
	while(read_p2(OSD_SPI_LOAD_CNT_H) & OSD_GO_BLOCK_F) {
        // Busy
		if(is_time_outs(timeout)) {
			DEBUG_LOGI("[ERROR] spi_load timeout!\n");
			return;
		}
	}
}

void osd_clear_fatt (void)
{
	short timeout;

	write_p2(ORAM_MANIPULATE_7_0, 0);
	write_p2(ORAM_MANIPULATE_15_8, 0);
	write_p2(ORAM_MANIPULATE_23_16, 0);
	write_p2(ORAM_MANIPULATE_31_24, 0);
	write_p2(OSD_ORAM_PTR_L, (OSD_FATT_START_PTR & 0xff));
	write_p2(OSD_ORAM_PTR_M, (OSD_FATT_START_PTR >> 8));
	write_p2(OSD_ACCESS_TYPE, OSD_BYTE_W_6 | OSD_ACCESS_LUT);
	// When BlockFill, unitis 4B/4B/6B for oRAM/LUT/FAtt
	write_p2(OSD_SPI_LOAD_CNT_L, (OSD_FATT_RAM_SIZE & 0xff));
	write_p2(OSD_SPI_LOAD_CNT_H, OSD_GO_BLOCK_F | (OSD_FATT_RAM_SIZE >> 8));
	timeout = read_cpu_count() + 1000;  // 1s
	while(read_p2(OSD_SPI_LOAD_CNT_H) & OSD_GO_BLOCK_F) {
        // Busy
		if(is_time_outs(timeout)) {
			DEBUG_LOGI("[ERROR] spi_load timeout!\n");
			return;
		}
	}
}

void osd_clear_oram (void)
{
	short timeout;

	write_p2(ORAM_MANIPULATE_7_0, 0);
	write_p2(ORAM_MANIPULATE_15_8, 0);
	write_p2(ORAM_MANIPULATE_23_16, 0);
	write_p2(ORAM_MANIPULATE_31_24, 0);
	write_p2(OSD_ORAM_PTR_L, 0);
	write_p2(OSD_ORAM_PTR_M, 0);
	write_p2(OSD_ACCESS_TYPE, OSD_BYTE_W_4);
	// When BlockFill, unitis 4B/4B/6B for oRAM/LUT/FAtt
	write_p2(OSD_SPI_LOAD_CNT_L, ((OSD_RAM_SIZE / 4) & 0xff));
	write_p2(OSD_SPI_LOAD_CNT_H, OSD_GO_BLOCK_F | ((OSD_RAM_SIZE / 4) >> 8));
	timeout = read_cpu_count() + 1000;  // 1s
	while(read_p2(OSD_SPI_LOAD_CNT_H) & OSD_GO_BLOCK_F) {
        // Busy
		if(is_time_outs(timeout)) {
			DEBUG_LOGI("[ERROR] spi_load timeout!\n");
			return;
		}
	}
}

void osd_init (void)
{
    osd_disable(OSD_CTRL_EN);
	osd_clear_lut();
	osd_clear_fatt();
    osd_clear_oram();

	cur_oram_ptr = 0;
}

void osd_enable (unsigned char en)
{
	//write_p2(OSD_CTRL_REG, read_p2(OSD_CTRL_REG) | en);
	cq_set_trig_mode(CQ_OSD_TRIG);
	CQ_CNT_START();
	CQ_P2_1B_(OSD_CTRL_REG, (read_p2(OSD_CTRL_REG) | en));
	CQ_FINISH_ISSUE();
}

void osd_disable (unsigned char en)
{
	//write_p2(OSD_CTRL_REG, read_p2(OSD_CTRL_REG) & (~en));
	cq_set_trig_mode(CQ_OSD_TRIG);
	CQ_CNT_START();
	CQ_P2_1B_(OSD_CTRL_REG, (read_p2(OSD_CTRL_REG) & (~en)));
	CQ_FINISH_ISSUE();
}

void osd_wr_en (unsigned char en)
{
	write_p2(OSD_CTRL_REG, en);
}

void oram_access_sp1_new(unsigned short ptr, unsigned char val, 
				unsigned char mask, unsigned char type, bit use_cq)
{
	switch((ptr % 4)) {
	case 0:
        write_p2(ORAM_MANIPULATE_7_0, val);
        write_p2(ORAM_GATE_BIT_7_0, mask);
        write_p2(ORAM_GATE_BIT_15_8, 0x00);
        write_p2(ORAM_GATE_BIT_23_16, 0x00);
        write_p2(ORAM_GATE_BIT_31_24, 0x00);
		break;
	case 1:
        write_p2(ORAM_MANIPULATE_15_8, val);
        write_p2(ORAM_GATE_BIT_7_0, 0x00);
        write_p2(ORAM_GATE_BIT_15_8, mask);
        write_p2(ORAM_GATE_BIT_23_16, 0x00);
        write_p2(ORAM_GATE_BIT_31_24, 0x00);
		break;
	case 2:
        write_p2(ORAM_MANIPULATE_23_16, val);
        write_p2(ORAM_GATE_BIT_7_0, 0x00);
        write_p2(ORAM_GATE_BIT_15_8, 0x00);
        write_p2(ORAM_GATE_BIT_23_16, mask);
        write_p2(ORAM_GATE_BIT_31_24, 0x00);
		break;
	case 3:
        write_p2(ORAM_MANIPULATE_31_24, val);
        write_p2(ORAM_GATE_BIT_7_0, 0x00);
        write_p2(ORAM_GATE_BIT_15_8, 0x00);
        write_p2(ORAM_GATE_BIT_23_16, 0x00);
        write_p2(ORAM_GATE_BIT_31_24, mask);
		break;
	}
	write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
	write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
	if (use_cq) {
		cq_set_trig_mode(CQ_OSD_TRIG);
		CQ_CNT_START();
		CQ_P2_1B_(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_1 | OSD_GATE_SPECIAL));
		CQ_FINISH_ISSUE();
	}
	else {
		write_p2(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_1 | OSD_GATE_SPECIAL));
	}
}

void oram_access_sp2_new(unsigned short ptr, unsigned short val, 
				unsigned short mask, unsigned char type, bit use_cq)
{
	switch((ptr % 2)) {
	case 0:
    	write_p2(ORAM_MANIPULATE_7_0, (val & 0xff));
    	write_p2(ORAM_MANIPULATE_15_8, ((val >> 8) & 0xff));
    	write_p2(ORAM_GATE_BIT_7_0, (mask & 0xff));
    	write_p2(ORAM_GATE_BIT_15_8, ((mask >> 8) & 0xff));
        write_p2(ORAM_GATE_BIT_23_16, 0x00);
        write_p2(ORAM_GATE_BIT_31_24, 0x00);
		break;
	case 1:
    	write_p2(ORAM_MANIPULATE_23_16, (val & 0xff));
    	write_p2(ORAM_MANIPULATE_31_24, ((val >> 8) & 0xff));
        write_p2(ORAM_GATE_BIT_7_0, 0x00);
        write_p2(ORAM_GATE_BIT_15_8, 0x00);
    	write_p2(ORAM_GATE_BIT_23_16, (mask & 0xff));
    	write_p2(ORAM_GATE_BIT_31_24, ((mask >> 8) & 0xff));
		break;
	}
	write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
	write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
	if (use_cq) {
		cq_set_trig_mode(CQ_OSD_TRIG);
		CQ_CNT_START();
		CQ_P2_1B_(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_SPECIAL));
		CQ_FINISH_ISSUE();
	}
	else {
		write_p2(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_SPECIAL));
	}
}

void oram_access_sp4_new(unsigned short ptr, unsigned long val,
				unsigned long mask, unsigned char type, bit use_cq)
{
	write_p2(ORAM_MANIPULATE_7_0, (val & 0xff));
	write_p2(ORAM_MANIPULATE_15_8, ((val >> 8) & 0xff));
	write_p2(ORAM_MANIPULATE_23_16, ((val >> 16) & 0xff));
	write_p2(ORAM_MANIPULATE_31_24, ((val >> 24) & 0xff));
	write_p2(ORAM_GATE_BIT_7_0, (mask & 0xff));
	write_p2(ORAM_GATE_BIT_15_8, ((mask >> 8) & 0xff));
	write_p2(ORAM_GATE_BIT_23_16, ((mask >> 16) & 0xff));
	write_p2(ORAM_GATE_BIT_31_24, ((mask >> 24) & 0xff));
	write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
	write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
	if (use_cq) {
		cq_set_trig_mode(CQ_OSD_TRIG);
		CQ_CNT_START();
		CQ_P2_1B_(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_SPECIAL));
		CQ_FINISH_ISSUE();
	}
	else {
		write_p2(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_SPECIAL));
	}
}


void oram_access_cq_start(void)
{
	cq_set_trig_mode(CQ_OSD_TRIG);
	CQ_CNT_START();
}
void oram_access_sp1_cq(uint16_t ptr, uint8_t val, 
				uint8_t mask, uint8_t type)
{
	CQ_CFG_BURST_HEADER_P2(ORAM_MANIPULATE_31_24, 5);
	CQ_PUT_DAT(val);
	CQ_PUT_DAT(0x00);
	CQ_PUT_DAT(0x00);
	CQ_PUT_DAT(0x00);
	CQ_PUT_DAT(mask);
	CQ_CFG_BURST_HEADER_P2(OSD_ORAM_PTR_L, 3);
	CQ_PUT_DAT((ptr & 0xff));
	CQ_PUT_DAT(((ptr >> 8) & 0xff));
	CQ_PUT_DAT((type | OSD_BYTE_W_1 | OSD_GATE_SPECIAL));
}

void oram_access_sp4_cq(unsigned short ptr, unsigned long val,
				unsigned long mask, unsigned char type)
{
	CQ_CFG_BURST_HEADER_P2(ORAM_MANIPULATE_7_0, 8);
	CQ_PUT_DAT((val & 0xff));
	CQ_PUT_DAT(((val >> 8) & 0xff));
	CQ_PUT_DAT(((val >> 16) & 0xff));
	CQ_PUT_DAT(((val >> 24) & 0xff));
	CQ_PUT_DAT((mask & 0xff));
	CQ_PUT_DAT(((mask >> 8) & 0xff));
	CQ_PUT_DAT(((mask >> 16) & 0xff));
	CQ_PUT_DAT(((mask >> 24) & 0xff));
	CQ_CFG_BURST_HEADER_P2(OSD_ORAM_PTR_L, 3);
	CQ_PUT_DAT((ptr & 0xff));
	CQ_PUT_DAT(((ptr >> 8) & 0xff));
	CQ_PUT_DAT((type | OSD_BYTE_W_4 | OSD_GATE_SPECIAL));
}

void oram_access_cq_finish(void)
{
	CQ_FINISH_ISSUE();
}

void oram_access_all(unsigned short ptr, unsigned long val, unsigned char type)
{
    write_p2(ORAM_MANIPULATE_7_0, (val & 0xff));
    write_p2(ORAM_MANIPULATE_15_8, ((val >> 8) & 0xff));
    write_p2(ORAM_MANIPULATE_23_16, ((val >> 16) & 0xff));
    write_p2(ORAM_MANIPULATE_31_24, ((val >> 24) & 0xff));
    write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
    write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
    write_p2(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_SPECIAL));
}

void oram_access_11_0(unsigned short ptr, unsigned short val, unsigned char type)
{
    write_p2(ORAM_MANIPULATE_7_0, (val & 0xff));
    write_p2(ORAM_MANIPULATE_15_8, (val >> 8));
    write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
    write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
    write_p2(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_11_0));
}

void oram_access_23_12(unsigned short ptr, unsigned short val, unsigned char type)
{
    write_p2(ORAM_MANIPULATE_15_8, (val & 0xff));
    write_p2(ORAM_MANIPULATE_23_16, (val >> 8));
    write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
    write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
    write_p2(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_23_12));
}

void oram_access_27_16(unsigned short ptr, unsigned short val, unsigned char type)
{
    write_p2(ORAM_MANIPULATE_23_16, (val & 0xff));
    write_p2(ORAM_MANIPULATE_31_24, (val >> 8));
    write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
    write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
    write_p2(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_27_16));
}

void oram_access_lut(unsigned short ptr, unsigned char lut, unsigned char type)
{
	write_p2(ORAM_MANIPULATE_15_8, lut);
    write_p2(ORAM_MANIPULATE_31_24, lut);
    write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
    write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
    write_p2(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_31_25P15_9));
}

void oram_access_fidx(unsigned short ptr, unsigned short idx, unsigned char type)
{
	write_p2(ORAM_MANIPULATE_7_0, (idx & 0xff));
    write_p2(ORAM_MANIPULATE_15_8, (idx >> 8));
    write_p2(ORAM_MANIPULATE_23_16, (idx & 0xff));
    write_p2(ORAM_MANIPULATE_31_24, (idx >> 8));
    write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
    write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
    write_p2(OSD_ACCESS_TYPE, (type | OSD_BYTE_W_4 | OSD_GATE_31_25P15_9));
}

bit spi_load_type(unsigned short oram_addr, unsigned long nor_addr, 
                unsigned short count, unsigned long addend, unsigned char type)
{
	short timeout;

    write_p2(OSD_ORAM_PTR_L, (oram_addr & 0xff));
    write_p2(OSD_ORAM_PTR_M, ((oram_addr >> 8) & 0xff));
	switch(type) {
	case LOAD_TYPE_LUT:
		/* When load LUT data, this must be DWord aligned (A[1:0] == 2'b00) */
        write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_LUT);
		break;
	case LOAD_TYPE_FATT:
		/* When load FAtt data, this must be Word aligned (A[0] == 1'b0) */
        write_p2(OSD_ACCESS_TYPE, OSD_BYTE_W_6 | OSD_ACCESS_LUT);
		break;
	case LOAD_TYPE_RLg:
	case LOAD_TYPE_Row:
	case LOAD_TYPE_RLs:
	case LOAD_TYPE_ORAM:
	default:
		/* No Byte/Word/DW alignment when move data from SPI to oRAM */
        write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_ORAM);
		break;
	}
    write_p2(OSD_BASS_PTR_ADDEND_7_0, (addend & 0xff));
    write_p2(OSD_BASS_PTR_ADDEND_15_8, ((addend >> 8) & 0xff));
    write_p2(OSD_BASS_PTR_ADDEND_23_16, ((addend >> 16) & 0xff));
	if(addend > 0) {
		/* Addend_Type[2:0]
		 *  These 3 bits will be reset whenever ORAM_Ptr/FontAtt_Ptr
		 *  being programmed
		 *  */
        write_p2(OSD_BASS_PTR_ADDEND_26_24, ((addend >> 24) & 0x03));
		switch(type) {
		case LOAD_TYPE_FATT:
            write_p2(OSD_BASS_PTR_ADDEND_26_24, 
            (read_p2(OSD_BASS_PTR_ADDEND_26_24) | ADDEND_TYPE_FATT));
			break;
		case LOAD_TYPE_Row:
            write_p2(OSD_BASS_PTR_ADDEND_26_24, 
            (read_p2(OSD_BASS_PTR_ADDEND_26_24) | ADDEND_TYPE_ROWATT));
			break;
		case LOAD_TYPE_RLg:
            write_p2(OSD_BASS_PTR_ADDEND_26_24, 
            (read_p2(OSD_BASS_PTR_ADDEND_26_24) | ADDEND_TYPE_RLGATT));
			break;
		case LOAD_TYPE_RLs:
            write_p2(OSD_BASS_PTR_ADDEND_26_24, 
            (read_p2(OSD_BASS_PTR_ADDEND_26_24) | ADDEND_TYPE_RLSATT));
			break;
		}
	} else {
		write_p2(OSD_BASS_PTR_ADDEND_26_24, ADDEND_TYPE_DISABLE);
	}

#if defined(RES_BUILTIN_CODESPACE)
	if(RBKL0) {
		nor_addr = nor_addr + (unsigned long) (RBKL0 << 14);
	}
#endif

    write_p2(OSD_SPI_ADDRESS_7_0, (nor_addr & 0xff));
    write_p2(OSD_SPI_ADDRESS_15_8, ((nor_addr >> 8) & 0xff));
    write_p2(OSD_SPI_ADDRESS_23_16, ((nor_addr >> 16) & 0xff));
    write_p2(OSD_SPI_ADDRESS_31_24, ((nor_addr >> 24) & 0xff));
    write_p2(OSD_SPI_LOAD_CNT_L, (count & 0xff));
    write_p2(OSD_SPI_LOAD_CNT_H, (OSD_GO_LOAD | ((count >> 8) & 0x3f)));

	timeout = read_cpu_count() + 1000;  // 1s
	while(read_p2(OSD_SPI_LOAD_CNT_H) & OSD_GO_LOAD) {
		// Busy
        if(is_time_outs(timeout)) {
			DEBUG_LOGI("[ERROR] spi_load timeout!\n");
			return EXIT_FAILURE;
		}
	}

	return EXIT_SUCCESS;
}

#if !defined(_EX_MCU_VERSION) || !defined(_NON_SPI_FLASH)
bit spi_load_oram_new(unsigned short oram_addr, unsigned long nor_addr, unsigned short count, bit use_cq)
{
	short timeout;

	//DEBUG_LOGI("spi_load_oram: %04x, %08lx, %d\n", oram_addr, nor_addr, count);
#if defined(RES_BUILTIN_CODESPACE)	
	if(RBKL0) {
		nor_addr = nor_addr + (unsigned long) (RBKL0 << 14);
	}
#endif
	if(count & 0x4000) {
		write_p2(OSD_ORAM_PTR_L, (oram_addr & 0xff));
		write_p2(OSD_ORAM_PTR_M, ((oram_addr >> 8) & 0xff));
		write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_ORAM);
		write_p2(OSD_BASS_PTR_ADDEND_26_24, ADDEND_TYPE_DISABLE);
		write_p2(OSD_SPI_ADDRESS_7_0, (nor_addr & 0xff));
		write_p2(OSD_SPI_ADDRESS_15_8, ((nor_addr >> 8) & 0xff));
		write_p2(OSD_SPI_ADDRESS_23_16, ((nor_addr >> 16) & 0xff));
		write_p2(OSD_SPI_ADDRESS_31_24, ((nor_addr >> 24) & 0xff));
		write_p2(OSD_SPI_LOAD_CNT_L, 0x00);
		write_p2(OSD_SPI_LOAD_CNT_H, OSD_GO_LOAD | 0x00);
		if (use_cq) {
			cq_set_trig_mode(CQ_OSD_TRIG);
			CQ_CNT_START();
			CQ_P2_1B_(OSD_SPI_LOAD_CNT_H, OSD_GO_LOAD | 0x00);
			CQ_FINISH_ISSUE();
		}
		else {
			write_p2(OSD_SPI_LOAD_CNT_H, OSD_GO_LOAD | 0x00);
		}
		timeout = read_cpu_count() + 1000;  // 1s
		while(read_p2(OSD_SPI_LOAD_CNT_H) & OSD_GO_LOAD) {
			// Busy
			if(is_time_outs(timeout)) {
				DEBUG_LOGI("[ERROR] spi_load timeout!\n");
				return EXIT_FAILURE;
			}
		}
		oram_addr += 0x4000;
		nor_addr += 0x4000;
		count -= 0x4000;
	}

    write_p2(OSD_ORAM_PTR_L, (oram_addr & 0xff));
    write_p2(OSD_ORAM_PTR_M, ((oram_addr >> 8) & 0xff));
    write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_ORAM);
    write_p2(OSD_BASS_PTR_ADDEND_26_24, ADDEND_TYPE_DISABLE);
    write_p2(OSD_SPI_ADDRESS_7_0, (nor_addr & 0xff));
    write_p2(OSD_SPI_ADDRESS_15_8, ((nor_addr >> 8) & 0xff));
    write_p2(OSD_SPI_ADDRESS_23_16, ((nor_addr >> 16) & 0xff));
    write_p2(OSD_SPI_ADDRESS_31_24, ((nor_addr >> 24) & 0xff));
    write_p2(OSD_SPI_LOAD_CNT_L, (count & 0xff));
    write_p2(OSD_SPI_LOAD_CNT_H, OSD_GO_LOAD | ((count >> 8) & 0x3f));
	if (use_cq) {
		cq_set_trig_mode(CQ_OSD_TRIG);
		CQ_CNT_START();
		CQ_P2_1B_(OSD_SPI_LOAD_CNT_H, OSD_GO_LOAD | ((count >> 8) & 0x3f));
		CQ_FINISH_ISSUE();
	}
	else {
		write_p2(OSD_SPI_LOAD_CNT_H, OSD_GO_LOAD | ((count >> 8) & 0x3f));
	}
	timeout = read_cpu_count() + 1000;  // 1s
	while(read_p2(OSD_SPI_LOAD_CNT_H) & OSD_GO_LOAD) {
		// Busy
        if(is_time_outs(timeout)) {
			DEBUG_LOGI("[ERROR] spi_load timeout!\n");
			return EXIT_FAILURE;
		}
	}

	return EXIT_SUCCESS;
}
#endif

#if defined(_EX_MCU_VERSION)
bit spi_dma(unsigned char *xram_addr, unsigned long nor_addr, unsigned short count)
{
	short timeout;

	write_p0(0xB2, 0x00);
	write_p0(0xB3, 0xF0);
	// sDMA_spiA_BytePtr[1:0] (W/R separated) will be reset to 0 whenever sDMA_xA/XferCnt prog
	write_p0(0xB1, (nor_addr & 0xff));
	write_p0(0xB1, ((nor_addr >> 8) & 0xff));
	write_p0(0xB1, ((nor_addr >> 16) & 0xff));
	write_p0(0xB1, ((nor_addr >> 24) & 0xff));

	write_p0(0xB4, (count & 0xff));
	write_p0(0xB5, 0x80 | ((count >> 8) & 0xf));

	timeout = read_cpu_count() + 1000;  // 1s
	while(read_p0(0xB5) & 0x80) {
        // Busy
		if(is_time_outs(timeout)) {
			DEBUG_LOGI("[ERROR] spi_dma timeout!\n");
			return EXIT_FAILURE;
		}
	}
	write_p0(0xA1, 0x00);
	write_p0(0xA2, 0xF0);
#ifdef SUPPORT_I2C_BURST_WR	
	read_burst_p0(0xA3, xram_addr, count);
#else	
	for(timeout = 0; timeout < count; ++timeout)
		xram_addr[timeout] = read_p0(0xA3);
#endif		

	return EXIT_SUCCESS;
}
#else
bit spi_dma(unsigned char *xram_addr, unsigned long nor_addr, unsigned short count)
{
	short timeout;

	DMAXAL = ((unsigned short)xram_addr & 0xff);
	DMAXAH = (((unsigned short)xram_addr >> 8) & 0xff);
	// sDMA_spiA_BytePtr[1:0] (W/R separated) will be reset to 0 whenever sDMA_xA/XferCnt prog
	DMASAP = (nor_addr & 0xff);
	DMASAP = ((nor_addr >> 8) & 0xff);
	DMASAP = ((nor_addr >> 16) & 0xff);
	DMASAP = ((nor_addr >> 24) & 0xff);
	DMACNTL = (count & 0xff);
	DMAGO = 0x80 | ((count >> 8) & 0xf);
	timeout = read_cpu_count() + 1000;  // 1s
	while(DMAGO & 0x80) {
        // Busy
		if(is_time_outs(timeout)) {
			DEBUG_LOGI("[ERROR] spi_dma timeout!\n");
			return EXIT_FAILURE;
		}
	}
	return EXIT_SUCCESS;
}
#endif
void oram_replace_ptr(unsigned short oram_addr, unsigned short ptr)
{
    write_p2(ORAM_MANIPULATE_7_0, (ptr & 0xff));
    write_p2(ORAM_MANIPULATE_15_8, ((ptr >> 8) & 0xff));
    write_p2(OSD_ORAM_PTR_L, (oram_addr & 0xff));
    write_p2(OSD_ORAM_PTR_M, ((oram_addr >> 8) & 0xff));
    write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_REPLACE | OSD_BYTE_W_2);
}

// Row Component
void row_config(unsigned char offset, unsigned long nor_addr)
{
	unsigned char i = 0;
#if defined(_EX_MCU_VERSION) && defined(_NON_SPI_FLASH)
	unsigned char code *buffer = nor_addr;
#else
	unsigned char buffer[ROW_REG_CONFIG_COUNT];

#if defined(RES_BUILTIN_CODESPACE)
	if(RBKL0) {
		nor_addr = nor_addr + (unsigned long) (RBKL0 << 14);
	}
#endif

	if(spi_dma(buffer, nor_addr, ROW_REG_CONFIG_COUNT) != EXIT_SUCCESS) {
		return;
	}
#endif

	for(i = 0; i < 8; i++) {
		write_p2((ROW_REG_START_INDEX + offset * 8 + i), buffer[i]);
	}
	write_p2(OSD_REG_INDEX, (ROW_OSD_POS_INDEX + offset * 4));
	for(i = 0; i < 4; i++) {
        write_p2(OSD_REG_DATA, (buffer[i + 8]));
	}
    write_p2(OSD_REG_INDEX, (ROW_OSD_PTR_INDEX + offset * 2));
	for(i = 0; i < 2; i++) {
        write_p2(OSD_REG_DATA, (buffer[i + 12]));
	}
}

void row_set_hilt_sel(unsigned char offset, unsigned char sel)
{
    write_p2((ROW_REG_HILT_INDEX + offset), (ROW_HILT_EN | sel));
}

void row_set_strength(uint8_t offset, uint8_t level)
{
    write_p2((ROW_REG_STRENG_INDEX + offset), ((read_p2(ROW_REG_STRENG_INDEX + offset) & 0xf0) | (level & 0x0f)));
}

void row_set_en(unsigned char offset, unsigned long en)
{
#if 0	
	write_p2((ROW_REG_ENABLE_7_0 + offset), (en & 0xff));
	write_p2((ROW_REG_ENABLE_15_8 + offset), ((en >> 8) & 0xff));
	write_p2((ROW_REG_ENABLE_23_16 + offset), ((en >> 16) & 0xff));
	write_p2((ROW_REG_ENABLE_31_24 + offset), ((en >> 24) & 0xff));
#else
	cq_set_trig_mode(CQ_OSD_TRIG);
    CQ_CNT_START();
	CQ_P2_1B_((ROW_REG_ENABLE_7_0 + offset), (en & 0xff));
	CQ_P2_1B_((ROW_REG_ENABLE_15_8 + offset), ((en >> 8) & 0xff));
	CQ_P2_1B_((ROW_REG_ENABLE_23_16 + offset), ((en >> 16) & 0xff));
	CQ_P2_1B_((ROW_REG_ENABLE_31_24 + offset), ((en >> 24) & 0xff));
	CQ_FINISH_ISSUE();
#endif
}
unsigned long row_get_en(unsigned char offset)
{	
	unsigned long en;

	en = read_p2((ROW_REG_ENABLE_31_24 + offset));
	en <<= 8;
	en |= read_p2((ROW_REG_ENABLE_23_16 + offset));
	en <<= 8;
	en |= read_p2((ROW_REG_ENABLE_15_8 + offset));
	en <<= 8;
	en |= read_p2((ROW_REG_ENABLE_7_0 + offset));

	return en;
}

void row_enable(unsigned char offset)
{
	write_p2((ROW_REG_EN_CTRL + offset), read_p2(ROW_REG_EN_CTRL + offset) | 0x80);
}

void row_disable(unsigned char offset)
{
	write_p2((ROW_REG_EN_CTRL + offset), read_p2(ROW_REG_EN_CTRL + offset) & 0x7f);
}

// P2_02[7:4]; Char_BlinkMode[3:0]
void row_char_blink(unsigned char mode)
{
	write_p2(0x02, (mode << 4));
}

// RLg Component
void rlg_config(unsigned char offset, unsigned long nor_addr)
{
	unsigned char i = 0;
#if defined(_EX_MCU_VERSION) && defined(_NON_SPI_FLASH)
	unsigned char code *buffer = nor_addr;
#else	
	unsigned char buffer[RLG_REG_CONFIG_COUNT];

#if defined(RES_BUILTIN_CODESPACE)
	if(RBKL0) {
		nor_addr = nor_addr + (unsigned long) (RBKL0 << 14);
	}
#endif

	if(spi_dma(buffer, nor_addr, RLG_REG_CONFIG_COUNT) != EXIT_SUCCESS) {
		return;
	}
#endif
	for(i = 0; i < 8; i++) {
        write_p2((RLG_REG_START_INDEX + offset * 8 + i), buffer[i]);
	}
    write_p2(OSD_REG_INDEX, (RLG_OSD_POS_INDEX + offset * 4));
	for(i = 0; i < 4; i++) {
        write_p2(OSD_REG_DATA, (buffer[i + 8]));
	}
    write_p2(0xC0, buffer[14]);
    write_p2(OSD_REG_INDEX, (RLG_OSD_PTR_INDEX + offset * 2));
	for(i = 0; i < 2; i++) {
        write_p2(OSD_REG_DATA, (buffer[i + 12]));
	}
}

void rlg_enable(unsigned char offset)
{
	write_p2((RLG_REG_EN_CTRL + offset), read_p2(RLG_REG_EN_CTRL + offset) | 0x80);
}

void rlg_disable(unsigned char offset)
{
	write_p2((RLG_REG_EN_CTRL + offset), read_p2(RLG_REG_EN_CTRL + offset) & 0x7f);
}

void rlg_set_en(unsigned char offset, unsigned short en)
{
	write_p2((RLG_REG_ENABLE + offset), (en & 0xff));
	write_p2((RLG_REG_ENABLE + offset) + 1, ((en >> 8) & 0xff));
}

void rlg_set_strength(unsigned char offset, unsigned char level)
{
	write_p2((RLG_REG_EN_CTRL + offset), 
		((read_p2(RLG_REG_EN_CTRL) & 0xf0) | (level & 0x0f)));
}

void rlg_set_ptr(unsigned char offset, unsigned short ptr)
{
	write_p2(OSD_REG_INDEX, (RLG_OSD_PTR_INDEX + offset * 2));
	write_p2(OSD_REG_DATA, (ptr & 0xff));
	write_p2(OSD_REG_DATA, ((ptr >> 8) & 0xff));
}

void rlg_set_ptr_cq(unsigned char offset, unsigned short optr)
{
	cq_set_trig_mode(CQ_OSD_TRIG);
	CQ_CNT_START();
	CQ_P2_1B_(OSD_REG_INDEX, (RLG_OSD_PTR_INDEX + offset * 2));
	CQ_P2_2B_(OSD_REG_DATA, optr);
	CQ_FINISH_ISSUE();
}

void rlg_swap_image(unsigned short att_ptr, unsigned short oram_ptr, unsigned long nor_addr,
					unsigned short length, bit use_cq)
{
	spi_load_oram_new(oram_ptr, nor_addr, length, use_cq);
	oram_access_sp2_new(att_ptr, oram_ptr,
				0xfffe, OSD_ACCESS_REPLACE, use_cq);
}

// RLs Component
void osd_rls_enable (unsigned char en)
{
	write_p2(RLS_REG_EN_CTRL, read_p2(RLS_REG_EN_CTRL) | en);
}

void osd_rls_disable (unsigned char en)
{
	write_p2(RLS_REG_EN_CTRL, read_p2(RLS_REG_EN_CTRL) & (~en));
}

void osd_wr_rls_en (unsigned char en)
{
	write_p2(RLS_REG_EN_CTRL, (read_p2(RLS_REG_EN_CTRL) & 0x0f) | en);
}

void osd_rls_lut_ws (unsigned char waits)
{
	write_p2(RLS_REG_EN_CTRL, (read_p2(RLS_REG_EN_CTRL) & 0xf3) | waits);
}

void osd_rls_burstmore (unsigned char en)
{
	write_p2(RLS_REG_EN_CTRL, (read_p2(RLS_REG_EN_CTRL) & 0xfe) | (en? 1 : 0));
}

void rls_fifo8bp_trig (unsigned char thd)
{
	write_p2(RLS_REG_FIFO_168BP, (read_p2(RLS_REG_FIFO_168BP) & 0xf0) | (thd & 0xf));
}

void rls_fifo4bp_trig (unsigned char thd)
{
	write_p2(RLS_REG_FIFO_42BP, (read_p2(RLS_REG_FIFO_42BP) & 0x0f) | ((thd & 0xf) << 4));
}

void rls_fifo2bp_trig (unsigned char thd)
{
	write_p2(RLS_REG_FIFO_42BP, (read_p2(RLS_REG_FIFO_42BP) & 0xf0) | (thd & 0xf));
}

void rls_preload_lncnt (unsigned char lncnt)
{
	write_osd_reg(RLS_OREG_PL_LNCNT, lncnt);
}

void rls_config(unsigned char offset, unsigned long nor_addr)
{
	unsigned char i = 0;	
	unsigned char buffer[RLS_REG_CONFIG_COUNT];

	if(spi_dma(buffer, nor_addr, RLS_REG_CONFIG_COUNT) != EXIT_SUCCESS) {
		return;
	}	
#if 0
	for(i = 0; i < 8; i++) {
        write_p2((RLS_REG_START_INDEX + offset * 8 + i), buffer[i]);
	}
#endif	
    write_p2(OSD_REG_INDEX, (RLS_OSD_POS_INDEX + offset * 4));
	for(i = 0; i < 4; i++) {
        write_p2(OSD_REG_DATA, (buffer[i]));
	}
    write_p2(OSD_REG_INDEX, (RLS_OSD_PTR_INDEX + offset * 2));
    write_p2(OSD_REG_DATA, (buffer[4]));
	write_p2(OSD_REG_DATA, (buffer[5]));
	// 2 dummy bytes
}

void rls_set_ptr(unsigned char offset, unsigned short optr)
{
	write_p2(OSD_REG_INDEX, (RLS_OSD_PTR_INDEX + offset * 2));
    write_p2(OSD_REG_DATA, (optr & 0xff));
	write_p2(OSD_REG_DATA, ((optr >> 8) & 0xff));
}

void rls_set_ptr_cq(unsigned char offset, unsigned short optr)
{
	cq_set_trig_mode(CQ_OSD_TRIG);
    CQ_CNT_START();
	CQ_P2_1B_(OSD_REG_INDEX, (RLS_OSD_PTR_INDEX + offset * 2));
	CQ_P2_2B_(OSD_REG_DATA, optr);
	CQ_FINISH_ISSUE();
}


// Mat Component
void mat_reg_config(unsigned char offset, unsigned char *buf)
{
	unsigned char i = 0;

	for(i = 0; i < MAT_REG_CONFIG_COUNT; i++) {
        write_p2((MAT_REG_INDEX + offset + i), buf[i]);
	}
}

void mat_load_config(unsigned char offset, unsigned long nor_addr)
{
	unsigned char i = 0;
#if defined(_EX_MCU_VERSION) && defined(_NON_SPI_FLASH)
	unsigned char code *buffer = nor_addr;
#else
	unsigned char buffer[MAT_REG_CONFIG_COUNT];

	if(spi_dma(buffer, nor_addr, MAT_REG_CONFIG_COUNT) != EXIT_SUCCESS) {
		return;
	}
#endif	
    mat_reg_config(offset, buffer);
}

void mat_disable(unsigned char offset)
{
	write_p2((MAT_REG_EN_CTRL + offset), read_p2(MAT_REG_EN_CTRL + offset) & 0x7f);
}

void mat_enable(unsigned char offset)
{
	write_p2((MAT_REG_EN_CTRL + offset), read_p2(MAT_REG_EN_CTRL + offset) | 0x80);
}


// Win Component
void win_config(unsigned char offset, unsigned char *buf)
{
	unsigned char i = 0;

	for(i = 0; i < 4; i++) {
        write_p2((WIN_REG_START_INDEX + offset * 4 + i), buf[i]);
	}
    write_p2(OSD_REG_INDEX, (WIN_OSD_POS_INDEX + offset * 4));
	for(;i < 8; i++) {
        write_p2(OSD_REG_DATA, buf[i]);
	}
	write_p2(OSD_REG_INDEX, (WIN_OSD_PTR_INDEX + offset * 2));
	for(; i < 10; i++) {
        write_p2(OSD_REG_DATA, buf[i]);
	}
}

void win_load_config(unsigned char offset, unsigned long nor_addr)
{
	unsigned char i = 0;
#if defined(_EX_MCU_VERSION) && defined(_NON_SPI_FLASH)
	unsigned char code *buffer = nor_addr;
#else	
	unsigned char buffer[WIN_REG_LOAD_CONFIG_COUNT];

	if(spi_dma(buffer, nor_addr, WIN_REG_LOAD_CONFIG_COUNT) != EXIT_SUCCESS) {
		return;
	}
#endif	
    win_config(offset, buffer);
}

void win_load_lut(unsigned char *buf, unsigned char count)
{
	unsigned char i = 0;

    write_p2(OSD_ORAM_PTR_L, (OSD_WIN_MAT_LUT_STA_PTR & 0xff));
    write_p2(OSD_ORAM_PTR_M, (OSD_WIN_MAT_LUT_STA_PTR >> 8));
    write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_LUT);
	for(i = 0; i < count; i++) {
        write_p2(OSD_DATAPORT, buf[i]);
	}
    write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_ORAM);
}

void win_set_hilt_sel(unsigned char offset, unsigned char sel)
{
	write_p2((WIN_REG_HIGHLIGHT + offset), (read_p2(WIN_REG_HIGHLIGHT + offset) & 0x10) | (sel & 0x0f));
}

void win_hilt_enable(unsigned char offset, bit en)
{
	if(en)
		write_p2((WIN_REG_HIGHLIGHT + offset), read_p2(WIN_REG_HIGHLIGHT + offset) | WIN_HILT_EN);
	else
		write_p2((WIN_REG_HIGHLIGHT + offset), read_p2(WIN_REG_HIGHLIGHT + offset) & (~WIN_HILT_EN));
}

void win_set_en(unsigned char offset, unsigned short en)
{
	write_p2((WIN_REG_ENABLE_L + offset), (en & 0xff));
	write_p2((WIN_REG_ENABLE_M + offset), ((en >> 8) & 0xff));
}

void win_enable(unsigned char offset, bit en)
{
	if(en)
		write_p2((WIN_REG_STRENGTH + offset), read_p2(WIN_REG_STRENGTH + offset) | 0x80);
	else
		write_p2((WIN_REG_STRENGTH + offset), read_p2(WIN_REG_STRENGTH + offset) & 0x7f);
}

void win_set_ptr(unsigned char offset, unsigned short ptr)
{
	write_p2(OSD_REG_INDEX, (WIN_OSD_PTR_INDEX + offset * 2));
	write_p2(OSD_REG_DATA, (ptr & 0xff));
	write_p2(OSD_REG_DATA, ((ptr >> 8) & 0xff));
}

void win_set_strength(unsigned char offset, unsigned char level)
{
	write_p2((WIN_REG_STRENGTH + offset), ((read_p2(WIN_REG_STRENGTH + offset) & 0xf0) | (level & 0xf)) );
}

void win_add_lut(unsigned char offset, unsigned char *buf, unsigned char count)
{
	unsigned char i = 0;
	unsigned short ptr;

	ptr = (OSD_WIN_MAT_LUT_STA_PTR + offset);

	write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
	write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
	write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_LUT);
	for(i = 0; i < count; i++) {
		write_p2(OSD_DATAPORT, buf[i]);
	}
	write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_ORAM);
}

/**
 * @func    change_string_lut
 * @brief   change CharAtt C13_FgC/C2_Remap/C4_Lut
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned char lut   C13_FgC/C2_Remap/C4_Lut Index
 *          unsigned char *str  string point
 *          unsigned char size  char size
 * @return  none
 */
void change_string_lut(unsigned short ptr, unsigned char lut,
                    unsigned char *str, unsigned char size)
{
	unsigned char i = 0;

	write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
	write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
	write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_ORAM);

	lut <<= 4;
	for(i = 0; i < size; i += 2) {
		if(str[i] == 0 && !(str[i + 1] & 0x01)) {
			// Blank Font
			write_p2(OSD_DATAPORT, 0);
			write_p2(OSD_DATAPORT, str[i + 1]);
		} else {
			write_p2(OSD_DATAPORT, str[i]);
			write_p2(OSD_DATAPORT, (str[i + 1] & 0x0f) | lut);
		}
	}	
}

/**
 * @func    put_string
 * @brief   put CharAtts
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned char *str  string point
 *          unsigned char size  char size
 * @return  none
 */
void put_string(unsigned short ptr, unsigned char *str, unsigned char size)
{
	unsigned char i = 0;

	write_p2(OSD_ORAM_PTR_L, (ptr & 0xff));
	write_p2(OSD_ORAM_PTR_M, (ptr >> 8));
	write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_ORAM);

	for(i = 0; i < size; i += 2) {
		write_p2(OSD_DATAPORT, str[i]);
		write_p2(OSD_DATAPORT, str[i + 1]);
	}	
}

void osd_original_x(unsigned short x)
{
	write_p2(0x04, (x & 0xff));
	write_p2(0x05, ((x >> 8) & 0x0f) | (read_p2(0x05) & 0xf0));
}
void osd_original_y(unsigned short y)
{
	write_p2(0x06, (y & 0xff));
	write_p2(0x07, ((y >> 8) & 0x0f) | (read_p2(0x07) & 0xf0));
}
void osd_strength(unsigned char stren)
{
	write_p2(0xC7, stren);
}
#if defined(_EX_MCU_VERSION) && defined(_NON_SPI_FLASH)
void i2c_load_lut(unsigned short oram_addr, unsigned long nor_addr, 
					unsigned short count)
{
	unsigned short i;
	unsigned char code *src = nor_addr;
	write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_LUT);
	write_p2(OSD_ORAM_PTR_L, (oram_addr & 0xff));
	write_p2(OSD_ORAM_PTR_M, (oram_addr >> 8) & 0xff);	
#ifdef SUPPORT_I2C_BURST_WR
	i = 0;
	while(count > 256) {
		write_burst_p2(OSD_DATAPORT, src+i, 256);
		count -= 256;
		i += 256;
	}
	if(count > 0)
		write_burst_p2(OSD_DATAPORT, src+i, count);
#else
	for(i = 0; i < count; ++i)
		write_p2(OSD_DATAPORT, src[i]);
#endif
}
#define UNUSED(x)    if(x){}
void i2c_load_fatt(unsigned short oram_addr, unsigned long nor_addr, 
					unsigned short count, unsigned char offset)
{
	unsigned short i;
	unsigned char code *src = nor_addr;

	// The i2c load doesn't support offset
	UNUSED(offset);

	write_p2(OSD_ACCESS_TYPE, OSD_BYTE_W_6 | OSD_ACCESS_LUT);
	write_p2(OSD_ORAM_PTR_L, (oram_addr & 0xff));
	write_p2(OSD_ORAM_PTR_M, (oram_addr >> 8) & 0xff);
#ifdef SUPPORT_I2C_BURST_WR		
	i = 0;
	while(count > 256) {
		write_burst_p2(OSD_DATAPORT, src+i, 256);
		count -= 256;
		i += 256;
	}
	if(count > 0)
		write_burst_p2(OSD_DATAPORT, src+i, count);
#else
	for(i = 0; i < count; ++i)
		write_p2(OSD_DATAPORT, src[i]);
#endif
}
void i2c_load_oram(unsigned short oram_addr, unsigned long nor_addr, 
					unsigned short count)
{
	unsigned short i;
	unsigned char code *src = nor_addr;
	write_p2(OSD_ACCESS_TYPE, OSD_ACCESS_ORAM);
	write_p2(OSD_ORAM_PTR_L, (oram_addr & 0xff));
	write_p2(OSD_ORAM_PTR_M, (oram_addr >> 8) & 0xff);	
#ifdef SUPPORT_I2C_BURST_WR
	i = 0;
	while(count > 256) {
		write_burst_p2(OSD_DATAPORT, src+i, 256);
		count -= 256;
		i += 256;
	}
	if(count > 0)
		write_burst_p2(OSD_DATAPORT, src+i, count);
#else
	for(i = 0; i < count; ++i)
		write_p2(OSD_DATAPORT, src[i]);
#endif		
}
#endif

/* for font database */
unsigned long find_code_addr (unsigned long addr, unsigned short cnt, unsigned short uc)
{
    unsigned short start, end, mid;
    unsigned short fcode;
    unsigned long codeaddr;
    start = 0;
    end = (cnt - 1);

    // binary search
	while (start <= end) {
        mid = start + (end - start) / 2;    // avoid overflow
        codeaddr = mid; codeaddr *= OWL_FONTDB_HEADER_SIZE;
        codeaddr += (addr + OWL_FONTDB_UCODE_OFFSET);
        spi_dma((unsigned char *) &fcode, codeaddr, 2);
        if(fcode < uc)	 
			start = mid + 1;
		else if(fcode > uc)
			end = mid - 1;
		else
			break;
    }
    if(fcode != uc) {
        DEBUG_LOGI("no contains 0x%04X font\n", uc);
        return 0;
    }
    codeaddr -= OWL_FONTDB_UCODE_OFFSET;
    return codeaddr;
}

// Enable error flags with interrupts for RLs's FIFO underrun and overrun detection
void osd_en_error_detection(void)
{
	write_p2(0x1f, 0xf0);
}

void osd_isr(void)
{
	/*
	 * Here do not clear flags and CPU will always stay here,
	 * that the purpose is to notify the Programmer of the underrun/overrun occurred.
	 */
	while (read_p2(0x1f) & 0xf0) {
	}
	/*
	 * Please avoid FIFO under-run occurrences, which is the most important due to underrun may cause the bus hang.
	 * That's can be achieved by reducing SPI bandwidth,
	 * Please refer to the app note for how to do reduction the SPI bandwidth.
	 */
}
