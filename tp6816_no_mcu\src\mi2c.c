/*
 * @file      mi2c.c
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:55 created
 * $Id: mi2c.c 1.1 $
 */

#include "bsp_i2c.h"
#include "mi2c.h"


bit mi2c_wr_byte(unsigned char sid, unsigned char addr, unsigned char value)
{
	uint8_t ret=0;
	ret = bsp_sim_i2c_write8_bytes(4,sid,addr,(uint8_t *)&value,1);
	return ret==0?1:0;
}

bit mi2c_rd_byte(unsigned char sid, unsigned char addr, unsigned char *value)
{
	uint8_t ret=0;
	ret = bsp_sim_i2c_read8_bytes(4,sid,addr,value,1);
	return ret==0?1:0;
}

unsigned char mi2c_rd_byte2(unsigned char sid, unsigned char addr)
{
	uint8_t ret=0;
	uint8_t r_data=0;
	ret = bsp_sim_i2c_read8_bytes(4,sid,addr,(uint8_t*)&r_data,1);
	return ret==0?1:r_data;
}
/**
 * @func    mi2c_rd_bytes
 * @brief   Control Master I2C to read bytes.
 * @param	sid, addr, size, *buf
 * @return	0: success, 1: fail
 * @note	size 0 = 256, else 1~255
 */
bit mi2c_rd_bytes(unsigned char sid, unsigned char addr, 
					unsigned char size, unsigned char *buf)
{
	uint8_t ret=0;
	if(size == 0)
		ret = bsp_sim_i2c_read8_bytes(4,sid,addr,buf,256);
	else
		ret = bsp_sim_i2c_read8_bytes(4,sid,addr,buf,size);
	return ret==0?1:0;
}

/**
 * @func    mi2c_wr_bytes
 * @brief   Control Master I2C to write bytes.
 * @param	sid, addr, size, *buf
 * @return	0: success, 1: fail
 * @note	size 0 = 256, else 1~255
 */
bit mi2c_wr_bytes(unsigned char sid, unsigned char addr, 
					unsigned char size, unsigned char *buf)
{
	uint8_t ret=0;
	if(size == 0)
		ret = bsp_sim_i2c_write8_bytes(4,sid,addr,buf,256);
	else
		ret = bsp_sim_i2c_write8_bytes(4,sid,addr,buf,size);
	return ret==0?1:0;
}

bit mi2c_2a_wr_byte(unsigned char sid, unsigned int addr, unsigned char value)
{
	uint8_t ret=0;
	ret = bsp_sim_i2c_write_reg(4,sid,addr,value);
	return ret==0?1:0;
}

bit mi2c_2a_rd_byte(unsigned char sid, unsigned int addr, unsigned char *value)
{
	uint8_t ret=0;
	ret = bsp_sim_i2c_read_reg(4,sid,addr,value);
	return ret==0?1:0;
}
/**
 * @func    mi2c_2a_wr_bytes
 * @brief   Control Master I2C to write bytes (2bytes address).
 * @param	sid, addr, size, *buf
 * @return	0: success, 1: fail
 * @note	size 0 = 256, else 1~255
 */
bit mi2c_2a_wr_bytes(unsigned char sid, unsigned int addr, 
						unsigned char size, unsigned char *buf)
{
	uint8_t ret=0;
	if(size == 0)
		ret = bsp_sim_i2c_write16_bytes(4,sid,addr,buf,256);
	else
		ret = bsp_sim_i2c_write16_bytes(4,sid,addr,buf,size);
	return ret==0?1:0;
}

bit mi2c_2a_rd_bytes(unsigned char sid, unsigned int addr, 
					unsigned char size, unsigned char *buf)
{
	uint8_t ret=0;
	if(size == 0)
		ret = bsp_sim_i2c_read16_bytes(4,sid,addr,buf,256);
	else
		ret = bsp_sim_i2c_read16_bytes(4,sid,addr,buf,size);
	return ret==0?1:0;
}

bit mi2c_load_tbl(unsigned char sid, unsigned char *tbl,unsigned int size)
{
	unsigned int i = 0;
	for (i = 0; i < size; i += 2) {
		if(mi2c_wr_byte(sid, tbl[i], tbl[i + 1])) {
			return 1;
		}
	}
	return 0;
}

bit mi2c_2a_load_tbl(unsigned char sid, unsigned int *tbl,unsigned int size)
{
	unsigned int i = 0;
	for (i = 0; i < size; i += 2) {
		if(mi2c_2a_wr_byte(sid, tbl[i], tbl[i + 1])) {
			return 1;
		}
	}
	return 0;
}
