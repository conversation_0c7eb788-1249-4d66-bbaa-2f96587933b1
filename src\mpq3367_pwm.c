#include "mpq3367_pwm.h"
#include "ftm.h"

uint8_t mpq3367_enable_state;   //背光开启标志位


// configure Pin Function Select for GPIOA6   mpq3367
void  mpq3367_enable(void)
{
	if(mpq3367_enable_state == 1)
	{
		return;
	}
	mpq3367_enable_state = 1;
	MPQ3367_EN_on();
	OUTPUT_SET(PTB,PTB3);
	DEBUG_LOGI("mpq3367_enable\r\n");
}

void  mpq3367_disable(void)
{
	if(mpq3367_enable_state == 0)
	{
		return;
	}
	mpq3367_enable_state = 0;
	MPQ3367_EN_off();
	DEBUG_LOGI("mpq3367_disable\r\n");
}
extern void pwm_set_init(void);
void mpq3367_init(void)
{
	mpq3367_enable_state = 0;
	MPQ3367_PG_config();
	MPQ3367_EN_config();
	MPQ3367_EN_off();
	mpq3367_pwm_init();
	pwm_set_init();
}
void FTM0_Task(void);
void  mpq3367_pwm_init(void)
{
	FTM_ConfigType FTM0_Config={0};
	FTM_ChParamsType FTM1CH0_Config={0};

	FTM0_Config.modulo=4699;  //10khz
	FTM0_Config.clk_source=FTM_CLOCK_SYSTEMCLOCK;
	FTM0_Config.prescaler=FTM_CLOCK_PS_DIV1;
	FTM0_Config.mode=1;
	FTM0_Config.toie=1;

	SIM_PINSEL0 |= SIM_PINSEL_FTM0PS1_MASK; /* Select Pins corresponds to the PTE7 for output */

	FTM1CH0_Config.ctrl.bits.bMode=FTM_PWMMODE_EDGEALLIGNED;
	FTM1CH0_Config.ctrl.bits.bPWMPol=FTM_PWM_HIGHTRUEPULSE;
	FTM1CH0_Config.u16CnV=1000;


	FTM_SetCallback(FTM0, FTM0_Task);
	FTM_ChannelInit(FTM0,1,FTM1CH0_Config);
	FTM_Init(FTM0,&FTM0_Config);
}

uint16_t u16ChV_old;
static uint16_t u16ChV_new;
void FTM0_Task(void)
{
    static uint16_t u16count;
    static uint8_t u8DirMark;
    FTM_MemMapPtr pFTM = FTM0;

    /* clear the flag */
    FTM_ClrOverFlowFlag(FTM0);

    if(100 == u16count)
    {
        u16count = 0;
        u16ChV_old = pFTM->CONTROLS[1].CnV;
        if(u16ChV_old!= u16ChV_new)
        {
        	/* update the channel value */
        	FTM_SetChannelValue(FTM0, FTM_CHANNEL_CHANNEL1, u16ChV_new);
        }
    }
    else
    {
        u16count++;
    }
}


void  mpq3367_pwm_set(uint8_t frequency,uint8_t high,uint8_t duration)
{	
	u16ChV_new = (uint16_t)(high)*4699/100;
	//FTM_SetChannelValue(FTM0, FTM_CHANNEL_CHANNEL1, high*10);//u16ChV_new);
}


