/*
 * bsp_isp.c
 *
 *  Created on: 20240118
 *      Author: andrew
 */

#include "i2c.h"
#include "gpio.h"
#include "tick.h"
#include "signal.h"
#include "para.h"
#include "config.h"
#include "state.h"
#include "para.h"
#include <stdio.h>
#include "bsp_isp.h"
#include "power.h"

static event_handle_t isp_handle;
static uint16_t  tick_isp_check;
typedef enum
{
	ISP_INIT = 0,
	ISP_VIEW,
	ISP_ANGLE,
	ISP_IDLE,
#if (USE_ISP_LAW_BY_KEY==1 || USE_API_CODE_LOAD_APPLY_ISPCFGBIN_BYLIN==1)
	ISP_LAWS,
#endif
#if (LIN_ISP_VERSION_QUERY==1)
	ISP_VER,
#endif
#if (LIN_ISP_OTHER_QUERY==1)
	ISP_ANGLE_READ,
	ISP_VIEW_READ,
	ISP_LAWS_READ,
#endif
#if (ISP_VEHICLE_TYPE_QUERY==1)
	ISP_VEHICLE_TYPE,
#endif
}ISP_STATUS_T;
typedef struct
{
	ISP_STATUS_T status;
	uint8_t angle;
	uint8_t view;
	uint8_t fail_times;
	uint32_t time;
}isp_t;
#define VEDIO_I2C_ADDRESS	0x40
#define VEDIO_I2C_REG_VIEW	0x0407
#define VEDIO_I2C_REG_ANGLE 0x0401
#define VEDIO_I2C_REG_LAWS  0x0402
#define VEDIO_I2C_REG_VER   0xD3
#if (LIN_ISP_OTHER_QUERY==1)
#define VEDIO_I2C_REG_ANGLE_READ 0x0501
#define VEDIO_I2C_REG_VIEW_READ	0x0507
#define VEDIO_I2C_REG_LAWS_READ 0x0502
#endif
#if (ISP_VEHICLE_TYPE_QUERY==1)
#define VEDIO_I2C_REG_VEHICLE_TYPE 0xD1  //车型信息查询寄存器
#endif
static isp_t g_isp;


static void isp_data_init()
{
	g_isp.status = ISP_INIT;
	g_isp.time = tick_get();
	g_isp.view = 0;
	g_isp.angle = 3;
	g_isp.fail_times = 0;
}
void isp_init(void)
{
	isp_handle = event_group_creat();
	isp_data_init();
	isp_event_post(EV_ISP_WRITE_LAWS|EV_ISP_WRITE_ANGLE|EV_ISP_WRITE_VIEW|EV_ISP_READ_VER);
#if (ISP_VEHICLE_TYPE_QUERY==1)
	isp_event_post(EV_ISP_READ_VEHICLE_TYPE);
#endif
}

void isp_event_post(uint32_t ev)
{
	event_group_post(isp_handle, ev);
}

uint8_t isp_set_laws(void)
{
	uint8_t ret = 0;
	uint8_t isp_mode = MirrPara.vedio_para.StreamingMirrModeSwt;
	printf("[%s] isp_mode=%d\n", __FUNCTION__,isp_mode);
	if(isp_mode >= 0 && isp_mode <= 1){
#if (USE_ISP_LAW_FLIP == 1)
		ret = bsp_sim_i2c_write_reg(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_LAWS, 1-isp_mode);
#else
		ret = bsp_sim_i2c_write_reg(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_LAWS, isp_mode);
#endif
		printf("[%s] ret=%d\r\n", __FUNCTION__,ret);
	}
	return ret;

}
uint8_t isp_set_angle(uint8_t _angle)
{
	uint8_t ret = 0;
	if(_angle>=0 && _angle <=6)
	{
#if (USE_ISP_ANGLE_FLIP == 1)
		ret = bsp_sim_i2c_write_reg(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_ANGLE, 6-_angle);
#else
		ret = bsp_sim_i2c_write_reg(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_ANGLE, _angle);
#endif
		printf("[%s] ret=%d\r\n", __FUNCTION__,ret);
	}
	return ret;
}

uint8_t isp_set_view(uint8_t _view)
{
	uint8_t ret = 0;
	if(_view>=0 && _view <=2)
	{
		ret = bsp_sim_i2c_write_reg(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_VIEW, _view);
		printf("[%s] ret=%d\r\n", __FUNCTION__,ret);
	}
	return ret;
}

uint8_t lin_isp_version = 0x0;
#if (LIN_ISP_VERSION_QUERY == 1)
#if 0
static uint8_t ISP_VERSION_ARR[][12]={
        "EF1E1v0.00S",
        "EF1E1v0.01S",
        "EF1E1v0.02S",
        "EF1E1v0.03S",
        "EF1E1v0.04S",
};
static uint8_t LIN_VERSION_ISP_ARR[]={
        0x00,
        0x01,
        0x02,
        0x03,
        0x04
};

uint8_t query_lin_version_isp_index(uint8_t str[])
{
	uint8_t i;
	uint8_t index = 0;
	uint8_t number = sizeof(LIN_VERSION_ISP_ARR) / sizeof(LIN_VERSION_ISP_ARR[0]);
	printf("[%s] ispversion=%s, number=%d\n", __FUNCTION__,str,number);
	for(i=0; i<number; i++){
		if(strcmp(str,&ISP_VERSION_ARR[i][0]) == 0){
			index = i;
			//printf("[%s] index=%d\n", __FUNCTION__,index);
			break;
		}
	}
	printf("[%s] index=%d\n", __FUNCTION__,index);
	return index;
}

uint8_t get_lin_version_isp(uint8_t ispver[])
{
	uint8_t ver,index;
	index = query_lin_version_isp_index(ispver);
	ver = LIN_VERSION_ISP_ARR[index];
	printf("[%s] isp lin version=%#x\r\n", __FUNCTION__,ver);
	return ver;
}
#endif

uint8_t isp_get_version(uint8_t *ver)
{
	uint8_t ret = 0;

	ret = bsp_sim_i2c_read8_bytes(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_VER, ver, 1);
	DEBUG_LOGI("[%s] ret=%d, ver=%d\r\n", __FUNCTION__,ret,*ver);

	return ret;
}

#endif

#if (ISP_VEHICLE_TYPE_QUERY==1)
uint8_t isp_get_vehicle_type(uint8_t *vehicle_type)
{
	uint8_t ret = 0;

	ret = bsp_sim_i2c_read8_bytes(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_VEHICLE_TYPE, vehicle_type, 1);
	DEBUG_LOGI("[%s] ret=%d, vehicle_type=0x%02x\r\n", __FUNCTION__, ret, *vehicle_type);

	// 打印车型信息
	if (ret) {
		switch (*vehicle_type) {
			case 0x09:
				DEBUG_LOGI("Vehicle Type: EF1E-A1\r\n");
				break;
			case 0x0d:
				DEBUG_LOGI("Vehicle Type: EX1H\r\n");
				break;
			case 0x0e:
				DEBUG_LOGI("Vehicle Type: XE08\r\n");
				break;
			default:
				DEBUG_LOGI("Vehicle Type: Unknown (0x%02x)\r\n", *vehicle_type);
				break;
		}
	}

	return ret;
}
#endif

#if (LIN_ISP_OTHER_QUERY==1)
uint8_t isp_get_angle(uint8_t *data)
{
	uint8_t ret = 0;

	ret = bsp_sim_i2c_read16_bytes(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_ANGLE_READ, data, 1);
	DEBUG_LOGI("[%s] ret=%d, data=%d\r\n", __FUNCTION__,ret,*data);

	return ret;
}
uint8_t isp_get_view(uint8_t *data)
{
	uint8_t ret = 0;

	ret = bsp_sim_i2c_read16_bytes(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_VIEW_READ, data, 1);
	DEBUG_LOGI("[%s] ret=%d, data=%d\r\n", __FUNCTION__,ret,*data);

	return ret;
}
uint8_t isp_get_laws(uint8_t *data)
{
	uint8_t ret = 0;

	ret = bsp_sim_i2c_read16_bytes(4, VEDIO_I2C_ADDRESS, VEDIO_I2C_REG_LAWS_READ, data, 1);
	DEBUG_LOGI("[%s] ret=%d, data=%d\r\n", __FUNCTION__,ret,*data);

	return ret;
}
#endif


#define ISP_CHECK_DELAY_COUNT_READ 200
uint8_t isp_init_status = 0;
void isp_process(void)
{
	//显示错误的情况下暂不执行指令

	if(state_get_err() & ERR_MAX96752)
	{
		DEBUG_LOGI("isp get ERR_MAX96752\r\n");
		return;
	}
	if (getVideoFlag()==1)
	{
		if(get_display_init_flag()==MIPI_INIT_CHECK_OVER)
		{
			//初始化过程已经结束，但是错误，这时候不再初始化isp
			set_display_init_flag(MIPI_INIT_ISP_FAIL);
			power_event_post(EV_P_POWER_INIT_OVER);
			DEBUG_LOGI("isp get error\r\n");
		}
		return;
	}

	switch(g_isp.status)
	{
		case ISP_INIT:
		{
			//等待g_isp、摄像头初始化完成
		    if (SysGetLapseTick(tick_isp_check) >= 10)
		    {
		    	DEBUG_LOGI("isp_init\r\n");
				g_isp.status = ISP_IDLE;
				isp_init_status = 0;
				SysSetCurrentTick(&tick_isp_check);
			}
		}
		break;
		case ISP_ANGLE:
		{
		    if (SysGetLapseTick(tick_isp_check) < 10)
		    {
		    	return;
		    }
		    SysSetCurrentTick(&tick_isp_check);
			DEBUG_LOGI("isp_angle in[%x]\n",g_isp.angle);
			uint8_t rtn = isp_set_angle(g_isp.angle);
			if(rtn == 0)
			{
				DEBUG_LOGI("ispS_vedio_angle_ctrl(%d) fail!====\r\n",g_isp.angle);
				if(++g_isp.fail_times >= 3){
					g_isp.status = ISP_IDLE;
					isp_event_post(EV_ISP_WRITE_ANGLE);
				}
			}
			else
			{
				if (isp_init_status ==0)
					isp_init_status ++;
				g_isp.status = ISP_IDLE;
			}
		}
		break;
		case ISP_VIEW:
		{
		    if (SysGetLapseTick(tick_isp_check) < 10)
		    {
		    	return;
		    }
		    SysSetCurrentTick(&tick_isp_check);
			DEBUG_LOGI("isp_view in[%x]\n",g_isp.view);
			uint8_t rtn = isp_set_view(g_isp.view);
			if(rtn == 0)
			{
				DEBUG_LOGI("ispS_vedio_view_ctrl(%d) fail!====\r\n",g_isp.view);
				if(++g_isp.fail_times >= 3){
					g_isp.status = ISP_IDLE;
					isp_event_post(EV_ISP_WRITE_VIEW);
				}
			}
			else
			{
				if (isp_init_status ==1)
					isp_init_status ++;
				g_isp.status = ISP_IDLE;

			}

		}
		break;
#if		(USE_ISP_LAW_BY_KEY==1 || USE_API_CODE_LOAD_APPLY_ISPCFGBIN_BYLIN==1)
		case ISP_LAWS:
		{
		    if (SysGetLapseTick(tick_isp_check) < 10)
		    {
		    	return;
		    }
		    SysSetCurrentTick(&tick_isp_check);
			uint8_t rtn = isp_set_laws();
			if(rtn == 0)
			{
				DEBUG_LOGI("ispS_vedio_mode_ctrl fail!====\r\n");
				if(++g_isp.fail_times >= 3){
					g_isp.status = ISP_IDLE;
					isp_event_post(EV_ISP_WRITE_LAWS);
				}
			}
			else
			{
				g_isp.status = ISP_IDLE;
				//isp_event_post(EV_ISP_WRITE_ANGLE);
				//isp_event_post(EV_ISP_WRITE_VIEW);
				isp_event_post(EV_ISP_READ_LAWS);
			}
		}
		break;
#endif
#if (LIN_ISP_VERSION_QUERY==1)
		case ISP_VER:
		{
			//extern unsigned char isp_version[12];
		    if (SysGetLapseTick(tick_isp_check) < ISP_CHECK_DELAY_COUNT_READ)
		    {
		    	return;
		    }
			SysSetCurrentTick(&tick_isp_check);
			uint8_t rtn = isp_get_version(&lin_isp_version);
			DEBUG_LOGI("[%s] rtn=%d, lin_isp_version=%d\r\n", __FUNCTION__,rtn,lin_isp_version);
			if(rtn == 0)
			{
				if(++g_isp.fail_times >= 3){
					g_isp.status = ISP_IDLE;
					isp_event_post(EV_ISP_READ_VER);
				}
			}else{
				if (isp_init_status ==1)
					isp_init_status ++;
				g_isp.status = ISP_IDLE;
			}
		}
		break;
#endif
#if (LIN_ISP_OTHER_QUERY==1)
		case ISP_ANGLE_READ:
		{
			uint8_t isp_angle = 0;
		    if (SysGetLapseTick(tick_isp_check) < ISP_CHECK_DELAY_COUNT_READ)
		    {
		    	return;
		    }
			SysSetCurrentTick(&tick_isp_check);
			uint8_t rtn = isp_get_angle(&isp_angle);
			DEBUG_LOGI("[%s] rtn=%d, isp_angle=%d\r\n", __FUNCTION__,rtn,isp_angle);
			if(rtn == 0)
			{
				if(++g_isp.fail_times >= 3){
					g_isp.status = ISP_IDLE;
					isp_event_post(EV_ISP_READ_ANGLE);
				}
			}else{
				g_isp.status = ISP_IDLE;
			}
		}
		break;
		case ISP_VIEW_READ:
		{
			uint8_t isp_view = 0;
		    if (SysGetLapseTick(tick_isp_check) < ISP_CHECK_DELAY_COUNT_READ)
		    {
		    	return;
		    }
			SysSetCurrentTick(&tick_isp_check);
			uint8_t rtn = isp_get_view(&isp_view);
			DEBUG_LOGI("[%s] rtn=%d, isp_view=%d\r\n", __FUNCTION__,rtn,isp_view);
			if(rtn == 0)
			{
				if(++g_isp.fail_times >= 3){
					g_isp.status = ISP_IDLE;
					isp_event_post(EV_ISP_READ_VIEW);
				}
			}else{
				g_isp.status = ISP_IDLE;
			}
		}
		break;
		case ISP_LAWS_READ:
		{
			uint8_t isp_laws = 0;
		    if (SysGetLapseTick(tick_isp_check) < ISP_CHECK_DELAY_COUNT_READ)
		    {
		    	return;
		    }
			SysSetCurrentTick(&tick_isp_check);
			uint8_t rtn = isp_get_laws(&isp_laws);
			DEBUG_LOGI("[%s]camera(0-zg,1-fg) rtn=%d, isp_laws=%d\r\n", __FUNCTION__,rtn,isp_laws);
			if(rtn == 0)
			{
				if(++g_isp.fail_times >= 3){
					g_isp.status = ISP_IDLE;
					isp_event_post(EV_ISP_READ_LAWS);
				}
			}else{
				g_isp.status = ISP_IDLE;
			}
		}
		break;
#endif
#if (ISP_VEHICLE_TYPE_QUERY==1)
		case ISP_VEHICLE_TYPE:
		{
			uint8_t vehicle_type = 0;
		    if (SysGetLapseTick(tick_isp_check) < ISP_CHECK_DELAY_COUNT_READ)
		    {
		    	return;
		    }
			SysSetCurrentTick(&tick_isp_check);
			uint8_t rtn = isp_get_vehicle_type(&vehicle_type);
			DEBUG_LOGI("[%s] rtn=%d, vehicle_type=0x%02x\r\n", __FUNCTION__, rtn, vehicle_type);
			if(rtn == 0)
			{
				if(++g_isp.fail_times >= 3){
					g_isp.status = ISP_IDLE;
					isp_event_post(EV_ISP_READ_VEHICLE_TYPE);
				}
			}else{
				g_isp.status = ISP_IDLE;
			}
		}
		break;
#endif
		case ISP_IDLE:
		{
		    if (SysGetLapseTick(tick_isp_check) < 10)
		    {
		    	return;
		    }
		    SysSetCurrentTick(&tick_isp_check);

			if (get_display_init_flag()==1)
			{
#if		(USE_ISP_LAW_BY_KEY==1 || USE_API_CODE_LOAD_APPLY_ISPCFGBIN_BYLIN==1)			
				if (isp_init_status>=2 && isp_init_status <5)				
				{
					isp_init_status ++;
				}
				else if (isp_init_status ==5)
#else
				if (isp_init_status>=2 && isp_init_status <4)
				{
					isp_init_status ++;
				}
				else if (isp_init_status ==4)
#endif				
				{
					set_display_init_flag(MIPI_INIT_ISP_OK);
					power_event_post(EV_P_POWER_INIT_OVER);
				}
			}
#if (USE_ISP_LAW_BY_KEY==1 || USE_API_CODE_LOAD_APPLY_ISPCFGBIN_BYLIN==1)
			if(event_group_wait(isp_handle,EV_ISP_WRITE_LAWS))
			{
				DEBUG_LOGI("EV_ISP_WRITE_LAWS\r\n");
				g_isp.status = ISP_LAWS;
				g_isp.fail_times = 0;
			}
#endif
			else if(event_group_wait(isp_handle, EV_ISP_WRITE_ANGLE))
			{
				DEBUG_LOGI("EV_ISP_WRITE_ANGLE\r\n");
				g_isp.angle  = MirrPara.vedio_para.StreamingMirrPosnAdjmt;//g_isp.pos  = 4;
				g_isp.status = ISP_ANGLE;
				g_isp.fail_times = 0;
			}
			else if(event_group_wait(isp_handle,EV_ISP_WRITE_VIEW))
			{
				DEBUG_LOGI("EV_ISP_WRITE_VIEW\r\n");
				g_isp.status = ISP_VIEW;
				g_isp.view = MirrPara.vedio_para.StreamingMirrImgAdjmt;
				g_isp.fail_times = 0;
			}
#if (LIN_ISP_VERSION_QUERY==1)
			else if(event_group_wait(isp_handle,EV_ISP_READ_VER)){
				DEBUG_LOGI("EV_ISP_READ_VER\r\n");
				g_isp.status = ISP_VER;
				g_isp.fail_times = 0;
			}
#endif
#if (LIN_ISP_OTHER_QUERY==1)
			else if(event_group_wait(isp_handle,EV_ISP_READ_ANGLE)){
				DEBUG_LOGI("EV_ISP_READ_ANGLE\r\n");
				g_isp.status = ISP_ANGLE_READ;
				g_isp.fail_times = 0;
			}
			else if(event_group_wait(isp_handle,EV_ISP_READ_VIEW)){
				DEBUG_LOGI("EV_ISP_READ_VIEW\r\n");
				g_isp.status = ISP_VIEW_READ;
				g_isp.fail_times = 0;
			}
			else if(event_group_wait(isp_handle,EV_ISP_READ_LAWS)){
				DEBUG_LOGI("EV_ISP_READ_LAWS\r\n");
				g_isp.status = ISP_LAWS_READ;
				g_isp.fail_times = 0;
			}
#endif
#if (ISP_VEHICLE_TYPE_QUERY==1)
			else if(event_group_wait(isp_handle,EV_ISP_READ_VEHICLE_TYPE)){
				DEBUG_LOGI("EV_ISP_READ_VEHICLE_TYPE\r\n");
				g_isp.status = ISP_VEHICLE_TYPE;
				g_isp.fail_times = 0;
			}
#endif
		}
		break;
	default:break;
	}
}
