/******************************************************************************
*
* Freescale Semiconductor Inc.
* (c) Copyright 2013-2016 Freescale Semiconductor, Inc.
* Copyright 2016-2024 NXP
* ALL RIGHTS RESERVED.
*
****************************************************************************//*!
*
* @file      lin_cfg.h
*
* <AUTHOR> Software
*
* @version   1.0
*
* @date      Wed Nov 13 10:32:04 CST 2024
*
* @brief     Hardware configuration file
*
******************************************************************************/
#ifndef    _LIN_CFG_H_
#define    _LIN_CFG_H_
#include "lin_hw_cfg.h"
/* Define operating mode */
#define _MASTER_MODE_     0
#define _SLAVE_MODE_      1
#define LIN_MODE   _SLAVE_MODE_
/* Define protocol version */
#define PROTOCOL_21       0
#define PROTOCOL_J2602    1
#define PROTOCOL_20       2
#define LIN_PROTOCOL    PROTOCOL_21

#define LDF3_PARTNUMBER_VERSION_SWAP    1 //VCAE add

#define UART_ADDR        UART2_ADDR     /* For slave */
#define LIN_BAUD_RATE    19200    	 /*For slave*/
/**********************************************************************/
/***************          Diagnostic class selection  *****************/
/**********************************************************************/
#define _DIAG_CLASS_I_          0
#define _DIAG_CLASS_II_         1
#define _DIAG_CLASS_III_        2

#define _DIAG_CLASS_SUPPORT_    _DIAG_CLASS_I_

#define MAX_LENGTH_SERVICE 6

#define MAX_QUEUE_SIZE 1


#define _DIAG_NUMBER_OF_SERVICES_    5

#define DIAGSRV_READ_BY_IDENTIFIER_ORDER    0

#define DIAGSRV_ASSIGN_FRAME_ID_RANGE_ORDER    1

#define DIAGSRV_ASSIGN_NAD_ORDER    2

#define DIAGSRV_CONDITIONAL_CHANGE_NAD_ORDER    3

#define DIAGSRV_SAVE_CONFIGURATION_ORDER    4


/**************** FRAME SUPPORT DEFINITION ******************/
#define _TL_SINGLE_FRAME_       0
#define _TL_MULTI_FRAME_        1

#define _TL_FRAME_SUPPORT_      _TL_SINGLE_FRAME_

/* frame buffer size */
#define LIN_FRAME_BUF_SIZE			69
#define LIN_FLAG_BUF_SIZE			11

/**********************************************************************/
/***************               Interfaces           *******************/
/**********************************************************************/
typedef enum {
   LI0
}l_ifc_handle;

/**********************************************************************/
/***************               Signals              *******************/
/**********************************************************************/
/* Number of signals */
#define LIN_NUM_OF_SIGS  57
/* List of signals */
typedef enum {

   /* Interface_name = LI0 */

   LI0_IntrMirrDimRespIntrMirrDimPerc

   , LI0_IntrMirrDimRespIntrMirrIntFailr
  
   , LI0_IntrMirrDimRespResdBoolean
  
   , LI0_IntrMirrDimRespResdUInt6
  
   , LI0_IRMMRMDPartNoCmplEndSgn1
  
   , LI0_IRMMRMDPartNoCmplEndSgn2
  
   , LI0_IRMMRMDPartNoCmplEndSgn3
  
   , LI0_IRMMRMDPartNoCmplNr1
  
   , LI0_IRMMRMDPartNoCmplNr2
  
   , LI0_IRMMRMDPartNoCmplNr3
  
   , LI0_IRMMRMDPartNoCmplNr4
  
   , LI0_IRMMRMDPartNo10CmplEndSgn1
  
   , LI0_IRMMRMDPartNo10CmplEndSgn2
  
   , LI0_IRMMRMDPartNo10CmplEndSgn3
  
   , LI0_IRMMRMDPartNo10CmplNr1
  
   , LI0_IRMMRMDPartNo10CmplNr2
  
   , LI0_IRMMRMDPartNo10CmplNr3
  
   , LI0_IRMMRMDPartNo10CmplNr4
  
   , LI0_IRMMRMDPartNo10CmplNr5
  
   , LI0_IRMMRMDSerNoNr1
  
   , LI0_IRMMRMDSerNoNr2
  
   , LI0_IRMMRMDSerNoNr3
  
   , LI0_IRMMRMDSerNoNr4
  
   , LI0_ErrRespRMD
  
   , LI0_StreamingMirrBri
  
   , LI0_StreamingMirrEnable
  
   , LI0_StreamingMirrImg
  
   , LI0_StreamingMirrModeSts
  
   , LI0_StreamingMirrPosn
  
   , LI0_StreamingMirrReWrnSwtSts
  
   , LI0_CbnGroupPwrReqforRMD
  
   , LI0_StreamingMirrModeSwt
  
   , LI0_StreamingMirrReWrnDis
  
   , LI0_StreamingMirrReWrnSwt
  
   , LI0_AmbTForVisy
  
   , LI0_VehModMngtGlbSafeCarModSts
  
   , LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp
  
   , LI0_VehModMngtGlbSafeChks8
  
   , LI0_VehModMngtGlbSafeCntr4
  
   , LI0_VehModMngtGlbSafeDataID4
  
   , LI0_VehModMngtGlbSafeEgyLvlElecMai
  
   , LI0_VehModMngtGlbSafeEgyLvlElecSubtyp
  
   , LI0_VehModMngtGlbSafeFltEgyCnsWdSts
  
   , LI0_VehModMngtGlbSafePwrLvlElecMai
  
   , LI0_VehModMngtGlbSafePwrLvlElecSubtyp
  
   , LI0_VehModMngtGlbSafePwrModSts
  
   , LI0_IntrMirrDimCmdDrvrSide
  
   , LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag
  
   , LI0_IntrMirrDimCmdIntrMirrDiagcRst
  
   , LI0_IntrMirrDimCmdIntrMirrDimSnvty
  
   , LI0_IntrMirrDimCmdIntrMirrEna
  
   , LI0_IntrMirrDimCmdIntrMirrInhbDim
  
   , LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag
  
   , LI0_StreamingMirrBriAdjmt
  
   , LI0_StreamingMirrImgAdjmt
  
   , LI0_StreamingMirrPosnAdjmt
  
   , LI0_StreamingMirrSwt
  
} l_signal_handle;
/**********************************************************************/
/*****************               Frame             ********************/
/**********************************************************************/
/* Number of frames */
#define LIN_NUM_OF_FRMS  11
/* List of frames */
typedef enum {
/* All frames for master node */

   /* Interface_name = LI0 */

   LI0_RMDZCLLin5Frame01

   , LI0_ZCLZCLLin5Frame02
  
   , LI0_ZCLZCLLin5Frame03
  
   , LI0_ZCLZCLLin5Frame08
  
   , LI0_ZCLZCLLin5Frame09
  
   , LI0_IRMMZCLLin5Frame01
  
   , LI0_IRMMZCLLin5PartNrFr01
  
   , LI0_IRMMZCLLin5PartNrFr02
  
   , LI0_IRMMZCLLin5SerNrFr01
  
   , LI0_MasterReq
  
   , LI0_SlaveResp
  
} l_frame_handle;
/**********************************************************************/
/***************             Configuration          *******************/
/**********************************************************************/
/* Size of configuration in ROM and RAM used for interface: LI1 */
#define LIN_SIZE_OF_CFG  13
#define LIN_CFG_FRAME_NUM  9
/*********************************************************************
 * global macros
 *********************************************************************/
#define l_bool_rd(SIGNAL) l_bool_rd_##SIGNAL()
#define l_bool_wr(SIGNAL, A) l_bool_wr_##SIGNAL(A)
#define l_u8_rd(SIGNAL) l_u8_rd_##SIGNAL()
#define l_u8_wr(SIGNAL, A) l_u8_wr_##SIGNAL(A)
#define l_u16_rd(SIGNAL) l_u16_rd_##SIGNAL()
#define l_u16_wr(SIGNAL, A) l_u16_wr_##SIGNAL(A)
#define l_bytes_rd(SIGNAL, start, count, data)  l_bytes_rd_##SIGNAL(start, count, data)
#define l_bytes_wr(SIGNAL, start, count, data) l_bytes_wr_##SIGNAL(start, count, data)
#define l_flg_tst(FLAG) l_flg_tst_##FLAG()
#define l_flg_clr(FLAG) l_flg_clr_##FLAG()
#define LIN_TEST_BIT(A,B) ((l_bool)((((A) & (1U << (B))) != 0U) ? 1U : 0U))
#define LIN_SET_BIT(A,B)                      ((A) |= (l_u8) (1U << (B)))
#define LIN_CLEAR_BIT(A,B)               ((A) &= ((l_u8) (~(1U << (B)))))
#define LIN_BYTE_MASK  ((l_u16)(((l_u16)((l_u16)1 << CHAR_BIT)) - (l_u16)1))
#define LIN_FRAME_LEN_MAX                                             10U

/* Returns the low byte of the 32-bit value    */
#define BYTE_0(n)                              ((l_u8)((n) & (l_u8)0xFF))
/* Returns the second byte of the 32-bit value */
#define BYTE_1(n)                        ((l_u8)(BYTE_0((n) >> (l_u8)8)))
/* Returns the third byte of the 32-bit value  */
#define BYTE_2(n)                       ((l_u8)(BYTE_0((n) >> (l_u8)16)))
/* Returns high byte of the 32-bit value       */
#define BYTE_3(n)                       ((l_u8)(BYTE_0((n) >> (l_u8)24)))

/*
 * defines for signal access
 */



#define LIN_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc    37U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc    0U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimRespIntrMirrDimPerc    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc    7U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc    0U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr    38U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr    0U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimRespIntrMirrIntFailr    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr    7U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr    1U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimRespResdBoolean    38U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimRespResdBoolean    1U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimRespResdBoolean    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespResdBoolean    7U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespResdBoolean    2U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimRespResdUInt6    38U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimRespResdUInt6    2U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimRespResdUInt6    6U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespResdUInt6    7U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespResdUInt6    3U


#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1    49U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1    45U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNoCmplEndSgn1    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1    8U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1    4U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1    0U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2    50U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2    46U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNoCmplEndSgn2    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2    8U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2    5U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2    1U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3    51U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3    47U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNoCmplEndSgn3    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3    8U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3    6U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3    2U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr1    45U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr1    48U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr1    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNoCmplNr1    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr1    8U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr1    0U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr1    3U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr2    46U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr2    49U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr2    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNoCmplNr2    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr2    8U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr2    1U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr2    4U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr3    47U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr3    50U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr3    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNoCmplNr3    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr3    8U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr3    2U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr3    5U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr4    48U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr4    51U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr4    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNoCmplNr4    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr4    8U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr4    3U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr4    6U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1    58U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1    53U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNo10CmplEndSgn1    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1    9U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1    5U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1    0U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2    59U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2    54U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNo10CmplEndSgn2    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2    9U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2    6U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2    1U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3    60U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3    55U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNo10CmplEndSgn3    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3    9U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3    7U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3    2U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr1    53U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr1    56U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr1    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNo10CmplNr1    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr1    9U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr1    0U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr1    3U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr2    54U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr2    57U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr2    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNo10CmplNr2    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr2    9U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr2    1U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr2    4U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr3    55U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr3    58U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr3    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNo10CmplNr3    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr3    9U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr3    2U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr3    5U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr4    56U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr4    59U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr4    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNo10CmplNr4    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr4    9U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr4    3U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr4    6U
#endif

#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr5    57U
#else
#define LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr5    60U
#endif
#define LIN_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr5    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDPartNo10CmplNr5    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr5    9U
#if (LDF3_PARTNUMBER_VERSION_SWAP==1)
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr5    4U
#else
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr5    7U
#endif


#define LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr1    61U
#define LIN_BIT_OFFSET_LI0_IRMMRMDSerNoNr1    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDSerNoNr1    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr1    10U
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr1    0U


#define LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr2    62U
#define LIN_BIT_OFFSET_LI0_IRMMRMDSerNoNr2    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDSerNoNr2    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr2    10U
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr2    1U


#define LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr3    63U
#define LIN_BIT_OFFSET_LI0_IRMMRMDSerNoNr3    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDSerNoNr3    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr3    10U
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr3    2U


#define LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr4    64U
#define LIN_BIT_OFFSET_LI0_IRMMRMDSerNoNr4    0U
#define LIN_SIGNAL_SIZE_LI0_IRMMRMDSerNoNr4    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr4    10U
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr4    3U


#define LIN_BYTE_OFFSET_LI0_ErrRespRMD    7U
#define LIN_BIT_OFFSET_LI0_ErrRespRMD    7U
#define LIN_SIGNAL_SIZE_LI0_ErrRespRMD    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_ErrRespRMD    0U
#define LIN_FLAG_BIT_OFFSET_LI0_ErrRespRMD    6U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrBri    0U
#define LIN_BIT_OFFSET_LI0_StreamingMirrBri    0U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrBri    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrBri    0U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrBri    0U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrEnable    0U
#define LIN_BIT_OFFSET_LI0_StreamingMirrEnable    4U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrEnable    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrEnable    0U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrEnable    1U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrImg    0U
#define LIN_BIT_OFFSET_LI0_StreamingMirrImg    5U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrImg    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrImg    0U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrImg    2U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrModeSts    2U
#define LIN_BIT_OFFSET_LI0_StreamingMirrModeSts    0U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrModeSts    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrModeSts    0U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrModeSts    5U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrPosn    1U
#define LIN_BIT_OFFSET_LI0_StreamingMirrPosn    1U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrPosn    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrPosn    0U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrPosn    3U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnSwtSts    1U
#define LIN_BIT_OFFSET_LI0_StreamingMirrReWrnSwtSts    5U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrReWrnSwtSts    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnSwtSts    0U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnSwtSts    4U


#define LIN_BYTE_OFFSET_LI0_CbnGroupPwrReqforRMD    10U
#define LIN_BIT_OFFSET_LI0_CbnGroupPwrReqforRMD    6U
#define LIN_SIGNAL_SIZE_LI0_CbnGroupPwrReqforRMD    2U
#define LIN_FLAG_BYTE_OFFSET_LI0_CbnGroupPwrReqforRMD    2U
#define LIN_FLAG_BIT_OFFSET_LI0_CbnGroupPwrReqforRMD    2U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrModeSwt    12U
#define LIN_BIT_OFFSET_LI0_StreamingMirrModeSwt    0U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrModeSwt    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrModeSwt    2U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrModeSwt    5U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnDis    13U
#define LIN_BIT_OFFSET_LI0_StreamingMirrReWrnDis    4U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrReWrnDis    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnDis    2U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnDis    7U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnSwt    9U
#define LIN_BIT_OFFSET_LI0_StreamingMirrReWrnSwt    7U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrReWrnSwt    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnSwt    1U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnSwt    5U


#define LIN_BYTE_OFFSET_LI0_AmbTForVisy    14U
#define LIN_BIT_OFFSET_LI0_AmbTForVisy    0U
#define LIN_SIGNAL_SIZE_LI0_AmbTForVisy    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_AmbTForVisy    3U
#define LIN_FLAG_BIT_OFFSET_LI0_AmbTForVisy    0U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSts    19U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSts    4U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafeCarModSts    3U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSts    4U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSts    1U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp    20U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp    0U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp    3U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp    4U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp    3U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeChks8    15U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeChks8    0U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafeChks8    8U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeChks8    3U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeChks8    1U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCntr4    16U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeCntr4    0U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafeCntr4    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCntr4    3U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCntr4    2U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeDataID4    16U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeDataID4    4U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafeDataID4    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeDataID4    3U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeDataID4    3U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai    17U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai    0U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafeEgyLvlElecMai    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai    3U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai    4U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp    17U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp    4U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp    3U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp    5U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts    19U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts    7U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafeFltEgyCnsWdSts    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts    4U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts    2U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai    18U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai    0U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafePwrLvlElecMai    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai    3U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai    6U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp    18U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp    4U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafePwrLvlElecSubtyp    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp    3U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp    7U


#define LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrModSts    19U
#define LIN_BIT_OFFSET_LI0_VehModMngtGlbSafePwrModSts    0U
#define LIN_SIGNAL_SIZE_LI0_VehModMngtGlbSafePwrModSts    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrModSts    4U
#define LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrModSts    0U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdDrvrSide    21U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimCmdDrvrSide    2U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimCmdDrvrSide    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdDrvrSide    5U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdDrvrSide    1U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag    21U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag    3U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag    5U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag    2U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst    21U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst    4U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimCmdIntrMirrDiagcRst    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst    5U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst    3U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty    21U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty    0U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimCmdIntrMirrDimSnvty    2U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty    5U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty    0U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna    21U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna    5U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimCmdIntrMirrEna    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna    5U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna    4U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim    21U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim    6U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimCmdIntrMirrInhbDim    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim    5U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim    5U


#define LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag    21U
#define LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag    7U
#define LIN_SIGNAL_SIZE_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag    1U
#define LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag    5U
#define LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag    6U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrBriAdjmt    29U
#define LIN_BIT_OFFSET_LI0_StreamingMirrBriAdjmt    0U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrBriAdjmt    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrBriAdjmt    6U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrBriAdjmt    0U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrImgAdjmt    29U
#define LIN_BIT_OFFSET_LI0_StreamingMirrImgAdjmt    4U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrImgAdjmt    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrImgAdjmt    6U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrImgAdjmt    1U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrPosnAdjmt    30U
#define LIN_BIT_OFFSET_LI0_StreamingMirrPosnAdjmt    0U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrPosnAdjmt    4U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrPosnAdjmt    6U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrPosnAdjmt    2U


#define LIN_BYTE_OFFSET_LI0_StreamingMirrSwt    30U
#define LIN_BIT_OFFSET_LI0_StreamingMirrSwt    4U
#define LIN_SIGNAL_SIZE_LI0_StreamingMirrSwt    2U
#define LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrSwt    6U
#define LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrSwt    3U




#define LIN_FLAG_BYTE_OFFSET_LI0_RMDZCLLin5Frame01             0
#define LIN_FLAG_BIT_OFFSET_LI0_RMDZCLLin5Frame01              0

#define LIN_FLAG_BYTE_OFFSET_LI0_ZCLZCLLin5Frame02             1
#define LIN_FLAG_BIT_OFFSET_LI0_ZCLZCLLin5Frame02              0

#define LIN_FLAG_BYTE_OFFSET_LI0_ZCLZCLLin5Frame03             3
#define LIN_FLAG_BIT_OFFSET_LI0_ZCLZCLLin5Frame03              0

#define LIN_FLAG_BYTE_OFFSET_LI0_ZCLZCLLin5Frame08             5
#define LIN_FLAG_BIT_OFFSET_LI0_ZCLZCLLin5Frame08              0

#define LIN_FLAG_BYTE_OFFSET_LI0_ZCLZCLLin5Frame09             6
#define LIN_FLAG_BIT_OFFSET_LI0_ZCLZCLLin5Frame09              0

#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMZCLLin5Frame01             7
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMZCLLin5Frame01              0

#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMZCLLin5PartNrFr01             8
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMZCLLin5PartNrFr01              0

#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMZCLLin5PartNrFr02             9
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMZCLLin5PartNrFr02              0

#define LIN_FLAG_BYTE_OFFSET_LI0_IRMMZCLLin5SerNrFr01             10
#define LIN_FLAG_BIT_OFFSET_LI0_IRMMZCLLin5SerNrFr01              0


/**********************************************************************/
/***************        Static API Functions        *******************/
/**********************************************************************/
/*
 * the static signal access macros
 */


 
/* static access macros for signal LI0_IntrMirrDimRespIntrMirrDimPerc */
 
#define l_u8_rd_LI0_IntrMirrDimRespIntrMirrDimPerc() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IntrMirrDimRespIntrMirrDimPerc(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc); \
    }


/* static access macros for signal LI0_IntrMirrDimRespIntrMirrIntFailr */

 
#define l_bool_rd_LI0_IntrMirrDimRespIntrMirrIntFailr() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr))

#define l_bool_wr_LI0_IntrMirrDimRespIntrMirrIntFailr(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr);}
/* static access macros for signal LI0_IntrMirrDimRespResdBoolean */

 
#define l_bool_rd_LI0_IntrMirrDimRespResdBoolean() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespResdBoolean], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimRespResdBoolean))

#define l_bool_wr_LI0_IntrMirrDimRespResdBoolean(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespResdBoolean], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimRespResdBoolean)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespResdBoolean], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimRespResdBoolean));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespResdBoolean],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespResdBoolean);}
 
/* static access macros for signal LI0_IntrMirrDimRespResdUInt6 */
 
#define l_u8_rd_LI0_IntrMirrDimRespResdUInt6() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespResdUInt6]) >> 2U) & 0x3fU))


#define l_u8_wr_LI0_IntrMirrDimRespResdUInt6(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespResdUInt6] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimRespResdUInt6] & 0x03U) | \
    (((A) << 2U) & 0xfcU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespResdUInt6],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespResdUInt6); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNoCmplEndSgn1 */
 
#define l_u8_rd_LI0_IRMMRMDPartNoCmplEndSgn1() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNoCmplEndSgn1(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNoCmplEndSgn2 */
 
#define l_u8_rd_LI0_IRMMRMDPartNoCmplEndSgn2() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNoCmplEndSgn2(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNoCmplEndSgn3 */
 
#define l_u8_rd_LI0_IRMMRMDPartNoCmplEndSgn3() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNoCmplEndSgn3(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNoCmplNr1 */
 
#define l_u8_rd_LI0_IRMMRMDPartNoCmplNr1() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr1]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNoCmplNr1(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr1] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr1] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr1); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNoCmplNr2 */
 
#define l_u8_rd_LI0_IRMMRMDPartNoCmplNr2() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr2]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNoCmplNr2(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr2] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr2] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr2); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNoCmplNr3 */
 
#define l_u8_rd_LI0_IRMMRMDPartNoCmplNr3() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr3]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNoCmplNr3(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr3] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr3] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr3); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNoCmplNr4 */
 
#define l_u8_rd_LI0_IRMMRMDPartNoCmplNr4() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr4]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNoCmplNr4(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr4] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr4] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr4],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr4); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNo10CmplEndSgn1 */
 
#define l_u8_rd_LI0_IRMMRMDPartNo10CmplEndSgn1() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNo10CmplEndSgn1(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNo10CmplEndSgn2 */
 
#define l_u8_rd_LI0_IRMMRMDPartNo10CmplEndSgn2() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNo10CmplEndSgn2(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNo10CmplEndSgn3 */
 
#define l_u8_rd_LI0_IRMMRMDPartNo10CmplEndSgn3() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNo10CmplEndSgn3(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNo10CmplNr1 */
 
#define l_u8_rd_LI0_IRMMRMDPartNo10CmplNr1() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr1]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNo10CmplNr1(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr1] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr1] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr1); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNo10CmplNr2 */
 
#define l_u8_rd_LI0_IRMMRMDPartNo10CmplNr2() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr2]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNo10CmplNr2(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr2] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr2] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr2); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNo10CmplNr3 */
 
#define l_u8_rd_LI0_IRMMRMDPartNo10CmplNr3() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr3]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNo10CmplNr3(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr3] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr3] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr3); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNo10CmplNr4 */
 
#define l_u8_rd_LI0_IRMMRMDPartNo10CmplNr4() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr4]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNo10CmplNr4(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr4] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr4] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr4],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr4); \
    }


 
/* static access macros for signal LI0_IRMMRMDPartNo10CmplNr5 */
 
#define l_u8_rd_LI0_IRMMRMDPartNo10CmplNr5() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr5]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDPartNo10CmplNr5(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr5] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr5] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr5],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr5); \
    }


 
/* static access macros for signal LI0_IRMMRMDSerNoNr1 */
 
#define l_u8_rd_LI0_IRMMRMDSerNoNr1() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr1]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDSerNoNr1(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr1] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr1] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr1); \
    }


 
/* static access macros for signal LI0_IRMMRMDSerNoNr2 */
 
#define l_u8_rd_LI0_IRMMRMDSerNoNr2() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr2]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDSerNoNr2(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr2] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr2] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr2); \
    }


 
/* static access macros for signal LI0_IRMMRMDSerNoNr3 */
 
#define l_u8_rd_LI0_IRMMRMDSerNoNr3() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr3]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDSerNoNr3(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr3] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr3] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr3); \
    }


 
/* static access macros for signal LI0_IRMMRMDSerNoNr4 */
 
#define l_u8_rd_LI0_IRMMRMDSerNoNr4() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr4]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_IRMMRMDSerNoNr4(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr4] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IRMMRMDSerNoNr4] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr4],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr4); \
    }


/* static access macros for signal LI0_ErrRespRMD */

 
#define l_bool_rd_LI0_ErrRespRMD() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_ErrRespRMD], \
    LIN_BIT_OFFSET_LI0_ErrRespRMD))

#define l_bool_wr_LI0_ErrRespRMD(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_ErrRespRMD], \
    LIN_BIT_OFFSET_LI0_ErrRespRMD)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_ErrRespRMD], \
    LIN_BIT_OFFSET_LI0_ErrRespRMD));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_ErrRespRMD],\
         LIN_FLAG_BIT_OFFSET_LI0_ErrRespRMD);}
 
/* static access macros for signal LI0_StreamingMirrBri */
 
#define l_u8_rd_LI0_StreamingMirrBri() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrBri]) >> 0U) & 0x0fU))


#define l_u8_wr_LI0_StreamingMirrBri(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrBri] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrBri] & 0xf0U) | \
    (((A) << 0U) & 0x0fU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrBri],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrBri); \
    }


/* static access macros for signal LI0_StreamingMirrEnable */

 
#define l_bool_rd_LI0_StreamingMirrEnable() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrEnable], \
    LIN_BIT_OFFSET_LI0_StreamingMirrEnable))

#define l_bool_wr_LI0_StreamingMirrEnable(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrEnable], \
    LIN_BIT_OFFSET_LI0_StreamingMirrEnable)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrEnable], \
    LIN_BIT_OFFSET_LI0_StreamingMirrEnable));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrEnable],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrEnable);}
 
/* static access macros for signal LI0_StreamingMirrImg */
 
#define l_u8_rd_LI0_StreamingMirrImg() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImg] + (lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImg + 1U] << 8U)) >> 5U) & 0x0fU))


#define l_u8_wr_LI0_StreamingMirrImg(A) \
    { \
    buffer_backup_data[0U] =  lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImg]; \
    lin_frame_updating_flag_tbl[LI0_RMDZCLLin5Frame01] |= (1U << 0); \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImg] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImg] & 0x1fU) | \
    (((A) << 5U) & 0xe0U)); \
    buffer_backup_data[0U + 1U] =  lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImg + 1U]; \
    lin_frame_updating_flag_tbl[LI0_RMDZCLLin5Frame01] |= (1U << (0 + 1U)); \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImg + 1U] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImg + 1U] & 0xfeU) | \
    (((A) >> 3U) & 0x01U)); \
    lin_frame_updating_flag_tbl[LI0_RMDZCLLin5Frame01] &= (~(0x03 << 0)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrImg],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrImg); \
    }


 
/* static access macros for signal LI0_StreamingMirrModeSts */
 
#define l_u8_rd_LI0_StreamingMirrModeSts() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrModeSts]) >> 0U) & 0x0fU))


#define l_u8_wr_LI0_StreamingMirrModeSts(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrModeSts] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrModeSts] & 0xf0U) | \
    (((A) << 0U) & 0x0fU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrModeSts],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrModeSts); \
    }


 
/* static access macros for signal LI0_StreamingMirrPosn */
 
#define l_u8_rd_LI0_StreamingMirrPosn() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrPosn]) >> 1U) & 0x0fU))


#define l_u8_wr_LI0_StreamingMirrPosn(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrPosn] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrPosn] & 0xe1U) | \
    (((A) << 1U) & 0x1eU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrPosn],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrPosn); \
    }


/* static access macros for signal LI0_StreamingMirrReWrnSwtSts */

 
#define l_bool_rd_LI0_StreamingMirrReWrnSwtSts() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnSwtSts], \
    LIN_BIT_OFFSET_LI0_StreamingMirrReWrnSwtSts))

#define l_bool_wr_LI0_StreamingMirrReWrnSwtSts(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnSwtSts], \
    LIN_BIT_OFFSET_LI0_StreamingMirrReWrnSwtSts)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnSwtSts], \
    LIN_BIT_OFFSET_LI0_StreamingMirrReWrnSwtSts));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnSwtSts],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnSwtSts);}
 
/* static access macros for signal LI0_CbnGroupPwrReqforRMD */
 
#define l_u8_rd_LI0_CbnGroupPwrReqforRMD() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_CbnGroupPwrReqforRMD]) >> 6U) & 0x03U))


#define l_u8_wr_LI0_CbnGroupPwrReqforRMD(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_CbnGroupPwrReqforRMD] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_CbnGroupPwrReqforRMD] & 0x3fU) | \
    (((A) << 6U) & 0xc0U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_CbnGroupPwrReqforRMD],\
         LIN_FLAG_BIT_OFFSET_LI0_CbnGroupPwrReqforRMD); \
    }


 
/* static access macros for signal LI0_StreamingMirrModeSwt */
 
#define l_u8_rd_LI0_StreamingMirrModeSwt() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrModeSwt]) >> 0U) & 0x0fU))


#define l_u8_wr_LI0_StreamingMirrModeSwt(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrModeSwt] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrModeSwt] & 0xf0U) | \
    (((A) << 0U) & 0x0fU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrModeSwt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrModeSwt); \
    }


 
/* static access macros for signal LI0_StreamingMirrReWrnDis */
 
#define l_u8_rd_LI0_StreamingMirrReWrnDis() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnDis]) >> 4U) & 0x0fU))


#define l_u8_wr_LI0_StreamingMirrReWrnDis(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnDis] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnDis] & 0x0fU) | \
    (((A) << 4U) & 0xf0U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnDis],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnDis); \
    }


/* static access macros for signal LI0_StreamingMirrReWrnSwt */

 
#define l_bool_rd_LI0_StreamingMirrReWrnSwt() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnSwt], \
    LIN_BIT_OFFSET_LI0_StreamingMirrReWrnSwt))

#define l_bool_wr_LI0_StreamingMirrReWrnSwt(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnSwt], \
    LIN_BIT_OFFSET_LI0_StreamingMirrReWrnSwt)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrReWrnSwt], \
    LIN_BIT_OFFSET_LI0_StreamingMirrReWrnSwt));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnSwt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnSwt);}
 
/* static access macros for signal LI0_AmbTForVisy */
 
#define l_u8_rd_LI0_AmbTForVisy() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_AmbTForVisy]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_AmbTForVisy(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_AmbTForVisy] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_AmbTForVisy] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_AmbTForVisy],\
         LIN_FLAG_BIT_OFFSET_LI0_AmbTForVisy); \
    }


 
/* static access macros for signal LI0_VehModMngtGlbSafeCarModSts */
 
#define l_u8_rd_LI0_VehModMngtGlbSafeCarModSts() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSts]) >> 4U) & 0x07U))


#define l_u8_wr_LI0_VehModMngtGlbSafeCarModSts(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSts] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSts] & 0x8fU) | \
    (((A) << 4U) & 0x70U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSts],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSts); \
    }


 
/* static access macros for signal LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp */
 
#define l_u8_rd_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp]) >> 0U) & 0x07U))


#define l_u8_wr_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp] & 0xf8U) | \
    (((A) << 0U) & 0x07U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp); \
    }


 
/* static access macros for signal LI0_VehModMngtGlbSafeChks8 */
 
#define l_u8_rd_LI0_VehModMngtGlbSafeChks8() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeChks8]) >> 0U) & 0xffU))


#define l_u8_wr_LI0_VehModMngtGlbSafeChks8(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeChks8] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeChks8] & 0x00U) | \
    (((A) << 0U) & 0xffU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeChks8],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeChks8); \
    }


 
/* static access macros for signal LI0_VehModMngtGlbSafeCntr4 */
 
#define l_u8_rd_LI0_VehModMngtGlbSafeCntr4() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCntr4]) >> 0U) & 0x0fU))


#define l_u8_wr_LI0_VehModMngtGlbSafeCntr4(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCntr4] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeCntr4] & 0xf0U) | \
    (((A) << 0U) & 0x0fU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCntr4],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCntr4); \
    }


 
/* static access macros for signal LI0_VehModMngtGlbSafeDataID4 */
 
#define l_u8_rd_LI0_VehModMngtGlbSafeDataID4() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeDataID4]) >> 4U) & 0x0fU))


#define l_u8_wr_LI0_VehModMngtGlbSafeDataID4(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeDataID4] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeDataID4] & 0x0fU) | \
    (((A) << 4U) & 0xf0U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeDataID4],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeDataID4); \
    }


 
/* static access macros for signal LI0_VehModMngtGlbSafeEgyLvlElecMai */
 
#define l_u8_rd_LI0_VehModMngtGlbSafeEgyLvlElecMai() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai]) >> 0U) & 0x0fU))


#define l_u8_wr_LI0_VehModMngtGlbSafeEgyLvlElecMai(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai] & 0xf0U) | \
    (((A) << 0U) & 0x0fU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai); \
    }


 
/* static access macros for signal LI0_VehModMngtGlbSafeEgyLvlElecSubtyp */
 
#define l_u8_rd_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp]) >> 4U) & 0x0fU))


#define l_u8_wr_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp] & 0x0fU) | \
    (((A) << 4U) & 0xf0U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp); \
    }


/* static access macros for signal LI0_VehModMngtGlbSafeFltEgyCnsWdSts */

 
#define l_bool_rd_LI0_VehModMngtGlbSafeFltEgyCnsWdSts() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts], \
    LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts))

#define l_bool_wr_LI0_VehModMngtGlbSafeFltEgyCnsWdSts(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts], \
    LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts], \
    LIN_BIT_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts);}
 
/* static access macros for signal LI0_VehModMngtGlbSafePwrLvlElecMai */
 
#define l_u8_rd_LI0_VehModMngtGlbSafePwrLvlElecMai() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai]) >> 0U) & 0x0fU))


#define l_u8_wr_LI0_VehModMngtGlbSafePwrLvlElecMai(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai] & 0xf0U) | \
    (((A) << 0U) & 0x0fU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai); \
    }


 
/* static access macros for signal LI0_VehModMngtGlbSafePwrLvlElecSubtyp */
 
#define l_u8_rd_LI0_VehModMngtGlbSafePwrLvlElecSubtyp() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp]) >> 4U) & 0x0fU))


#define l_u8_wr_LI0_VehModMngtGlbSafePwrLvlElecSubtyp(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp] & 0x0fU) | \
    (((A) << 4U) & 0xf0U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp); \
    }


 
/* static access macros for signal LI0_VehModMngtGlbSafePwrModSts */
 
#define l_u8_rd_LI0_VehModMngtGlbSafePwrModSts() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrModSts]) >> 0U) & 0x0fU))


#define l_u8_wr_LI0_VehModMngtGlbSafePwrModSts(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrModSts] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrModSts] & 0xf0U) | \
    (((A) << 0U) & 0x0fU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrModSts],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrModSts); \
    }


/* static access macros for signal LI0_IntrMirrDimCmdDrvrSide */

 
#define l_bool_rd_LI0_IntrMirrDimCmdDrvrSide() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdDrvrSide], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdDrvrSide))

#define l_bool_wr_LI0_IntrMirrDimCmdDrvrSide(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdDrvrSide], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdDrvrSide)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdDrvrSide], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdDrvrSide));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdDrvrSide],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdDrvrSide);}
/* static access macros for signal LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag */

 
#define l_bool_rd_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag))

#define l_bool_wr_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag);}
/* static access macros for signal LI0_IntrMirrDimCmdIntrMirrDiagcRst */

 
#define l_bool_rd_LI0_IntrMirrDimCmdIntrMirrDiagcRst() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst))

#define l_bool_wr_LI0_IntrMirrDimCmdIntrMirrDiagcRst(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst);}
 
/* static access macros for signal LI0_IntrMirrDimCmdIntrMirrDimSnvty */
 
#define l_u8_rd_LI0_IntrMirrDimCmdIntrMirrDimSnvty() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty]) >> 0U) & 0x03U))


#define l_u8_wr_LI0_IntrMirrDimCmdIntrMirrDimSnvty(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty] & 0xfcU) | \
    (((A) << 0U) & 0x03U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty); \
    }


/* static access macros for signal LI0_IntrMirrDimCmdIntrMirrEna */

 
#define l_bool_rd_LI0_IntrMirrDimCmdIntrMirrEna() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna))

#define l_bool_wr_LI0_IntrMirrDimCmdIntrMirrEna(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna);}
/* static access macros for signal LI0_IntrMirrDimCmdIntrMirrInhbDim */

 
#define l_bool_rd_LI0_IntrMirrDimCmdIntrMirrInhbDim() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim))

#define l_bool_wr_LI0_IntrMirrDimCmdIntrMirrInhbDim(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim);}
/* static access macros for signal LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag */

 
#define l_bool_rd_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag() \
    (LIN_TEST_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag))

#define l_bool_wr_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag(A) \
    {(A) ? \
    (LIN_SET_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag)):\
    (LIN_CLEAR_BIT(lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag], \
    LIN_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag));\
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag);}
 
/* static access macros for signal LI0_StreamingMirrBriAdjmt */
 
#define l_u8_rd_LI0_StreamingMirrBriAdjmt() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrBriAdjmt]) >> 0U) & 0x0fU))


#define l_u8_wr_LI0_StreamingMirrBriAdjmt(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrBriAdjmt] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrBriAdjmt] & 0xf0U) | \
    (((A) << 0U) & 0x0fU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrBriAdjmt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrBriAdjmt); \
    }


 
/* static access macros for signal LI0_StreamingMirrImgAdjmt */
 
#define l_u8_rd_LI0_StreamingMirrImgAdjmt() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImgAdjmt]) >> 4U) & 0x0fU))


#define l_u8_wr_LI0_StreamingMirrImgAdjmt(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImgAdjmt] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrImgAdjmt] & 0x0fU) | \
    (((A) << 4U) & 0xf0U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrImgAdjmt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrImgAdjmt); \
    }


 
/* static access macros for signal LI0_StreamingMirrPosnAdjmt */
 
#define l_u8_rd_LI0_StreamingMirrPosnAdjmt() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrPosnAdjmt]) >> 0U) & 0x0fU))


#define l_u8_wr_LI0_StreamingMirrPosnAdjmt(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrPosnAdjmt] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrPosnAdjmt] & 0xf0U) | \
    (((A) << 0U) & 0x0fU)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrPosnAdjmt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrPosnAdjmt); \
    }


 
/* static access macros for signal LI0_StreamingMirrSwt */
 
#define l_u8_rd_LI0_StreamingMirrSwt() \
    ((l_u8)  (((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrSwt]) >> 4U) & 0x03U))


#define l_u8_wr_LI0_StreamingMirrSwt(A) \
    { \
    lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrSwt] = \
    (l_u8)((lin_pFrameBuf[LIN_BYTE_OFFSET_LI0_StreamingMirrSwt] & 0xcfU) | \
    (((A) << 4U) & 0x30U)); \
    LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrSwt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrSwt); \
    }




/* Signal flag APIs */

#define l_flg_tst_LI0_IntrMirrDimRespIntrMirrDimPerc_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc)
#define l_flg_clr_LI0_IntrMirrDimRespIntrMirrDimPerc_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrDimPerc)

#define l_flg_tst_LI0_IntrMirrDimRespIntrMirrIntFailr_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr)
#define l_flg_clr_LI0_IntrMirrDimRespIntrMirrIntFailr_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespIntrMirrIntFailr)

#define l_flg_tst_LI0_IntrMirrDimRespResdBoolean_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespResdBoolean],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespResdBoolean)
#define l_flg_clr_LI0_IntrMirrDimRespResdBoolean_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespResdBoolean],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespResdBoolean)

#define l_flg_tst_LI0_IntrMirrDimRespResdUInt6_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespResdUInt6],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespResdUInt6)
#define l_flg_clr_LI0_IntrMirrDimRespResdUInt6_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimRespResdUInt6],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimRespResdUInt6)

#define l_flg_tst_LI0_IRMMRMDPartNoCmplEndSgn1_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1)
#define l_flg_clr_LI0_IRMMRMDPartNoCmplEndSgn1_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn1)

#define l_flg_tst_LI0_IRMMRMDPartNoCmplEndSgn2_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2)
#define l_flg_clr_LI0_IRMMRMDPartNoCmplEndSgn2_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn2)

#define l_flg_tst_LI0_IRMMRMDPartNoCmplEndSgn3_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3)
#define l_flg_clr_LI0_IRMMRMDPartNoCmplEndSgn3_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplEndSgn3)

#define l_flg_tst_LI0_IRMMRMDPartNoCmplNr1_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr1)
#define l_flg_clr_LI0_IRMMRMDPartNoCmplNr1_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr1)

#define l_flg_tst_LI0_IRMMRMDPartNoCmplNr2_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr2)
#define l_flg_clr_LI0_IRMMRMDPartNoCmplNr2_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr2)

#define l_flg_tst_LI0_IRMMRMDPartNoCmplNr3_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr3)
#define l_flg_clr_LI0_IRMMRMDPartNoCmplNr3_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr3)

#define l_flg_tst_LI0_IRMMRMDPartNoCmplNr4_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr4],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr4)
#define l_flg_clr_LI0_IRMMRMDPartNoCmplNr4_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNoCmplNr4],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNoCmplNr4)

#define l_flg_tst_LI0_IRMMRMDPartNo10CmplEndSgn1_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1)
#define l_flg_clr_LI0_IRMMRMDPartNo10CmplEndSgn1_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn1)

#define l_flg_tst_LI0_IRMMRMDPartNo10CmplEndSgn2_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2)
#define l_flg_clr_LI0_IRMMRMDPartNo10CmplEndSgn2_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn2)

#define l_flg_tst_LI0_IRMMRMDPartNo10CmplEndSgn3_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3)
#define l_flg_clr_LI0_IRMMRMDPartNo10CmplEndSgn3_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplEndSgn3)

#define l_flg_tst_LI0_IRMMRMDPartNo10CmplNr1_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr1)
#define l_flg_clr_LI0_IRMMRMDPartNo10CmplNr1_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr1)

#define l_flg_tst_LI0_IRMMRMDPartNo10CmplNr2_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr2)
#define l_flg_clr_LI0_IRMMRMDPartNo10CmplNr2_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr2)

#define l_flg_tst_LI0_IRMMRMDPartNo10CmplNr3_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr3)
#define l_flg_clr_LI0_IRMMRMDPartNo10CmplNr3_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr3)

#define l_flg_tst_LI0_IRMMRMDPartNo10CmplNr4_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr4],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr4)
#define l_flg_clr_LI0_IRMMRMDPartNo10CmplNr4_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr4],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr4)

#define l_flg_tst_LI0_IRMMRMDPartNo10CmplNr5_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr5],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr5)
#define l_flg_clr_LI0_IRMMRMDPartNo10CmplNr5_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDPartNo10CmplNr5],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDPartNo10CmplNr5)

#define l_flg_tst_LI0_IRMMRMDSerNoNr1_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr1)
#define l_flg_clr_LI0_IRMMRMDSerNoNr1_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr1],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr1)

#define l_flg_tst_LI0_IRMMRMDSerNoNr2_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr2)
#define l_flg_clr_LI0_IRMMRMDSerNoNr2_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr2],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr2)

#define l_flg_tst_LI0_IRMMRMDSerNoNr3_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr3)
#define l_flg_clr_LI0_IRMMRMDSerNoNr3_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr3],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr3)

#define l_flg_tst_LI0_IRMMRMDSerNoNr4_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr4],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr4)
#define l_flg_clr_LI0_IRMMRMDSerNoNr4_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IRMMRMDSerNoNr4],\
         LIN_FLAG_BIT_OFFSET_LI0_IRMMRMDSerNoNr4)

#define l_flg_tst_LI0_ErrRespRMD_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_ErrRespRMD],\
         LIN_FLAG_BIT_OFFSET_LI0_ErrRespRMD)
#define l_flg_clr_LI0_ErrRespRMD_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_ErrRespRMD],\
         LIN_FLAG_BIT_OFFSET_LI0_ErrRespRMD)

#define l_flg_tst_LI0_StreamingMirrBri_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrBri],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrBri)
#define l_flg_clr_LI0_StreamingMirrBri_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrBri],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrBri)

#define l_flg_tst_LI0_StreamingMirrEnable_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrEnable],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrEnable)
#define l_flg_clr_LI0_StreamingMirrEnable_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrEnable],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrEnable)

#define l_flg_tst_LI0_StreamingMirrImg_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrImg],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrImg)
#define l_flg_clr_LI0_StreamingMirrImg_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrImg],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrImg)

#define l_flg_tst_LI0_StreamingMirrModeSts_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrModeSts],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrModeSts)
#define l_flg_clr_LI0_StreamingMirrModeSts_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrModeSts],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrModeSts)

#define l_flg_tst_LI0_StreamingMirrPosn_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrPosn],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrPosn)
#define l_flg_clr_LI0_StreamingMirrPosn_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrPosn],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrPosn)

#define l_flg_tst_LI0_StreamingMirrReWrnSwtSts_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnSwtSts],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnSwtSts)
#define l_flg_clr_LI0_StreamingMirrReWrnSwtSts_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnSwtSts],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnSwtSts)

#define l_flg_tst_LI0_CbnGroupPwrReqforRMD_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_CbnGroupPwrReqforRMD],\
         LIN_FLAG_BIT_OFFSET_LI0_CbnGroupPwrReqforRMD)
#define l_flg_clr_LI0_CbnGroupPwrReqforRMD_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_CbnGroupPwrReqforRMD],\
         LIN_FLAG_BIT_OFFSET_LI0_CbnGroupPwrReqforRMD)

#define l_flg_tst_LI0_StreamingMirrModeSwt_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrModeSwt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrModeSwt)
#define l_flg_clr_LI0_StreamingMirrModeSwt_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrModeSwt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrModeSwt)

#define l_flg_tst_LI0_StreamingMirrReWrnDis_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnDis],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnDis)
#define l_flg_clr_LI0_StreamingMirrReWrnDis_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnDis],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnDis)

#define l_flg_tst_LI0_StreamingMirrReWrnSwt_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnSwt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnSwt)
#define l_flg_clr_LI0_StreamingMirrReWrnSwt_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrReWrnSwt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrReWrnSwt)

#define l_flg_tst_LI0_AmbTForVisy_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_AmbTForVisy],\
         LIN_FLAG_BIT_OFFSET_LI0_AmbTForVisy)
#define l_flg_clr_LI0_AmbTForVisy_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_AmbTForVisy],\
         LIN_FLAG_BIT_OFFSET_LI0_AmbTForVisy)

#define l_flg_tst_LI0_VehModMngtGlbSafeCarModSts_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSts],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSts)
#define l_flg_clr_LI0_VehModMngtGlbSafeCarModSts_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSts],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSts)

#define l_flg_tst_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp)
#define l_flg_clr_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCarModSubtypWdCarModSubtyp)

#define l_flg_tst_LI0_VehModMngtGlbSafeChks8_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeChks8],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeChks8)
#define l_flg_clr_LI0_VehModMngtGlbSafeChks8_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeChks8],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeChks8)

#define l_flg_tst_LI0_VehModMngtGlbSafeCntr4_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCntr4],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCntr4)
#define l_flg_clr_LI0_VehModMngtGlbSafeCntr4_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeCntr4],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeCntr4)

#define l_flg_tst_LI0_VehModMngtGlbSafeDataID4_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeDataID4],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeDataID4)
#define l_flg_clr_LI0_VehModMngtGlbSafeDataID4_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeDataID4],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeDataID4)

#define l_flg_tst_LI0_VehModMngtGlbSafeEgyLvlElecMai_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai)
#define l_flg_clr_LI0_VehModMngtGlbSafeEgyLvlElecMai_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecMai)

#define l_flg_tst_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp)
#define l_flg_clr_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeEgyLvlElecSubtyp)

#define l_flg_tst_LI0_VehModMngtGlbSafeFltEgyCnsWdSts_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts)
#define l_flg_clr_LI0_VehModMngtGlbSafeFltEgyCnsWdSts_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafeFltEgyCnsWdSts)

#define l_flg_tst_LI0_VehModMngtGlbSafePwrLvlElecMai_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai)
#define l_flg_clr_LI0_VehModMngtGlbSafePwrLvlElecMai_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecMai)

#define l_flg_tst_LI0_VehModMngtGlbSafePwrLvlElecSubtyp_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp)
#define l_flg_clr_LI0_VehModMngtGlbSafePwrLvlElecSubtyp_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrLvlElecSubtyp)

#define l_flg_tst_LI0_VehModMngtGlbSafePwrModSts_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrModSts],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrModSts)
#define l_flg_clr_LI0_VehModMngtGlbSafePwrModSts_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_VehModMngtGlbSafePwrModSts],\
         LIN_FLAG_BIT_OFFSET_LI0_VehModMngtGlbSafePwrModSts)

#define l_flg_tst_LI0_IntrMirrDimCmdDrvrSide_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdDrvrSide],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdDrvrSide)
#define l_flg_clr_LI0_IntrMirrDimCmdDrvrSide_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdDrvrSide],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdDrvrSide)

#define l_flg_tst_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag)
#define l_flg_clr_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrAsyFanCmpMag)

#define l_flg_tst_LI0_IntrMirrDimCmdIntrMirrDiagcRst_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst)
#define l_flg_clr_LI0_IntrMirrDimCmdIntrMirrDiagcRst_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDiagcRst)

#define l_flg_tst_LI0_IntrMirrDimCmdIntrMirrDimSnvty_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty)
#define l_flg_clr_LI0_IntrMirrDimCmdIntrMirrDimSnvty_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrDimSnvty)

#define l_flg_tst_LI0_IntrMirrDimCmdIntrMirrEna_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna)
#define l_flg_clr_LI0_IntrMirrDimCmdIntrMirrEna_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrEna)

#define l_flg_tst_LI0_IntrMirrDimCmdIntrMirrInhbDim_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim)
#define l_flg_clr_LI0_IntrMirrDimCmdIntrMirrInhbDim_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrInhbDim)

#define l_flg_tst_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag)
#define l_flg_clr_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag],\
         LIN_FLAG_BIT_OFFSET_LI0_IntrMirrDimCmdIntrMirrWindHeatrCmpMag)

#define l_flg_tst_LI0_StreamingMirrBriAdjmt_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrBriAdjmt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrBriAdjmt)
#define l_flg_clr_LI0_StreamingMirrBriAdjmt_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrBriAdjmt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrBriAdjmt)

#define l_flg_tst_LI0_StreamingMirrImgAdjmt_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrImgAdjmt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrImgAdjmt)
#define l_flg_clr_LI0_StreamingMirrImgAdjmt_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrImgAdjmt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrImgAdjmt)

#define l_flg_tst_LI0_StreamingMirrPosnAdjmt_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrPosnAdjmt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrPosnAdjmt)
#define l_flg_clr_LI0_StreamingMirrPosnAdjmt_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrPosnAdjmt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrPosnAdjmt)

#define l_flg_tst_LI0_StreamingMirrSwt_flag() \
         LIN_TEST_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrSwt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrSwt)
#define l_flg_clr_LI0_StreamingMirrSwt_flag() \
         LIN_CLEAR_BIT(lin_flag_handle_tbl[LIN_FLAG_BYTE_OFFSET_LI0_StreamingMirrSwt],\
         LIN_FLAG_BIT_OFFSET_LI0_StreamingMirrSwt)



/* Frame flag APIs */

   /* Interface_name = LI0 */

 #define l_flg_tst_LI0_RMDZCLLin5Frame01_flag() \
          lin_frame_flag_tbl[LI0_RMDZCLLin5Frame01]
 #define l_flg_clr_LI0_RMDZCLLin5Frame01_flag() \
          lin_frame_flag_tbl[LI0_RMDZCLLin5Frame01] = 0

 #define l_flg_tst_LI0_ZCLZCLLin5Frame02_flag() \
          lin_frame_flag_tbl[LI0_ZCLZCLLin5Frame02]
 #define l_flg_clr_LI0_ZCLZCLLin5Frame02_flag() \
          lin_frame_flag_tbl[LI0_ZCLZCLLin5Frame02] = 0

 #define l_flg_tst_LI0_ZCLZCLLin5Frame03_flag() \
          lin_frame_flag_tbl[LI0_ZCLZCLLin5Frame03]
 #define l_flg_clr_LI0_ZCLZCLLin5Frame03_flag() \
          lin_frame_flag_tbl[LI0_ZCLZCLLin5Frame03] = 0

 #define l_flg_tst_LI0_ZCLZCLLin5Frame08_flag() \
          lin_frame_flag_tbl[LI0_ZCLZCLLin5Frame08]
 #define l_flg_clr_LI0_ZCLZCLLin5Frame08_flag() \
          lin_frame_flag_tbl[LI0_ZCLZCLLin5Frame08] = 0

 #define l_flg_tst_LI0_ZCLZCLLin5Frame09_flag() \
          lin_frame_flag_tbl[LI0_ZCLZCLLin5Frame09]
 #define l_flg_clr_LI0_ZCLZCLLin5Frame09_flag() \
          lin_frame_flag_tbl[LI0_ZCLZCLLin5Frame09] = 0

 #define l_flg_tst_LI0_IRMMZCLLin5Frame01_flag() \
          lin_frame_flag_tbl[LI0_IRMMZCLLin5Frame01]
 #define l_flg_clr_LI0_IRMMZCLLin5Frame01_flag() \
          lin_frame_flag_tbl[LI0_IRMMZCLLin5Frame01] = 0

 #define l_flg_tst_LI0_IRMMZCLLin5PartNrFr01_flag() \
          lin_frame_flag_tbl[LI0_IRMMZCLLin5PartNrFr01]
 #define l_flg_clr_LI0_IRMMZCLLin5PartNrFr01_flag() \
          lin_frame_flag_tbl[LI0_IRMMZCLLin5PartNrFr01] = 0

 #define l_flg_tst_LI0_IRMMZCLLin5PartNrFr02_flag() \
          lin_frame_flag_tbl[LI0_IRMMZCLLin5PartNrFr02]
 #define l_flg_clr_LI0_IRMMZCLLin5PartNrFr02_flag() \
          lin_frame_flag_tbl[LI0_IRMMZCLLin5PartNrFr02] = 0

 #define l_flg_tst_LI0_IRMMZCLLin5SerNrFr01_flag() \
          lin_frame_flag_tbl[LI0_IRMMZCLLin5SerNrFr01]
 #define l_flg_clr_LI0_IRMMZCLLin5SerNrFr01_flag() \
          lin_frame_flag_tbl[LI0_IRMMZCLLin5SerNrFr01] = 0

 #define l_flg_tst_LI0_MasterReq_flag() \
          lin_frame_flag_tbl[LI0_MasterReq]
 #define l_flg_clr_LI0_MasterReq_flag() \
          lin_frame_flag_tbl[LI0_MasterReq] = 0

 #define l_flg_tst_LI0_SlaveResp_flag() \
          lin_frame_flag_tbl[LI0_SlaveResp]
 #define l_flg_clr_LI0_SlaveResp_flag() \
          lin_frame_flag_tbl[LI0_SlaveResp] = 0



/* INTERFACE MANAGEMENT */

#define l_ifc_init_LI0() l_ifc_init(LI0)



#define l_ifc_wake_up_LI0() l_ifc_wake_up(LI0)



#define l_ifc_rx_LI0() l_ifc_rx(LI0)



#define l_ifc_tx_LI0() l_ifc_tx(LI0)



#define l_ifc_aux_LI0() l_ifc_aux(LI0)



#define l_ifc_read_status_LI0() l_ifc_read_status(LI0)


#endif    /* _LIN_CFG_H_ */