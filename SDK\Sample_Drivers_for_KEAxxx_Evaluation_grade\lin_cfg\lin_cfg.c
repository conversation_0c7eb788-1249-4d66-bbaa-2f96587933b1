/******************************************************************************
* 
* Freescale Semiconductor Inc.
* (c) Copyright 2013-2016 Freescale Semiconductor, Inc.
* Copyright 2016-2024 NXP
* ALL RIGHTS RESERVED.
* 
****************************************************************************//*!
*
* @file      lin_cfg.c
*
* <AUTHOR> Software
*
* @version   1.0
*
* @date      Wed Nov 13 10:32:04 CST 2024
*
* @brief     Common LIN configuration, data structure
*
******************************************************************************/
#include "lin_cfg.h"
#include "lin.h"
/* Mapping interface with hardware */
const lin_hardware_name lin_virtual_ifc = LINUART2;
l_u8 lin_lld_response_buffer[10];
l_u8 lin_successful_transfer;
l_u8 lin_error_in_response;
l_u8 lin_goto_sleep_flg;
/* Save configuration flag */
l_u8 lin_save_configuration_flg = 0;
lin_word_status_str lin_word_status;
l_u8 lin_current_pid;
unsigned char lin_awake_up_flag;

const l_signal_handle LI0_response_error_signal = LI0_ErrRespRMD;

volatile l_u8 buffer_backup_data[8];

/* definition and initialization of signal array */
l_u8    lin_pFrameBuf[LIN_FRAME_BUF_SIZE] =
{


  0x1a /* 0 : 00011010 */ /* start of frame LI0_RMDZCLLin5Frame01 */

  ,0xc6 /* 1 : 11000110 */
  ,0xf0 /* 2 : 11110000 */
  ,0xff /* 3 : 11111111 */
  ,0xff /* 4 : 11111111 */
  ,0xff /* 5 : 11111111 */
  ,0xff /* 6 : 11111111 */
  ,0x7f /* 7 : 01111111 */


  ,0xff /* 8 : 11111111 */ /* start of frame LI0_ZCLZCLLin5Frame02 */

  ,0x7f /* 9 : 01111111 */
  
  ,0x3f /* 10 : 00111111 */
  
  ,0xff /* 11 : 11111111 */
  
  ,0xf0 /* 12 : 11110000 */
  
  ,0x0f /* 13 : 00001111 */
  

  ,0x00 /* 14 : 00000000 */ /* start of frame LI0_ZCLZCLLin5Frame03 */

  ,0x00 /* 15 : 00000000 */
  
  ,0x00 /* 16 : 00000000 */
  
  ,0x00 /* 17 : 00000000 */
  
  ,0x00 /* 18 : 00000000 */
  
  ,0x02 /* 19 : 00000010 */
  
  ,0xf8 /* 20 : 11111000 */
  

  ,0x10 /* 21 : 00010000 */ /* start of frame LI0_ZCLZCLLin5Frame08 */

  ,0xff /* 22 : 11111111 */
  
  ,0xff /* 23 : 11111111 */
  
  ,0xff /* 24 : 11111111 */
  
  ,0xff /* 25 : 11111111 */
  
  ,0xff /* 26 : 11111111 */
  
  ,0xff /* 27 : 11111111 */
  
  ,0xff /* 28 : 11111111 */
  

  ,0x0a /* 29 : 00001010 */ /* start of frame LI0_ZCLZCLLin5Frame09 */

  ,0xd3 /* 30 : 11010011 */
  
  ,0xff /* 31 : 11111111 */
  
  ,0xff /* 32 : 11111111 */
  
  ,0xff /* 33 : 11111111 */
  
  ,0xff /* 34 : 11111111 */
  
  ,0xff /* 35 : 11111111 */
  
  ,0xff /* 36 : 11111111 */
  

  ,0x00 /* 37 : 00000000 */ /* start of frame LI0_IRMMZCLLin5Frame01 */

  ,0x00 /* 38 : 00000000 */
  
  ,0xff /* 39 : 11111111 */
  
  ,0xff /* 40 : 11111111 */
  
  ,0xff /* 41 : 11111111 */
  
  ,0xff /* 42 : 11111111 */
  
  ,0xff /* 43 : 11111111 */
  
  ,0xff /* 44 : 11111111 */
  

  ,0x00 /* 45 : 00000000 */ /* start of frame LI0_IRMMZCLLin5PartNrFr01 */

  ,0x00 /* 46 : 00000000 */
  
  ,0x00 /* 47 : 00000000 */
  
  ,0x00 /* 48 : 00000000 */
  
  ,0x00 /* 49 : 00000000 */
  
  ,0x00 /* 50 : 00000000 */
  
  ,0x00 /* 51 : 00000000 */
  
  ,0xff /* 52 : 11111111 */
  

  ,0x00 /* 53 : 00000000 */ /* start of frame LI0_IRMMZCLLin5PartNrFr02 */

  ,0x00 /* 54 : 00000000 */
  
  ,0x00 /* 55 : 00000000 */
  
  ,0x00 /* 56 : 00000000 */
  
  ,0x00 /* 57 : 00000000 */
  
  ,0x00 /* 58 : 00000000 */
  
  ,0x00 /* 59 : 00000000 */
  
  ,0x00 /* 60 : 00000000 */
  

  ,0x00 /* 61 : 00000000 */ /* start of frame LI0_IRMMZCLLin5SerNrFr01 */

  ,0x00 /* 62 : 00000000 */
  
  ,0x00 /* 63 : 00000000 */
  
  ,0x00 /* 64 : 00000000 */
  
  ,0xff /* 65 : 11111111 */
  
  ,0xff /* 66 : 11111111 */
  
  ,0xff /* 67 : 11111111 */
  
  ,0xff /* 68 : 11111111 */
  
};

/* definition and initialization of signal array */
l_u8    lin_flag_handle_tbl[LIN_FLAG_BUF_SIZE] =
{


  0xFF /* 0: start of flag frame LI0_RMDZCLLin5Frame01 */



  ,0xFF /* 1: start of flag frame LI0_ZCLZCLLin5Frame02 */

  ,0xFF /* 2: */
  

  ,0xFF /* 3: start of flag frame LI0_ZCLZCLLin5Frame03 */

  ,0xFF /* 4: */
  

  ,0xFF /* 5: start of flag frame LI0_ZCLZCLLin5Frame08 */


  ,0xFF /* 6: start of flag frame LI0_ZCLZCLLin5Frame09 */


  ,0xFF /* 7: start of flag frame LI0_IRMMZCLLin5Frame01 */


  ,0xFF /* 8: start of flag frame LI0_IRMMZCLLin5PartNrFr01 */


  ,0xFF /* 9: start of flag frame LI0_IRMMZCLLin5PartNrFr02 */


  ,0xFF /* 10: start of flag frame LI0_IRMMZCLLin5SerNrFr01 */

};

/*************************** Flag set when signal is updated ******************/
/* Diagnostic signal */
l_u8 lin_diag_signal_tbl[16];
/*****************************event trigger frame*****************************/

/**********************************  Frame table **********************************/
const lin_frame_struct lin_frame_tbl[LIN_NUM_OF_FRMS] ={

    { LIN_FRM_UNCD, 8, LIN_RES_PUB, 0, 0, 1  , (l_u8*)&LI0_response_error_signal  }

   ,{ LIN_FRM_UNCD, 6, LIN_RES_SUB, 8, 1, 2 , (l_u8*)0 }
  
   ,{ LIN_FRM_UNCD, 7, LIN_RES_SUB, 14, 3, 2 , (l_u8*)0 }
  
   ,{ LIN_FRM_UNCD, 8, LIN_RES_SUB, 21, 5, 1 , (l_u8*)0 }
  
   ,{ LIN_FRM_UNCD, 8, LIN_RES_SUB, 29, 6, 1 , (l_u8*)0 }
  
   ,{ LIN_FRM_UNCD, 8, LIN_RES_PUB, 37, 7, 1 , (l_u8*)0 }
  
   ,{ LIN_FRM_UNCD, 8, LIN_RES_PUB, 45, 8, 1 , (l_u8*)0 }
  
   ,{ LIN_FRM_UNCD, 8, LIN_RES_PUB, 53, 9, 1 , (l_u8*)0 }
  
   ,{ LIN_FRM_UNCD, 8, LIN_RES_PUB, 61, 10, 1 , (l_u8*)0 }
  
   ,{ LIN_FRM_DIAG, 8, LIN_RES_SUB, 0, 0, 0 , (l_u8*)0 }
  
   ,{ LIN_FRM_DIAG, 8, LIN_RES_PUB, 0, 0, 0 , (l_u8*)0 }
  
};

/*********************************** Frame flag Initialization **********************/
/*************************** Frame flag for send/receive successfully ***************/
l_bool lin_frame_flag_tbl[LIN_NUM_OF_FRMS] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
/*************************** Frame flag for updating signal in frame ****************/
volatile l_u8 lin_frame_updating_flag_tbl[LIN_NUM_OF_FRMS] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};


/**************************** Lin configuration Initialization ***********************/
/* max_response_frame_timeout = round((1.4x(10+Nx10)xTbit)/Tbase_period) + 3 */

const l_u16 lin_max_frame_res_timeout_val[8]={

6, 7, 9, 10, 12, 18, 24,26 //13, 22, 24

};


l_u8 lin_configuration_RAM[LIN_SIZE_OF_CFG]= {0x00, 0x2D, 0x05, 0x07, 0x2A, 0x2B, 0x29, 0x0B, 0x0C, 0x0D, 0x3C, 0x3D ,0xFF};


const l_u16  lin_configuration_ROM[LIN_SIZE_OF_CFG]= {0x00, 0x2D, 0x05, 0x07, 0x2A, 0x2B, 0x29, 0x0B, 0x0C, 0x0D, 0x3C, 0x3D ,0xFFFF};

/***************************************** Node Attribute*****************************************/

l_u8 lin_configured_NAD = 0x7C;    /*<configured_NAD>*/
const l_u8 lin_initial_NAD    =0x7C;    /*<initial_NAD>*/
const lin_product_id product_id = {0x0000, 0x0000, 0x0000 };  /* {<supplier_id>,<function_id>,<variant>} */
const l_signal_handle response_error =  LI0_ErrRespRMD;
const l_u8 num_frame_have_esignal = 1;                                 /*number of frame contain error signal*/
const l_u16 lin_response_error_byte_offset[1] = {LIN_BYTE_OFFSET_LI0_ErrRespRMD};                  /*<interface_name>_< response_error>*/
const l_u8 lin_response_error_bit_offset[1] = {LIN_BIT_OFFSET_LI0_ErrRespRMD};                  /*<interface_name>_< response_error>*/



/************************** TL Layer and Diagnostic: SINGLE interface **************************/
lin_tl_pdu_data tx_single_pdu_data = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
lin_tl_pdu_data rx_single_pdu_data = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};








/****************************Support SID Initialization ***********************/

const l_u8 lin_diag_services_supported[_DIAG_NUMBER_OF_SERVICES_] = {0xB2,0xB7,0xB0,0xB3,0xB6};
l_u8 lin_diag_services_flag[_DIAG_NUMBER_OF_SERVICES_] = {0,0,0,0,0};

lin_tl_pdu_data *tl_current_tx_pdu_ptr;
lin_tl_pdu_data *tl_current_rx_pdu_ptr;
l_u8 tl_slaveresp_cnt = 0;
/*This ld_read_by_id_callout() function is used when the master node transmits a read by
 identifier request with an identifier in the user defined area (id from 32 to 63).
 The driver will call this function when such request is received.
 * id: the identifier in the user defined area (32 to 63)
 * data: pointer points to a data area with 5 bytes, used to give the positive response.
  Driver uses 0xFF "do not care value" for unassigned data values.
  Data length in PCI is (1 + number of assigned meaningful data values).
  Driver will take as data for all data before and including the last value in the frame that is different from 0xFF.
  PCI is 0x02-0x06, so data should have at least one value different from 0xFF.
  For example, a response frame, (NAD) (PCI) (0xF2) (0xFF) (0x00) (0xFF) (0xFF) (0xFF),
  PCI will be 0x03, since in this case driver takes all data before 0x00 and 0x00 as meaningful data,
  and values after 0x00 are do not care value.
 * return: LD_NEGATIVE_RESPONSE Respond with a negative response.
           LD_POSTIVE_RESPONSE Respond with a positive response.
           LD_ID_NO_RESPONSE The slave node will not answer.
 */
l_u8 ld_read_by_id_callout(l_u8 id, l_u8 *data)
{
    l_u8 retval = LD_NEGATIVE_RESPONSE;
    /* Following code is an example - Real implementation is application-dependent */
    /* This example implement with ID = 32 - LIN_READ_USR_DEF_MIN */
    if (id == LIN_READ_USR_DEF_MIN)
    {
      /* id received is user defined 32 */
      data[0] = (l_u8) (id + 1);    /* Data user define */
      data[1] = (l_u8) (id + 2);    /* Data user define */
      data[2] = (l_u8) (id + 3);    /* Data user define */
      data[3] = (l_u8) (id + 4);    /* Data user define */
      data[4] = (l_u8) (id + 5);    /* Data user define */
      retval = LD_POSITIVE_RESPONSE;
    }
    else
    {
      /* other identifiers, respond with negative response by default*/
    }
    return retval;
}