/**
* @file		<FILE>
* <AUTHOR>
* @date		<COMMITTERDATE>
* @version	<BRANCH>
*/

#ifndef SPI_NOR_FLASH_LIST_H_
#define SPI_NOR_FLASH_LIST_H_

/* TODO: create suppoort list for NOR Flash */
/* TODO: read SFDP registers? */
// JEDEC ID
#define ISSI_MFID           0x9D
/* QE is bit 6 of Status Register. where 1=Quad Enable or 0=not Quad Enable.
 * Write using instruction WRSR (01h) and instruction RDSR (05h) for read.
 */
#define IS25LP256D_ID       0x6019
#define IS25WP256D_ID       0x7019

/* Macronix International */
#define MXIC_MFID           0xC2
/* QE is bit 6 of Status Register. where 1=Quad Enable or 0=not Quad Enable.
 * Write using instruction WRSR (01h) and instruction RDSR (05h) for read.
 */
#define MX25L1635D_ID       0x2415
#define MX25L3233F_ID       0x2016
#define MX25L6433F_ID       0x2017
#define MX25L12845G_ID      0x2018

/* Winbond */
#define WINBOND_MFID        0xEF
/* QE is bit 1 of the Configuration Register 1 and read using instruction 35h.
 * QE is set via Write Status instruction 01h with two data bytes where bit 1 of the second byte is one.
 * It is cleared via Write Status with two data bytes where bit 1 of the second byte is zero.
 * or via Write Status 2 instruction 35h with 1 data bytes where bit 1
 */
#define W25Q16JV_ID         0x4015
#define W25Q32JV_ID         0x4016
#define W25Q64JV_ID         0x4017  // QPI
#define W25Q128JV_ID        0x4018
#define W25Q256JV_ID        0x4019
#define W25Q64FV_ID         0x6017  // QPI
#define W25Q64JVDTR_ID      0x7017  // DTR
#define W25M512JV_ID        0x7119  // doesn't need quad enable? S9 bit is reserved

/* GD */
#define GD_MFID             0xC8
/* The Write Status Register (WRSR) command has no effect on 
 * S23, S20, S19, S18, S17, S16, S15, S10, S1 and S0 of the Status Register.
 * QE is set only via Write Status 2 instruction 35h with 1 data bytes where bit 1.
 */
#define GD25Q32C_ID         0x4016

/* Cypress FL-L family */
#define CYP_MFID	        0x01
/* QE is bit 1 of the Configuration Register 1 and read using instruction 35h.
 * QE is set via Write Status instruction 01h with two data bytes where bit 1 of the second byte is one.
 * It is cleared via Write Status with two data bytes where bit 1 of the second byte is zero.
 */
#define S25FL064L_ID	    0x6017

/* Elite Semiconductor Memory */
#define EON_MFID	        0x1C
/* It seems like no need to configure QE for run Quad I/O.
 */
#define EN25QH64A_ID	    0x7017

#endif  /* SPI_NOR_FLASH_LIST_H_ */
