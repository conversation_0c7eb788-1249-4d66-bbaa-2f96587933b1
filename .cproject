<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.freescale.s32ds.cdt.core.errorParsers.S32DSGNULinkerErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="com.freescale.s32ds.cross.gnu.arm.cortexm.buildArtefact.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=com.freescale.s32ds.cross.gnu.arm.cortexm.buildArtefact.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="${cross_rm}" description="" id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.debug.**********" name="Debug" parent="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.debug">
					<folderInfo id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.debug.**********." name="/" resourcePath="">
						<toolChain id="com.freescale.s32ds.cross.gnu.arm.cortexm.toolChain.debug.836800701" name="Standard S32DS toolchain for ARM" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.toolChain.debug">
							<option defaultValue="true" id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.addtools.printsize.1579785241" name="Print size" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.addtools.printsize" valueType="boolean"/>
							<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.mcpu.807584994" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.mcpu" value="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
							<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.libraries.1619337830" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.libraries" value="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.libraries.ewl_c_noio" valueType="enumerated"/>
							<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.sysroot.1568183707" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.sysroot" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
							<option id="com.freescale.s32ds.cross.gnu.option.command.c.13736655" name="C compiler" superClass="com.freescale.s32ds.cross.gnu.option.command.c" value="gcc" valueType="string"/>
							<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.addtools.createflash.1660025212" name="Create flash image" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.addtools.createflash" value="true" valueType="boolean"/>
							<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.addtools.createlisting.1085431311" name="Create extended listing" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.addtools.createlisting" value="true" valueType="boolean"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.targetPlatform.gnu.cross.694330053" isAbstract="false" osList="all" superClass="cdt.managedbuild.targetPlatform.gnu.cross"/>
							<builder buildPath="${workspace_loc:/S9KEAZN64}/Debug" id="com.freescale.s32ds.cross.gnu.builder.396854406" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="FSL Make Builder" superClass="com.freescale.s32ds.cross.gnu.builder"/>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.262660008" name="Standard S32DS C Compiler" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler">
								<option defaultValue="gnu.c.optimization.level.none" id="gnu.c.compiler.option.optimization.level.185275069" name="Optimization Level" superClass="gnu.c.compiler.option.optimization.level" useByScannerDiscovery="false" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.debugging.level.806332927" name="Debug Level" superClass="gnu.c.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.optimization.functionsections.204857390" name="Function sections (-ffunction-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.optimization.functionsections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.optimization.datasections.314737365" name="Data sections (-fdata-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.optimization.datasections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.debugging.format.2119587522" name="Debug format" superClass="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.debugging.format" useByScannerDiscovery="true"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.mcpu.859616645" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.mcpu" useByScannerDiscovery="true" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.libraries.1434430889" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.libraries" useByScannerDiscovery="false" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.libraries.ewl_c_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.sysroot.917931236" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.sysroot" useByScannerDiscovery="false" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
								<option id="gnu.c.compiler.option.include.paths.1360639003" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/headers&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/LIN_Driver&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/LIN_Driver/bsp/UART&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/LIN_Driver/lowlevel&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/tp6816_no_mcu/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/lin_cfg&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/LIN_Driver/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.742263047" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.69362544" name="Standard S32DS C++ Compiler" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler">
								<option id="gnu.cpp.compiler.option.optimization.level.489371722" name="Optimization Level" superClass="gnu.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.debugging.level.213779653" name="Debug Level" superClass="gnu.cpp.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.debugging.level.max" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.optimization.functionsections.959616365" name="Function sections (-ffunction-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.optimization.functionsections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.optimization.datasections.2028183367" name="Data sections (-fdata-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.optimization.datasections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.debugging.format.2011417361" name="Debug format" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.debugging.format" useByScannerDiscovery="true"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.mcpu.146775541" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.mcpu" useByScannerDiscovery="true" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.libraries.26907602" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.libraries" useByScannerDiscovery="false" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.libraries.ewl_c_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.sysroot.892961238" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.sysroot" useByScannerDiscovery="false" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
								<option id="gnu.cpp.compiler.option.include.paths.880995361" name="Include paths (-I)" superClass="gnu.cpp.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C++/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C++/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/headers&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/bsp/UART&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/lin_cfg&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/lowlevel&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver&quot;"/>
								</option>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.1235563186" name="Standard S32DS C Linker" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker">
								<option id="com.freescale.s32ds.cross.gnu.tool.c.linker.option.gcsections.805630499" name="Remove unused sections (-Xlinker --gc-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.c.linker.option.gcsections" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.c.linker.option.scriptfile.258794637" name="Script files (-T)" superClass="com.freescale.s32ds.cross.gnu.tool.c.linker.option.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/Project_Settings/Linker_Files/SKEAZ_flash.ld&quot;"/>
								</option>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.mcpu.1924886245" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.mcpu" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.libraries.1844959001" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.libraries" useByScannerDiscovery="false" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.libraries.ewl_c_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.sysroot.1354789610" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.sysroot" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
								<inputType id="com.freescale.s32ds.cross.gnu.tool.c.linker.inputType.scriptfile.1588740055" superClass="com.freescale.s32ds.cross.gnu.tool.c.linker.inputType.scriptfile"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.1624501452" name="Standard S32DS C++ Linker" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker">
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.linker.option.gcsections.547986672" name="Remove unused sections (-Xlinker --gc-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.linker.option.gcsections" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.linker.option.scriptfile.578672247" name="Script files (-T)" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.linker.option.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/Project_Settings/Linker_Files/SKEAZ_flash.ld&quot;"/>
								</option>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.mcpu.2128665416" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.mcpu" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.libraries.1821098436" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.libraries" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.libraries.ewl_c_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.sysroot.499643930" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.sysroot" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.archiver.1680617315" name="Standard S32DS Archiver" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.archiver"/>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.1881197945" name="Standard S32DS Assembler" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler">
								<option id="com.freescale.s32ds.cross.gnu.tool.assembler.usepreprocessor.1513986825" name="Use preprocessor" superClass="com.freescale.s32ds.cross.gnu.tool.assembler.usepreprocessor" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.assembler.option.debugging.level.249444859" name="Debug Level" superClass="com.freescale.s32ds.cross.gnu.tool.assembler.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.mcpu.339735255" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.mcpu" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.libraries.2064525606" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.libraries" useByScannerDiscovery="false" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.libraries.ewl_c_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.sysroot.1544812711" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.sysroot" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
								<option id="gnu.both.asm.option.include.paths.1421383587" name="Include paths (-I)" superClass="gnu.both.asm.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;"/>
								</option>
								<option id="com.freescale.s32ds.cross.gnu.tool.assembler.option.defs.1131390143" name="Defined symbols (-D)" superClass="com.freescale.s32ds.cross.gnu.tool.assembler.option.defs" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="__START=__thumb_startup"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.830624638" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
								<inputType id="com.freescale.s32ds.cross.gnu.tool.assembler.inputType.asmfile.728728128" superClass="com.freescale.s32ds.cross.gnu.tool.assembler.inputType.asmfile"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.createflash.648499223" name="Standard S32DS Create Flash Image" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.createflash">
								<option id="com.freescale.s32ds.cross.gnu.option.createflash.textsection.791541422" name="Section: -j .text" superClass="com.freescale.s32ds.cross.gnu.option.createflash.textsection" useByScannerDiscovery="false" value="false" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.option.createflash.choice.353249292" name="Output file format (-O)" superClass="com.freescale.s32ds.cross.gnu.option.createflash.choice" useByScannerDiscovery="false" value="com.freescale.s32ds.cross.gnu.option.createflash.choice.binary" valueType="enumerated"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.createlisting.2119392383" name="Standard S32DS Create Listing" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.createlisting">
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.source.700178252" name="Display source (--source|-S)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.source" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.allheaders.1728097202" name="Display all headers (--all-headers|-x)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.allheaders" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.demangle.677146855" name="Demangle names (--demangle|-C)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.demangle" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.linenumbers.1249708946" name="Display line numbers (--line-numbers|-l)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.linenumbers" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.wide.1030376398" name="Wide lines (--wide|-w)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.wide" value="true" valueType="boolean"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.printsize.256219251" name="Standard S32DS Print Size" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.printsize">
								<option id="com.freescale.s32ds.cross.gnu.option.printsize.format.1841600327" name="Size format" superClass="com.freescale.s32ds.cross.gnu.option.printsize.format"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.c.preprocessor.1232028977" name="Standard S32DS C Preprocessor" superClass="com.freescale.s32ds.cross.gnu.c.preprocessor"/>
							<tool id="com.freescale.s32ds.cross.gnu.cpp.preprocessor.1557594522" name="Standard S32DS C++ Preprocessor" superClass="com.freescale.s32ds.cross.gnu.cpp.preprocessor"/>
							<tool id="com.freescale.s32ds.cross.gnu.disassembler.1990215469" name="Standard S32DS Disassembler" superClass="com.freescale.s32ds.cross.gnu.disassembler"/>
						</toolChain>
					</folderInfo>
					<fileInfo id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.debug.**********.Project_Settings/Debugger" name="Debugger" rcbsApplicability="disable" resourcePath="Project_Settings/Debugger" toolsToInvoke=""/>
					<fileInfo id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.debug.**********.Project_Settings/Linker_Files" name="Linker_Files" rcbsApplicability="disable" resourcePath="Project_Settings/Linker_Files" toolsToInvoke=""/>
					<sourceEntries>
						<entry flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name="EC"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="KEAZ128"/>
						<entry excluding="Debugger|Linker_Files" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Project_Settings"/>
						<entry excluding="bsp/AUTOBAUD" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/LIN_Driver"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/headers"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/lin_cfg"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="SDK/Sample_Drivers_for_KEAxxx_Evaluation_grade/sources"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="include"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="src"/>
						<entry excluding="src/panel_if_init.c|src/l601_8_init.c" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="tp6816_no_mcu"/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.release.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.release.**********" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.freescale.s32ds.cdt.core.errorParsers.S32DSGNULinkerErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="com.freescale.s32ds.cross.gnu.arm.cortexm.buildArtefact.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=com.freescale.s32ds.cross.gnu.arm.cortexm.buildArtefact.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="${cross_rm}" description="" id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.release.**********" name="Release" parent="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.release">
					<folderInfo id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.release.**********." name="/" resourcePath="">
						<toolChain id="com.freescale.s32ds.cross.gnu.arm.cortexm.toolChain.release.1265335334" name="Standard S32DS toolchain for ARM" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.toolChain.release">
							<option defaultValue="true" id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.addtools.printsize.696392552" name="Print size" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.addtools.printsize" valueType="boolean"/>
							<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.mcpu.1935112229" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.mcpu" value="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
							<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.libraries.1030361328" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.libraries" value="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.libraries.ewl_c9x_noio" valueType="enumerated"/>
							<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.sysroot.866363900" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.option.target.sysroot" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.targetPlatform.gnu.cross.1848881102" isAbstract="false" osList="all" superClass="cdt.managedbuild.targetPlatform.gnu.cross"/>
							<builder buildPath="${workspace_loc:/S9KEAZN64}/Release" id="com.freescale.s32ds.cross.gnu.builder.1096827601" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="FSL Make Builder" superClass="com.freescale.s32ds.cross.gnu.builder"/>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.1815165407" name="Standard S32DS C Compiler" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler">
								<option defaultValue="gnu.c.optimization.level.most" id="gnu.c.compiler.option.optimization.level.316820421" name="Optimization Level" superClass="gnu.c.compiler.option.optimization.level" useByScannerDiscovery="false" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.debugging.level.1721609902" name="Debug Level" superClass="gnu.c.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.optimization.functionsections.1576174474" name="Function sections (-ffunction-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.optimization.functionsections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.optimization.datasections.1432839000" name="Data sections (-fdata-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.optimization.datasections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.debugging.format.1235488838" name="Debug format" superClass="com.freescale.s32ds.cross.gnu.tool.c.compiler.option.debugging.format" useByScannerDiscovery="true"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.mcpu.1381348444" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.mcpu" useByScannerDiscovery="true" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.libraries.693665550" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.libraries" useByScannerDiscovery="false" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.libraries.ewl_c9x_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.sysroot.842908525" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.option.target.sysroot" useByScannerDiscovery="false" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
								<option id="gnu.c.compiler.option.include.paths.839931679" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/headers&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/bsp/UART&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/lin_cfg&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/lowlevel&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.1856227455" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.323605621" name="Standard S32DS C++ Compiler" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler">
								<option id="gnu.cpp.compiler.option.optimization.level.646645146" name="Optimization Level" superClass="gnu.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.most" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.debugging.level.1208531774" name="Debug Level" superClass="gnu.cpp.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.debugging.level.none" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.optimization.functionsections.1106853930" name="Function sections (-ffunction-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.optimization.functionsections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.optimization.datasections.682970494" name="Data sections (-fdata-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.optimization.datasections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.debugging.format.1351032819" name="Debug format" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.compiler.option.debugging.format" useByScannerDiscovery="true"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.mcpu.1751153039" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.mcpu" useByScannerDiscovery="true" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.libraries.1648265447" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.libraries" useByScannerDiscovery="false" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.libraries.ewl_c9x_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.sysroot.408874614" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.compiler.option.target.sysroot" useByScannerDiscovery="false" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
								<option id="gnu.cpp.compiler.option.include.paths.1428159309" name="Include paths (-I)" superClass="gnu.cpp.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/headers&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/bsp/UART&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/lin_cfg&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/lowlevel&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver&quot;"/>
								</option>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.1487763498" name="Standard S32DS C Linker" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker">
								<option id="com.freescale.s32ds.cross.gnu.tool.c.linker.option.gcsections.37925590" name="Remove unused sections (-Xlinker --gc-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.c.linker.option.gcsections" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.c.linker.option.scriptfile.1100027691" name="Script files (-T)" superClass="com.freescale.s32ds.cross.gnu.tool.c.linker.option.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/Project_Settings/Linker_Files/SKEAZ_flash.ld&quot;"/>
								</option>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.mcpu.1022239214" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.mcpu" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.libraries.1698329312" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.libraries" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.libraries.ewl_c9x_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.sysroot.2100316811" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.linker.option.target.sysroot" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
								<inputType id="com.freescale.s32ds.cross.gnu.tool.c.linker.inputType.scriptfile.1711680153" superClass="com.freescale.s32ds.cross.gnu.tool.c.linker.inputType.scriptfile"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.777451695" name="Standard S32DS C++ Linker" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker">
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.linker.option.gcsections.1886589994" name="Remove unused sections (-Xlinker --gc-sections)" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.linker.option.gcsections" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.cpp.linker.option.scriptfile.804716106" name="Script files (-T)" superClass="com.freescale.s32ds.cross.gnu.tool.cpp.linker.option.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/Project_Settings/Linker_Files/SKEAZ_flash.ld&quot;"/>
								</option>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.mcpu.1460124069" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.mcpu" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.libraries.1663740375" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.libraries" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.libraries.ewl_c9x_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.sysroot.456802536" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.cpp.linker.option.target.sysroot" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.archiver.982834080" name="Standard S32DS Archiver" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.archiver"/>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.65771154" name="Standard S32DS Assembler" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler">
								<option id="com.freescale.s32ds.cross.gnu.tool.assembler.usepreprocessor.1399906707" name="Use preprocessor" superClass="com.freescale.s32ds.cross.gnu.tool.assembler.usepreprocessor" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.tool.assembler.option.debugging.level.1740340041" name="Debug Level" superClass="com.freescale.s32ds.cross.gnu.tool.assembler.option.debugging.level" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.mcpu.1446527252" name="ARM family" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.mcpu" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.mcpu.cortex-m0plus" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.libraries.55469958" name="Libraries support" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.libraries" value="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.libraries.ewl_c9x_noio" valueType="enumerated"/>
								<option id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.sysroot.332639915" name="Sysroot" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.assembler.option.target.sysroot" value="--sysroot=&quot;${ARM_EWL_DIR}&quot;" valueType="string"/>
								<option id="gnu.both.asm.option.include.paths.602988032" name="Include paths (-I)" superClass="gnu.both.asm.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;"/>
								</option>
								<option id="com.freescale.s32ds.cross.gnu.tool.assembler.option.defs.1951702152" name="Defined symbols (-D)" superClass="com.freescale.s32ds.cross.gnu.tool.assembler.option.defs" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="__START=__thumb_startup"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.60299888" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
								<inputType id="com.freescale.s32ds.cross.gnu.tool.assembler.inputType.asmfile.364335342" superClass="com.freescale.s32ds.cross.gnu.tool.assembler.inputType.asmfile"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.createflash.728930584" name="Standard S32DS Create Flash Image" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.createflash"/>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.createlisting.943298687" name="Standard S32DS Create Listing" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.createlisting">
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.source.314681967" name="Display source (--source|-S)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.source" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.allheaders.456300388" name="Display all headers (--all-headers|-x)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.allheaders" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.demangle.195446517" name="Demangle names (--demangle|-C)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.demangle" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.linenumbers.1840412106" name="Display line numbers (--line-numbers|-l)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.linenumbers" value="true" valueType="boolean"/>
								<option id="com.freescale.s32ds.cross.gnu.option.createlisting.wide.1603438586" name="Wide lines (--wide|-w)" superClass="com.freescale.s32ds.cross.gnu.option.createlisting.wide" value="true" valueType="boolean"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.printsize.1749264933" name="Standard S32DS Print Size" superClass="com.freescale.s32ds.cross.gnu.arm.cortexm.tool.printsize">
								<option id="com.freescale.s32ds.cross.gnu.option.printsize.format.1987003685" name="Size format" superClass="com.freescale.s32ds.cross.gnu.option.printsize.format"/>
							</tool>
							<tool id="com.freescale.s32ds.cross.gnu.c.preprocessor.1069631829" name="Standard S32DS C Preprocessor" superClass="com.freescale.s32ds.cross.gnu.c.preprocessor"/>
							<tool id="com.freescale.s32ds.cross.gnu.cpp.preprocessor.791686586" name="Standard S32DS C++ Preprocessor" superClass="com.freescale.s32ds.cross.gnu.cpp.preprocessor"/>
							<tool id="com.freescale.s32ds.cross.gnu.disassembler.118955312" name="Standard S32DS Disassembler" superClass="com.freescale.s32ds.cross.gnu.disassembler"/>
						</toolChain>
					</folderInfo>
					<fileInfo id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.release.**********.Project_Settings/Debugger" name="Debugger" rcbsApplicability="disable" resourcePath="Project_Settings/Debugger" toolsToInvoke=""/>
					<fileInfo id="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.release.**********.Project_Settings/Linker_Files" name="Linker_Files" rcbsApplicability="disable" resourcePath="Project_Settings/Linker_Files" toolsToInvoke=""/>
					<sourceEntries>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="KEAZ128"/>
						<entry excluding="Debugger|Linker_Files" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Project_Settings"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="include"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="src"/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="S9KEAZN64.com.freescale.s32ds.cross.gnu.arm.cortexm.exe.555925650" name="ARM32 Executable" projectType="com.freescale.s32ds.cross.gnu.arm.cortexm.exe"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.release.**********;com.freescale.s32ds.cross.gnu.arm.cortexm.exe.release.**********.;com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.1815165407;cdt.managedbuild.tool.gnu.c.compiler.input.1856227455">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.freescale.s32ds.cross.gnu.arm.cortexm.exe.debug.**********;com.freescale.s32ds.cross.gnu.arm.cortexm.exe.debug.**********.;com.freescale.s32ds.cross.gnu.arm.cortexm.tool.c.compiler.262660008;cdt.managedbuild.tool.gnu.c.compiler.input.742263047">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.embsys" parent_project="true" register_architecture="" register_board="---  none ---" register_chip="SKEAZN64" register_core="CortexM0P" register_vendor=""/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/S9KEAZN64"/>
		</configuration>
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/S9KEAZN64"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
</cproject>
