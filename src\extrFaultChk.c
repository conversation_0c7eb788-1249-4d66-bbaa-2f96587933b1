//extrFaultChk.c

#include "adc.h"
#include "extrFaultChk.h"
#include "printf.h"
#include "power.h"

uint8_t pDelayTurnOffFg = 0;
static uint8_t overtempbit = 0;
extern uint8_t mirr_power_state_flag(void);

uint16_t pCountVal = 0;
uint8_t pKeaOverHeatFlag = 0;
static int16_t pTempVal = 0;

void usr_adc_init(void)
{
//	ADC_SelectClock(ADC, CLOCK_SOURCE_BUS_CLOCK_DIVIDE_2);
//	ADC_SelectClockDivide(ADC, ADC_ADIV_DIVIDE_8);
//	ADC_VrefSelect(ADC, ADC_VREF_VDDA);
//	ADC_SetChannel(ADC, ADC_CHANNEL_AD22_TEMPSENSOR);
//	ADC_SetMode(ADC, ADC_MODE_12BIT);
//	ADC_SetLongSample(ADC);
//	ADC_SingleConversion(ADC);
	if(pTempVal < (KEA_CORE_TEMP_LIMIT)) pCountVal = 30;
}

//static uint16_t pTempValLast = 0;
void usr_adc_process(void)
{
	uint16_t tAdcVal = 0;
	double temp = 0;
	//if(ADC_IsConversionActiveFlag(ADC) == FALSE)//last conversion is complete
	{
		//tAdcVal = ADC_ReadResultReg(ADC);
		ADC_SetMode(ADC, ADC_MODE_12BIT);
		ADC_SingleConversion(ADC);
		tAdcVal = ADC_PollRead(ADC, ADC_CHANNEL_AD22_TEMPSENSOR);
		temp = tAdcVal;
		
		temp = (temp/4096)*3300;// in mv
		int16_t tAdcMv =  (int16_t)(temp);

		if(tAdcMv >1396)//slope < 25C (-40~25) 3.266mv/C
		{
			temp = 25 - (double)(tAdcMv-1396)*1000/3266;
		}
		else//slope > 25C (25~125)	3.638mv/C
		{
			temp = 20 - (double)(tAdcMv-1396)*1000/3638;
		}

		pTempVal = (int16_t)(temp);//real temperature in C
//		DEBUG_LOGI("kea core temperature : %d C\r\n", pTempVal);

#if 0
		if(pTempValLast != pTempVal)
		{
			DEBUG_LOGI("kea core temperature : %d C\r\n", pTempVal);
			pTempValLast = pTempVal;
		}
#endif
//		DEBUG_LOGI("pCountVal : %d\r\n", pCountVal);

		if(pTempVal > (KEA_CORE_TEMP_LIMIT))
		{
			//send signal to DHU!!!
			pKeaOverHeatFlag = 1;
		}
		else if(pTempVal < KEA_CORE_TEMP_LIMIT-10)
		{
			pKeaOverHeatFlag = 0;
			pCountVal = 30;
		}
	}
	//ADC_SingleConversion(ADC);//wait 100ms until next read
}

uint8_t getKeaOverHeatFlag(void)
{
	return pKeaOverHeatFlag;
}


uint8_t overtempbitFlag(void)
{
	return overtempbit;
}

void delayTurnOffIrmd(void)
{
	if(pKeaOverHeatFlag)
	{
		if(pCountVal == 0)//trun off irmd power after counter clock running over
		{
			irmd_power_off();
			GEOCAMERA_power_off();
			overtempbit = 1;
		}
		--pCountVal;
	}
	else
	{
		if(overtempbit == 1 && mirr_power_state_flag() == 0)
		{
			power_init();
			overtempbit = 0;
		}
	}
//	DEBUG_LOGI("pCountVal : %d C\r\n", pCountVal);
}



