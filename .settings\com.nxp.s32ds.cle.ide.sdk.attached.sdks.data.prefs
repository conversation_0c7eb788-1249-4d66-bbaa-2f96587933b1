KEAZN64_1.0.0_PATH=<attachData>\r\n<config name\="Debug">\r\n<option id\="gnu.c.compiler.option.include.paths" newValue\="&quot;${ProjDirPath}/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/include&quot;,&quot;${KEAZN64_1.0.0_PATH}/headers&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/bsp/UART&quot;,&quot;${KEAZN64_1.0.0_PATH}/lin_cfg&quot;,&quot;${KEAZN64_1.0.0_PATH}/&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/lowlevel&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver&quot;" oldValue\="&quot;${ProjDirPath}/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;" skippedValue\=""/>\r\n<option id\="gnu.cpp.compiler.option.include.paths" newValue\="&quot;${ProjDirPath}/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_C++/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C++/include/arm&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/include&quot;,&quot;${KEAZN64_1.0.0_PATH}/headers&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/bsp/UART&quot;,&quot;${KEAZN64_1.0.0_PATH}/lin_cfg&quot;,&quot;${KEAZN64_1.0.0_PATH}/&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/lowlevel&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver&quot;" oldValue\="&quot;${ProjDirPath}/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_C++/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C++/include/arm&quot;" skippedValue\=""/>\r\n</config>\r\n<config name\="Release">\r\n<option id\="gnu.c.compiler.option.include.paths" newValue\="&quot;${ProjDirPath}/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/include&quot;,&quot;${KEAZN64_1.0.0_PATH}/headers&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/bsp/UART&quot;,&quot;${KEAZN64_1.0.0_PATH}/lin_cfg&quot;,&quot;${KEAZN64_1.0.0_PATH}/&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/lowlevel&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver&quot;" oldValue\="&quot;${ProjDirPath}/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;" skippedValue\=""/>\r\n<option id\="gnu.cpp.compiler.option.include.paths" newValue\="&quot;${ProjDirPath}/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/include&quot;,&quot;${KEAZN64_1.0.0_PATH}/headers&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/bsp/UART&quot;,&quot;${KEAZN64_1.0.0_PATH}/lin_cfg&quot;,&quot;${KEAZN64_1.0.0_PATH}/&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver/lowlevel&quot;,&quot;${KEAZN64_1.0.0_PATH}/LIN_Driver&quot;" oldValue\="&quot;${ProjDirPath}/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include&quot;,&quot;${ARM_EWL_DIR}/EWL_C/include/arm&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include&quot;,&quot;${ARM_EWL_DIR}/EWL_Runtime/include/arm&quot;" skippedValue\=""/>\r\n</config>\r\n</attachData>\r\n
eclipse.preferences.version=1
