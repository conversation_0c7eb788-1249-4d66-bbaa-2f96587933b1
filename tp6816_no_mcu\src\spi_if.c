/*
 * @file      spi_if.c
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:55 created
 * $Id: spi_if.c 1.1 $
 */


#include "tp6823.h"
#include "cmd_queue.h"
#include "spi_if.h"
#include "spi_general_flash.h"
#include "SPI_NOR_FLASH_LIST.h"

unsigned char  CONST_ERROR_QUAD[] = "[ERROR] failed in enable quad mode\n";
unsigned char  CONST_ERROR_ISSUE[] = "[ERROR] Issue Command Timeout!\n";

/* important! 5 is maximum counts of read/write unless used SPI buffer(256B) */
bit spi_cmd_rd(unsigned char cmd, unsigned char *buf, unsigned char cnt)
{
    unsigned char i;

    //assert(cnt <= SPI_IF_RD_CNT_MAX);

    SPI_SET_CMD(cmd);
#if defined(SPIIF_EN_QPI_MODE)
    if(SPI_IN_QPI())
        SPI_SET_MODE(QPI_IF_4I4O);
    else
        SPI_SET_MODE(SPI_IF_1I1O);
#else
    SPI_SET_MODE(SPI_IF_1I1O);
#endif
    SPI_SET_RDD_CNT(cnt);
    SPI_SET_CTRL(SPI_IF_INSTR_YES);
    if(SPI_ISSUE() == EXIT_FAILURE)
        return EXIT_FAILURE;
    for(i = 0; i < cnt; i++)
        buf[i] = SPI_GET_RDD(i);
    return EXIT_SUCCESS;
}

bit spi_cmd_wr(unsigned char cmd, unsigned char *buf, unsigned char cnt)
{
    unsigned char i;

    //assert(cnt <= SPI_IF_WR_NCT_MAX);

    SPI_SET_CMD(cmd);
    for(i = 0; i < cnt; i++)
        SPI_SET_WRD(i, buf[i]);
    if(SPI_IN_QPI())
        SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | QPI_IF_4I4O);
    else
        SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | SPI_IF_1I1O);
    SPI_SET_RDD_CNT(0);
    SPI_SET_CTRL(SPI_IF_INSTR_YES | SPI_IF_WR_CNT(cnt));
    
    return SPI_ISSUE_CQ();
}

bit spi_rdid(NOR_ID *id)
{
    return spi_cmd_rd(NOR_RDID_CMD, (unsigned char *)id, sizeof(NOR_ID));
}

bit spi_rdsr(unsigned char *sta_regs, unsigned char cnt)
{
    return spi_cmd_rd(NOR_RDSR_CMD, sta_regs, cnt);
}

bit spi_wrsr(unsigned char *sta_regs, unsigned char cnt)
{
    return spi_cmd_wr(NOR_WRSR_CMD, sta_regs, cnt);
}

static unsigned char nor_cur_sr;
bit spi_unprotect(void)
{
    unsigned char val;
    // store current SR
    if (spi_rdsr(&nor_cur_sr, 1))
        return 1;
    
    // SRWD (Status Register Write Disable), SR[7]
    // Protected Area: 4'b0000, SR[5:2]
    nor_cur_sr &= 0xFC;
    val = (nor_cur_sr & (~0xBF));

    return spi_wrsr(&val, 1);
}

bit spi_protect(void)
{
    unsigned char val;
    // SRWD (Status Register Write Disable), SR[7]
    // Protected Area: 4'b0000, SR[5:2]
    val = (nor_cur_sr | 0xBC);
    // restore SR
    return spi_wrsr(&val, 1);
}

bit spi_rdid_check(unsigned char mf_id, unsigned short dev_id)
{
    NOR_ID id;
    if (spi_cmd_rd(NOR_RDID_CMD, (unsigned char *)&id, sizeof(NOR_ID))) {
        DBGMSG(("Can't read NOR Flash's ID\n"));
        return EXIT_FAILURE;
    }
    DBGMSG(("RDID: %bX%X\n", id.mf_id, id.dev_id));
    if (id.mf_id != mf_id
        || id.dev_id != dev_id) {
        DBGMSG(("mismatch target's ID %bX%X\n", mf_id, dev_id));
        return EXIT_FAILURE;
    }
    return EXIT_SUCCESS;
}

bit spi_sr1_bit6_quad_enable(void)
{
    unsigned char sr1;
    if(spi_rdsr(&sr1, 1) != EXIT_SUCCESS) 
        return EXIT_FAILURE;
    if (sr1 & SR1_QUAD_EN_BIT6)
		return EXIT_SUCCESS;
    /* Update the Quad Enable bit. */
    sr1 |= SR1_QUAD_EN_BIT6;
    return spi_wrsr(&sr1, 1);
}
bit spi_sr2_bit7_quad_enable(void)
{
    unsigned char sr2;
    if(spi_cmd_rd(NOR_RDSR2_CMD2, &sr2, 1) != EXIT_SUCCESS)
        return EXIT_FAILURE;
    if (sr2 & SR2_QUAD_EN_BIT7)
		return EXIT_SUCCESS;
    /* Update the Quad Enable bit. */
	sr2 |= SR2_QUAD_EN_BIT7;  
    return spi_cmd_wr(NOR_WRSR2_CMD2, &sr2, 1);
}
bit spi_sr2_bit1_quad_enable(bit nurd)
{
    unsigned char sr[2];

    if(spi_rdsr(sr, 1) != EXIT_SUCCESS)
        return EXIT_FAILURE;
    if (nurd) {
        // maybe SR2 not only QE bit, needs to confirm in NOR datasheet
        sr[1] = SR2_QUAD_EN_BIT1;
    }
    else {
        if(spi_cmd_rd(NOR_RDSR2_CMD2, &sr[1], 1) != EXIT_SUCCESS)
            return EXIT_FAILURE;
        if (sr[1] & SR2_QUAD_EN_BIT1)
		    return EXIT_SUCCESS;
        sr[1] |= SR2_QUAD_EN_BIT1;
    }
    /* Update the Quad Enable bit. */
    return spi_cmd_wr(NOR_WRSR_CMD, sr, 2);
}
bit spi_sr2_bit1_wr_quad_enable(void)
{
    unsigned char sr2;
    if(spi_cmd_rd(NOR_RDSR2_CMD, &sr2, 1) != EXIT_SUCCESS)
        return EXIT_FAILURE;
    if (sr2 & SR2_QUAD_EN_BIT1)
		return EXIT_SUCCESS;
    /* Update the Quad Enable bit. */
	sr2 |= SR2_QUAD_EN_BIT1;
    return spi_cmd_wr(NOR_WRSR2_CMD, &sr2, 1);
}
bit spi_sr1_bit6_quad_enable_dbg(void)
{
    if (spi_sr1_bit6_quad_enable() == EXIT_SUCCESS)
        return EXIT_SUCCESS;
    DBGMSG((CONST_ERROR_QUAD));
    return EXIT_FAILURE;
}
bit spi_sr2_bit7_quad_enable_dbg(void)
{
    if (spi_sr2_bit7_quad_enable() == EXIT_SUCCESS)
        return EXIT_SUCCESS;
    DBGMSG((CONST_ERROR_QUAD));
    return EXIT_FAILURE;
}
bit spi_sr2_bit1_quad_enable_dbg(bit unrd)
{
    if (spi_sr2_bit1_quad_enable(unrd) == EXIT_SUCCESS)
        return EXIT_SUCCESS;
    DBGMSG((CONST_ERROR_QUAD));
    return EXIT_FAILURE;
}
bit spi_sr2_bit1_wr_quad_enable_dbg(void)
{
    if (spi_sr2_bit1_wr_quad_enable() == EXIT_SUCCESS)
        return EXIT_SUCCESS;
    DBGMSG((CONST_ERROR_QUAD));
    return EXIT_FAILURE;
}
bit spi_nor_qre(unsigned char idx)
{
	bit ret = EXIT_FAILURE;
    /* Quad Enable Requirements. */
	switch (idx) {
	case SNOR_QER_NONE:
		return EXIT_SUCCESS;
	case SNOR_QER_SR1_BIT6:
		ret = spi_sr1_bit6_quad_enable();
        break;
	case SNOR_QER_SR2_BIT7:
		ret = spi_sr2_bit7_quad_enable();
        break;
	case SNOR_QER_SR2_BIT1_BUGGY:
	case SNOR_QER_SR2_BIT1_NO_RD:
		ret = spi_sr2_bit1_quad_enable(1);
        break;
	case SNOR_QER_SR2_BIT1:
		ret = spi_sr2_bit1_quad_enable(0);
        break;
	case SNOR_QER_SR2_BIT1_WR:
		ret = spi_sr2_bit1_wr_quad_enable();
        break;
	}
    if (ret == EXIT_FAILURE) {
        DBGMSG((CONST_ERROR_QUAD));
    }
	return ret;
}

bit spi_quad_en_customize(NOR_ID *nid, unsigned char *wrbuf, unsigned char wcnt)
{
    NOR_ID id;

    if (spi_rdid(&id) != EXIT_SUCCESS)
        return EXIT_FAILURE;
    DBGMSG(("RDID: %bX%X\n", id.mf_id, id.dev_id));

    if (id.mf_id != nid->mf_id 
        || id.dev_id != nid->dev_id) {
        DBGMSG(("unknown MFID, don't run in quad mode...\n"));
        return EXIT_FAILURE;
    }

    if (wcnt > 0) {
        if (spi_cmd_wr(wrbuf[0], (wrbuf + 1), wcnt) != EXIT_SUCCESS) {
            DBGMSG((CONST_ERROR_QUAD));
            return EXIT_FAILURE;
        }
    }
    return EXIT_SUCCESS;
}

bit spi_quad_en(void)
{
    bit status;
    NOR_ID id;

    if (spi_rdid(&id) != EXIT_SUCCESS) {
        DBGMSG((CONST_ERROR_QUAD));
        return EXIT_FAILURE;
    }
    DBGMSG(("RDID: %X-%X\n", id.mf_id, id.dev_id));

    switch (id.mf_id) {
    case WINBOND_MFID:
	case GD_MFID:
    case CYP_MFID:
    case EON_MFID:
        SPI_SET_INSTR_QPI(NOR_QPI2_EN_CMD, NOR_QPI2_EX_CMD);
    }
    if (id.mf_id == ISSI_MFID) {
        SPI_SET_INSTR_4BA(NOR_4B2_EX_CMD, NOR_4B2_EX_CMD);
    }
    
    switch (id.mf_id) {
    case MXIC_MFID:
    case ISSI_MFID:
        status = spi_nor_qre(SNOR_QER_SR1_BIT6);
        break;
    case WINBOND_MFID:
    case GD_MFID:
        status = spi_nor_qre(SNOR_QER_SR2_BIT1_WR);
        break;
    case CYP_MFID:
        status = spi_nor_qre(SNOR_QER_SR2_BIT1);
        break;
    case EON_MFID:
        status = spi_nor_qre(SNOR_QER_NONE);
        break;
    default:
        DBGMSG(("unknown MFID, don't run in quad mode...\n"));
        return EXIT_FAILURE;
    }
    return status;
}

bit spi_cmd_wr_cq(unsigned char cmd, unsigned char *buf, unsigned char cnt)
{
    unsigned char i;

    assert(cnt <= SPI_IF_WR_NCT_MAX);

    SPI_SET_CMD(cmd);
    for(i = 0; i < cnt; i++)
        SPI_SET_WRD(i, buf[i]);
#if defined(SPIIF_EN_QPI_MODE)
    if(SPI_IN_QPI())
        SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | QPI_IF_4I4O);
    else
        SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | SPI_IF_1I1O);
#else
    SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | SPI_IF_1I1O);
#endif
    SPI_SET_RDD_CNT(0);
    SPI_SET_CTRL(SPI_IF_INSTR_YES | SPI_IF_WR_CNT(cnt));
    return SPI_ISSUE_CQ();
}

bit spi_sector_erase(unsigned long addr)
{
    SPI_SET_CMD(NOR_SE_CMD);
#ifdef SPIIF_EN_4B_ADDR
    SPI_SET_NORADDR_4B(addr);
#else
    SPI_SET_NORADDR(addr);
#endif
#if defined(SPIIF_EN_QPI_MODE)
    if(SPI_IN_QPI())
        SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | QPI_IF_4I4O);
    else
        SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | SPI_IF_1I1O);
#else
    SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | SPI_IF_1I1O);
#endif
    SPI_SET_RDD_CNT(0);
#ifdef SPIIF_EN_4B_ADDR    
    SPI_SET_CTRL(SPI_IF_INSTR_YES | SPI_IF_WR_CNT(4));
#else
    SPI_SET_CTRL(SPI_IF_INSTR_YES | SPI_IF_WR_CNT(3));
#endif
    return SPI_ISSUE_CQ();
}

bit spi_pp(unsigned long addr, unsigned char *buf, unsigned char size)
{
    unsigned short len = 0;

    SPI_SET_CMD(NOR_PP_CMD);
#ifdef SPIIF_EN_4B_ADDR
    SPI_SET_NORADDR_4B(addr);
#else
    SPI_SET_NORADDR(addr);
#endif
#if defined(SPIIF_EN_QPI_MODE)
    if(SPI_IN_QPI())
        SPI_SET_MODE(SPI_IF_PTR_RST | SPI_IF_RDSR | SPI_IF_WREN | QPI_IF_4I4O);
    else
        SPI_SET_MODE(SPI_IF_PTR_RST | SPI_IF_RDSR | SPI_IF_WREN | SPI_IF_1I1O);
#else
    SPI_SET_MODE(SPI_IF_PTR_RST | SPI_IF_RDSR | SPI_IF_WREN | SPI_IF_1I1O);
#endif
    SPI_SET_RDD_CNT(0);
    len = size;
    if(len == 0) len = 0x100;
    size = 0;
    SPI_SET_PP_CNT(len);
    if (buf) {
        while (len--) {
            SPI_PUT_DPORT(buf[size++]);
        }
    }
#ifdef SPIIF_EN_4B_ADDR    
    SPI_SET_CTRL(SPI_IF_INSTR_YES | SPI_IF_WR_CNT(4) | SPI_IF_BUFF_WR);
#else
    SPI_SET_CTRL(SPI_IF_INSTR_YES | SPI_IF_WR_CNT(3) | SPI_IF_BUFF_WR);
#endif    
    return SPI_ISSUE_CQ();
}

extern bit spi_watting_finish(int time);

bit spi_issue_short(int time)
{
    int timeout = 0;
    if ((read_p0(SPI_FETCH_DUMMY_REG) & 0x03) == SPI_IF_ENTER_XMODE) {
        cq_set_trig_mode(CQ_OSD_TRIG);
	    CQ_CNT_START();
        CQ_P0_1B_(SPI_IF_RDBACK_SEL_REG, TRIGE_EXIT_XIP);
        CQ_P0_1B_(SPI_IF_CTRL_REG, (SPI_GET_CTRL | SPI_IF_ISSUE_BIT));	
        CQ_FINISH_ISSUE();
    }
    return spi_watting_finish(time);
}

bit spi_issue_cq(int time)
{
    unsigned char rls_en_val;
    unsigned char grls_en_val;
    int timeout = 0;
    rls_en_val = read_p2(0xcf);
    grls_en_val = read_p2(0x03);
    if ((rls_en_val & 0xf0)
        && (grls_en_val & 0x08)) {
        cq_set_trig_mode(CQ_VS_TRIG);
        CQ_CNT_START();
        CQ_P2_1B_(0xcf, 0x00);  // disable RLs threads
        CQ_P2_1B_(0x03, (grls_en_val & 0xf7));  // disable Global RLs
        CQ_FINISH_ISSUE();
    }
    if (spi_watting_finish(time)) {
        return EXIT_FAILURE;
    }
    if ((rls_en_val & 0xf0)
        && (grls_en_val & 0x08)) {
        write_p2(0xcf, rls_en_val);
        write_p2(0x03, grls_en_val);
    }
    return EXIT_SUCCESS;
}
