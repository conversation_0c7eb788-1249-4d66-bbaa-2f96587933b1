Version: 0.00
Header:
  - "### LCD<PERSON><PERSON>, (C) Techpoint"
  - Project File
Project Name: TP6816_MIPI
Project Dir: D:/TP/DEMO/TP6823&6815/CSF/TP6816_MIPI
Project Chip:
  Name: TP6823
  Package: 128TQFP
  Chip ID: 0x6823
  Revision ID: 0x00
  Data Code: 0x95
  Description: New
  Bonding ID: 0xF
Panel Parameters:
  Interface: LVDS
  DCLK Frequency: [40.8, 51.2, 67.2]
  Horizontal Valid Data: 1024
  HSync Pulse Width: [2, 2, 2]
  HSync Back Porch: [32, 32, 344]
  HSync Front Porch: [19, 44, 155]
  Horizontal Line: 1024
  Vertical Active: 600
  VSync Pulse Width: [2, 2, 2]
  VSync Back Porch: [5, 5, 198]
  VSync Front Porch: [5, 5, 91]
  Vertical Field: [610, 635, 800]
Scaler Blocks:
  - function: BT.1120/BT.656 Decoder
    description: input select, capture, crop...
  - function: L601/RGB888/RGB666/YUV444/YUV422
    description: input select, capture, crop...
  - function: Pixel Enhance
    description: Pixel Enhance...
  - function: Scaling Machine
    description: Line Buffers...
  - function: Gamma/Dither/Pattern Gen.
    description: Gamma/Dither/Pattern Gen....
  - function: SPI OSD/Font OSD
    description: SPI OSD/Font OSD....
  - function: Panel Timing Controller
    description: Panel Timing Controller....
  - function: LVDS_Tx
    description: Single/Dual LVDS_Tx....
  - function: BT.1120/BT.656 Encoder
    description: input select, capture, crop...
  - function: MIPI_Rx
    description: Single MIPI_Rx....
  - function: FSK / Audio
    description: FSK / Audio....
  - function: PLLs
    description: PLLs/Touch Screen/Aux SAR ADCs....
  - function: SPI Master
    description: SPI Master....
  - function: DPWMs/GPIO/UART
    description: DPWMs/GPIO/UART....
VD Blocks:
  - function: AFE
    description: Analog Front End...
  - function: Sync Processor
    description: Sync Processor...
  - function: Y/C separation
    description: Y/C separation
  - function: Chroma demodulation
    description: Chroma demodulation....
  - function: Automatic Chroma Gain Control
    description: Automatic Chroma Gain Control....
  - function: Color Killer
    description: Color Killer....
  - function: Component Processing
    description: Component Processing....
  - function: Sharpness (Peaking)
    description: Sharpness (Peaking)....
  - function: FSK / PTZ / Audio Stream
    description: FSK / PTZ / Audio Stream....
Pin Function Select:
  - interface: VIN0
    default: 1
  - interface: VIN1
    default: 1
  - interface: VIN2
    default: 1
  - interface: VIN3
    default: 1
  - interface: VIN4
    default: 1
  - interface: VIN5
    default: 1
  - interface: IV1
    default: 6
  - interface: IV1DE
    default: 0
  - interface: IV2
    default: 1
  - interface: IV3
    default: 1
  - interface: IV4
    default: 1
  - interface: Panel
    default: 1
  - interface: 2nd Panel
    default: 0
  - interface: OV1
    default: 0
  - interface: OV1 with HRef/VRef
    default: 0
  - interface: OV2
    default: 0
  - interface: OV2 with HRef/VRef
    default: 0
  - interface: GPIOA 0
    default: 1
  - interface: GPIOA 1
    default: 1
  - interface: GPIOA 2
    default: 0
  - interface: GPIOA 3
    default: 0
  - interface: GPIOA 4/5
    default: 0
  - interface: GPIOA 6
    default: 0
  - interface: GPIOA 7
    default: 1
  - interface: GPIOB 0/1
    default: 0
  - interface: GPIOB 2~7
    default: 0
  - interface: GPIOB 3B
    default: 0
  - interface: GPIOB 4B
    default: 0
  - interface: GPIOB 5B
    default: 0
  - interface: GPIOB 6B
    default: 0
  - interface: GPIOB 7B
    default: 0
  - interface: GPIOC 0~7
    default: 0
  - interface: GPIOD 0~7
    default: 2
  - interface: Port0 0~7
    default: 1
  - interface: Port1 0
    default: 0
  - interface: Port1 1
    default: 0
  - interface: Port1 2
    default: 0
  - interface: Port1 3
    default: 0
  - interface: Port1 4
    default: 1
  - interface: Port1 5
    default: 1
  - interface: Port1 6
    default: 0
  - interface: Port1 7
    default: 0
  - interface: Port2 0~7
    default: 1
  - interface: Port3 0
    default: 1
  - interface: Port3 1
    default: 1
  - interface: Port3 2
    default: 1
  - interface: Port3 3
    default: 1
  - interface: Port3 4
    default: 1
  - interface: Port3 5
    default: 1
  - interface: Port3 6
    default: 1
  - interface: Port3 7
    default: 1
  - interface: TXD0
    default: 1
  - interface: RXD0
    default: 1
  - interface: TXD1 (U2Cfg)
    default: 1
  - interface: RXD1 (U2Cfg)
    default: 1
  - interface: PWMDC
    default: 1
  - interface: PWMLED
    default: 1
  - interface: PWMA
    default: 0
  - interface: PWMB
    default: 0
  - interface: PTZ
    default: 0
  - interface: PTZ 0
    default: 1
  - interface: PTZ 2
    default: 1
  - interface: INTRO
    default: 0
  - interface: ADC 0~2
    default: 1
  - interface: ADC3
    default: 1
  - interface: DIAG0
    default: 0
  - interface: DIAG1
    default: 0
  - interface: DIAG2
    default: 0
  - interface: DIAG3
    default: 0
  - interface: DIAG5
    default: 0
  - interface: XP/YP/XM/YM
    default: 0
VideoInOV1: !<tag:yaml.org,2002:binary> AQ7QMuABERQAUDAA
VideoInOV2: !<tag:yaml.org,2002:binary> AQ7QMuABERQAUDAA
VideoOutOV1: !<tag:yaml.org,2002:binary> AQAAAA==
VideoOutOV2: !<tag:yaml.org,2002:binary> AgAAAA==
LvT2Vals: !<tag:yaml.org,2002:binary> eQMEAA==
PwmDcVals: !<tag:yaml.org,2002:binary> AAMAAAAQOAQAQIAgAAAA
PwmBvals: !<tag:yaml.org,2002:binary> AAAAQA==
GPIOBvals: !<tag:yaml.org,2002:binary> AAAAAAAAAAAA
GPIOCvals: !<tag:yaml.org,2002:binary> AAAAAAAAAAAA
GPIODvals: !<tag:yaml.org,2002:binary> AAAAAAAAAAAA
Custom Panels:
  - Name: 1920线84
    Interface: Single LVDS
    res:
      - 1920
      - 384
    hactive: 1920
    vactive: 384
    dclk:
      - 42.00
      - 52.00
      - 63.00
    frate:
      - 0
      - 0
      - 0
    hsw:
      - 2
      - 2
      - 5
    hbpch:
      - 32
      - 32
      - 50
    htotal:
      - 1958
      - 2050
      - 2200
    vsw:
      - 2
      - 2
      - 2
    vbpch:
      - 5
      - 5
      - 25
    vtotal:
      - 412
      - 420
      - 450
Panel Index: 0
Custom Reg Blks:
  - VD Ain Select
  - Rx Video Decoder
  - VPLL
  - Pull Enable
  - Input Enable
  - Driving Select
  - Pin Function Select
  - MIPI Rx
  - Main Path Digital Video Input
  - Scaling Ratio
  - LineBuffer Timing
  - Panel Output Timing
  - Panel Control
  - LVDS Tx
  - DPLL
  - Panel Type Select
  - Power Down
Connect:
  - Interface: UART Cfg
    Baud Rate: 921600
    COM Port: COM10
    MI2C Clock: 1MHz
ScalerPage0: !!binary "AAyANwAFEQkAUDCFMKAEIAAAAACABwAFEDMAYAAAwAAABrwN6hIgIAMBAwAAAABgQQABAAABAAASwAQgAAAAAAgQIDBAUGBwgJCgsMDQ4PD4EAAETEgARFD/gIAAgICAAgECAQAAIIAAs4AsW4BxgERECAAAAAAAAAAAAMAC4AAAAAAAAADgAABAagVQCxAwBjE19bfpFwAAAQAAqAAIQAAA8AAAAAEAAAAA5AIAAwAAAADwAQAgAAAAg9UBAAAAV44AaAAAAAAAAAAAAAAAAAAfAAAAAAMRoAAAAAAAAAAEAAAAAAAQABAAAAAAAgAA8LgriHhpsQEAAAAAAIdoIw=="
ScalerPage1: !!binary "AAAAAAFVVQMAAIAHQAAAABAQAAADgigoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAw0I48IEahdACkKAAAKACldAKgIEI80IQwDZANwAABwAAAAAAAAAAAAAAAAAAAAAICAgICAgAAIAAAAAAAAAQAAAIAHAAA4BAAAAAABYgAAAgAgAIAHAggCAAUAgAGkAWUEAAAAAAAAAAwAAAAAKACACBABAAAAAAAAAAE5A3QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAA=="
ScalerPage2: !!binary "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABAAAAEAARAAEAAAAQABEAAQAAABAAEQABAAAAEAAQAADw8AAA8PAAAPDwAADw8QABAAAAEAAQAAAA8QABAAAAEAAQAAAA8QABAAAAEAAQAAAA8QABAAAAEAAQAAAA8AAAAAAAAfDwAAAAAAAB8PAAAAAAAAHw8AAAAAAAAfDwAAAAAAAA8PAAAAAAAADw8AAAAAAAAPDwAAAAAAAA8PIGSo7AAAAD8AAAAAzigMAAABAAEQABAAAAEAARAAEAAAAQABEAAQAAABAAEQABAAAAAAAAAAAAAAAAAAAAAAAA=="
ScalerPage3: !!binary "KgACiEgAAAAAAAAAAAAAADDxAFVVAAgADwAAAAD//5hAFAEAEZgIAAAAAAAACScJZQQAAAAAAAAAAQIAgAcAAAD///8A////AP//OwD///+IAIgBJAYisCKoygClijIl/P8A//D/PwDmAP///wAA/6YAAACAcABHwgDlAAABAAAQAEAEIAAAACAAAAAAAIKASwgCiEZAZhAqAQOISAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAQOAQAQIAgAAAAAAAAAEAAAAABzMwMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=="
VdPage0: !!binary "AADCAAAAMkAAJEhAQ1AQAABAQAAAExUAGdAlAAZygIAwhjg8BP8FLQBIMEoKMHAASLoukAAF3ABADjIlAGBgAAAAAAAAAAAAAAAAAAAAAAAcAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABVVVRZSAAAwEBg+FQcPpVgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAD6AAAAAAAAAAAAAAAAAAALDAsAH3h4AAAHCAsABAAAYBAGvjknAGjAGBIaAAAAAA4fSpAAOAgTuxO7E7sTuwAAAADwAAAAAAAAAABoIw=="
GammaLUT R: !!binary "AAECAwQFBgcICQoLDA0ODxAREhMUFRYXGBkaGxwdHh8gISIjJCUmJygpKissLS4vMDEyMzQ1Njc4OTo7PD0+P0BBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWltcXV5fYGFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6e3x9fn+AgYKDhIWGh4iJiouMjY6PkJGSk5SVlpeYmZqbnJ2en6ChoqOkpaanqKmqq6ytrq+wsbKztLW2t7i5uru8vb6/wMHCw8TFxsfIycrLzM3Oz9DR0tPU1dbX2Nna29zd3t/g4eLj5OXm5+jp6uvs7e7v8PHy8/T19vf4+fr7/P3+/w=="
GammaLUT G: !!binary "AAECAwQFBgcICQoLDA0ODxAREhMUFRYXGBkaGxwdHh8gISIjJCUmJygpKissLS4vMDEyMzQ1Njc4OTo7PD0+P0BBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWltcXV5fYGFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6e3x9fn+AgYKDhIWGh4iJiouMjY6PkJGSk5SVlpeYmZqbnJ2en6ChoqOkpaanqKmqq6ytrq+wsbKztLW2t7i5uru8vb6/wMHCw8TFxsfIycrLzM3Oz9DR0tPU1dbX2Nna29zd3t/g4eLj5OXm5+jp6uvs7e7v8PHy8/T19vf4+fr7/P3+/w=="
GammaLUT B: !!binary "AAECAwQFBgcICQoLDA0ODxAREhMUFRYXGBkaGxwdHh8gISIjJCUmJygpKissLS4vMDEyMzQ1Njc4OTo7PD0+P0BBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWltcXV5fYGFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6e3x9fn+AgYKDhIWGh4iJiouMjY6PkJGSk5SVlpeYmZqbnJ2en6ChoqOkpaanqKmqq6ytrq+wsbKztLW2t7i5uru8vb6/wMHCw8TFxsfIycrLzM3Oz9DR0tPU1dbX2Nna29zd3t/g4eLj5OXm5+jp6uvs7e7v8PHy8/T19vf4+fr7/P3+/w=="
GammaLUT All: !!binary "AAECAwQFBgcICQoLDA0ODxAREhMUFRYXGBkaGxwdHh8gISIjJCUmJygpKissLS4vMDEyMzQ1Njc4OTo7PD0+P0BBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWltcXV5fYGFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6e3x9fn+AgYKDhIWGh4iJiouMjY6PkJGSk5SVlpeYmZqbnJ2en6ChoqOkpaanqKmqq6ytrq+wsbKztLW2t7i5uru8vb6/wMHCw8TFxsfIycrLzM3Oz9DR0tPU1dbX2Nna29zd3t/g4eLj5OXm5+jp6uvs7e7v8PHy8/T19vf4+fr7/P3+/w=="
Product ID: 26630