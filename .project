<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>EF1E_MCE_MIPI</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>KEAZ128</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>KEAZ128/LIN_Driver</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>KEAZ128/lin_cfg</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>KEAZ128/sources</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>KEAZ128/LIN_Driver/bsp</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>KEAZ128/LIN_Driver/coreapi</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>KEAZ128/LIN_Driver/diagnostic</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>KEAZ128/LIN_Driver/lowlevel</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>KEAZ128/LIN_Driver/transport</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>KEAZ128/LIN_Driver/bsp/UART</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
	</linkedResources>
</projectDescription>
