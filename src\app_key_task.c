#include "config.h"
#include "bsp_msg.h"
#include "bsp_key.h"
#include "app_key_task.h"
#include "tick.h"
#include "para.h"
#include "bsp_isp.h"
#include "power.h"
#include "tp6823.h"
#include "mpq3367_pwm.h"

static uint16_t  tick_key_check;
extern void i2c_cmd_on_off(uint8_t _on_off);
extern uint8_t i2c_cmd_get_on_off(void);
#if (EF1E_TEST_MODE==1)
static uint8_t  test_mode_flag = 0;
static uint16_t  test_mode_count = 0;
extern void display_test_mode(uint8_t _mode);
uint8_t double_click_test_mode_flag = 0;
#elif (EF1E_TEST_MODE == 4)
extern void display_test_screen(uint8_t _on_off, uint8_t _r, uint8_t _g, uint8_t _b);
extern void display_test_error_mode(uint8_t _mode, uint8_t _on_off);
extern void mpq3367_disable(void);
extern void mpq3367_enable(void);
typedef enum
{
    TEST_MODE_0 = 0,
    TEST_MODE_NOISE,
    TEST_MODE_FOCUS,
    TEST_MODE_BLACK,

};
typedef enum
{
    TEST_STEP_0 = 0,
    TEST_STEP_VIEW_ERROR,
    TEST_STEP_VIEW_TEXT,
    TEST_STEP_MAX,
};
static uint8_t test_mode_index = 0; 
static uint8_t test_mode_flag = 0;  
static uint8_t test_mode_step = 0;  
static uint16_t test_mode_view_error_count = 0;

void test_error_mode_noise(uint8_t *_step)
{
    switch (*_step)
    {
    case 0:
        write_p0(0x0F, read_p0(0x0F) | 0x01);
        write_p0(0x0C, read_p0(0x0C) | 0x80);
        test_mode_view_error_count = 0;
        *_step = 1;
        printf("test_error_mode_noise\n");
        break;
    case 1:
       
        if (test_mode_view_error_count < 1)
            test_mode_view_error_count++;
        else
        {
            *_step = 2;
        }
     
        break;
    case 2:
        display_test_error_mode(1, 1);
        *_step = 3;
      
        break;
    case 4:
        write_p0(0x0C, read_p0(0x0c) & 0x7f);
        write_p0(0x0F, read_p0(0x0F) & 0xfe);
        display_test_error_mode(1, 0);
       
        break;
    default:
        break;
    }
}

void test_error_mode_focus(uint8_t *_step)
{
    switch (*_step)
    {
    case 0:
        write_p1(0x02, 0x00);
        write_p1(0x03, 0x28);
        write_p1(0x04, 0x00);
        test_mode_view_error_count = 0;
        *_step = 1;
        printf("test_error_mode_focus\n");
        break;
    case 1:
       
        if (test_mode_view_error_count < 1)
            test_mode_view_error_count++;
        else
        {
            *_step = 2;
        }
      
        break;
    case 2:
        display_test_error_mode(7, 1);
        *_step = 3;
       
        break;
    case 4:
        write_p1(0x02, 0x00);
        write_p1(0x03, 0x00);
        write_p1(0x04, 0x01);
        display_test_error_mode(7, 0);
       
        break;
    default:
        break;
    }
}

void test_error_mode_frozen(uint8_t *_step)
{
    switch (*_step)
    {
    case 0:
        test_mode_view_error_count = 0;
        *_step = 1;
        write_p1(0xA9, read_p1(0xA9) & 0xF7);
      
        break;
    case 1:
        if (test_mode_view_error_count < 5)
            test_mode_view_error_count++;
        else
        {
            *_step = 2;
        }
        break;
    case 2:
        display_test_error_mode(3, 1);
        *_step = 3;
       
        break;
    case 3:
        test_mode_view_error_count = 0;
      
        break;
    case 4:
        write_p1(0xA9, read_p1(0xA9) | 0x08);
        display_test_error_mode(3, 0);
       
        break;
    default:
        break;
    }
}
void test_error_mode_delay(uint8_t *_step)
{
    switch (*_step)
    {
    case 0:
        test_mode_view_error_count = 0;
        *_step = 1;
        write_p1(0xA9, read_p1(0xA9) & 0xF7);
      
        break;
    case 1:
        if (test_mode_view_error_count < 5)
            test_mode_view_error_count++;
        else
        {
            *_step = 2;
        }
       
        break;
    case 2:
        display_test_error_mode(2, 1);
        *_step = 3;
      
        break;
    case 3:
        test_mode_view_error_count = 0;
       
        break;
    case 4:
    write_p1(0xA9, read_p1(0xA9) | 0x08);
        display_test_error_mode(2, 0);
      
        break;
    default:
        break;
    }
}
void test_error_mode_black(uint8_t *_step)
{
    switch (*_step)
    {
    case 0:
        test_mode_view_error_count = 0;
        *_step = 1;
       
        break;
    case 1:
        if (test_mode_view_error_count < 20)
            test_mode_view_error_count++;
        else
        {
            *_step = 2;
        }
       
    case 2:
        display_test_screen(1, 5, 5, 5);
        *_step = 3;
       
        break;
    case 4:
        display_test_screen(0, 5, 5, 5);
     
        break;
    }
}
void test_error_mode_mcu_invlid(uint8_t *_step)
{
    switch (*_step)
    {
    case 0:
        test_mode_view_error_count = 0;
        *_step = 3;
        display_test_error_mode(4, 1);
       
        break;
    case 3:
      
        break;
    case 4:
        display_test_error_mode(4, 0);
       
        break;
    default:
        break;
    }
}

void test_error_mode_mcu_loss(uint8_t *_step)
{
    switch (*_step)
    {
    case 0:
        test_mode_view_error_count = 0;
        *_step = 3;
        // display_test_error_mode(6, 1);
       
        break;
    case 3:
      
        break;
    case 4:
        // display_test_error_mode(6, 0);
      
        break;
    default:
        break;
    }
}
void test_error_mode_mcu_lcm(uint8_t *_step)
{
    switch (*_step)
    {
    case 0:
        test_mode_view_error_count = 0;
        *_step = 3;
        display_test_error_mode(5, 1);
       
        break;
    case 3:
       
        break;
    case 4:
        display_test_error_mode(5, 0);
       
        break;
    default:
        break;
    }
}

void test_error_mode_index(uint8_t _index, uint8_t *_on_off)
{
    switch (_index)
    {
    case 1:
        test_error_mode_noise(_on_off);
        break;
    case 2:
        test_error_mode_focus(_on_off);
        break;
    case 3:
        test_error_mode_black(_on_off);
        break;
    case 4:
        test_error_mode_frozen(_on_off);
        break;
    case 5:
        test_error_mode_delay(_on_off);
        break;
    case 6:
        test_error_mode_mcu_invlid(_on_off);
        break;
    case 7:
        //test_error_mode_mcu_loss(_on_off);
        test_error_mode_mcu_lcm(_on_off);
        break;
    case 8:
        //test_error_mode_mcu_lcm(_on_off);
        break;
    default:
        break;
    }
}
#endif
extern mirr_para_t MirrPara;
#if EF1E_TEST_WARNING_MODE
static uint8_t g_test_warning_mode = 0;
void set_StreamingMirrReWrnDis(uint8_t _dis);
void set_StreamingMirrReWnSwt(uint8_t _swt);
#endif

#if (USE_ISP_REPOWER_DEBUG == 1)
extern uint8_t cameraRepowerFlag;
#endif

void app_key_task(void)
{
    uint8_t ret = 0;
    MSG_T  pMsg;
    uint8_t _on_off = 0;

    if (SysGetLapseTick(tick_key_check) >= 100)
    {
        SysSetCurrentTick(&tick_key_check);
#if (EF1E_TEST_MODE == 4)
        test_error_mode_index(test_mode_index, &test_mode_step);

#endif
        ret = bsp_GetMsg(&pMsg);
        if (ret == 1)
        {
            DEBUG_LOGI("key_code=%d\r\n", pMsg.MsgCode);
            DEBUG_LOGI("key_pram=%ld\r\n", pMsg.MsgParam);
        }
        if (ret > 0)
        {
            switch (pMsg.MsgCode)
            {
            case EVENT_ID_KEY_CLICK:
                _on_off = MirrPara.vedio_para.StreamingMirrSwt;// i2c_cmd_get_on_off();
#if (EF1E_TEST_MODE==1)
                if (test_mode_flag > 0 && test_mode_flag < TEST_MODE_FLAG_MAX)
                {
                    test_mode_flag++;
                    if (test_mode_flag > TEST_MODE_FLAG_MAX)
                    {
                        test_mode_flag = 1;
                        test_mode_count = 0;
                    }
                    display_test_mode(test_mode_flag);
                }
                else if (double_click_test_mode_flag > 0)
                {
                    DEBUG_LOGI("double_click_test_mode_flag=%d\r\n", double_click_test_mode_flag);
                    double_click_test_mode_flag++;
                    if (double_click_test_mode_flag > 2)
                    {
                        double_click_test_mode_flag = 1;
                    }
                }
                else
#elif (EF1E_TEST_MODE == 4)
                if (test_mode_index > 0 && test_mode_index <= 8)
                {
                    
                   if(test_mode_step !=4)
                    {
                        test_mode_step = 4;
                        test_error_mode_index(test_mode_index, &test_mode_step);
                        
                        if (test_mode_index == 7)
                        {
                            test_mode_index = 0;
                        }
                    }
                    else
                    {
                        test_mode_index++;
                        test_mode_step = 0;
                     test_error_mode_index(test_mode_index, &test_mode_step);
                    }
                     
                }
                else

#endif
                {
#if USE_LIN_SLEEP_MODE
                	if(mirr_power_state_flag() == 1)
#endif
#if EF1E_TEST_WARNING_MODE
                	if (g_test_warning_mode >=1)
                	{
                    	if (g_test_warning_mode == 1)
                    	{
                    		//test warnning_mode ÆôÓÃ
                    		g_test_warning_mode = 2;
                    	}
                    	else if (g_test_warning_mode == 2)
                    	{
                    		g_test_warning_mode = 3;
                    	}
                    	else if (g_test_warning_mode == 3)
                    	{
                    		g_test_warning_mode = 1;
                    	}
                    	set_StreamingMirrReWrnDis(g_test_warning_mode-1);
                    	break;
                	}

#endif
                	{
                		MirrPara.vedio_para.StreamingMirrSwt = (_on_off==0)?1:0;
                		disp_on_off(MirrPara.vedio_para.StreamingMirrSwt);
                		MirrPara.StreamingMirrEnable = MirrPara.vedio_para.StreamingMirrSwt;
                		para_save();
                	}
                }

                break;
            case EVENT_ID_KEY_LONG_PRESSED:

#if (USE_ISP_REPOWER_DEBUG==0 && EF1E_TEST_MODE==0 && USE_ISP_LAW_BY_KEY==0 && EF1E_TEST_WARNING_MODE==0)
                DEBUG_LOGI("[%s] System Reset Start...\r\n", __FUNCTION__);
                nvram_write();
                MPQ3367_EN_off();
                OUTPUT_SET(PTB,PTB3);
                camera_power_onoff(0); //0 - camera off
                WDOG_Feed();
                KEA_Delay(500); //500ms
                WDOG_Feed();
                KEA_Delay(500); //500ms
                WDOG_Feed();
                KEA_Delay(500); //500ms
                DEBUG_LOGI("[%s] System Reset...\r\n", __FUNCTION__);
                NVIC_SystemReset();
#endif

            #if (USE_ISP_REPOWER_DEBUG == 1)
                cameraRepowerFlag = 1;
            #endif
                _on_off =  MirrPara.vedio_para.StreamingMirrSwt;//i2c_cmd_get_on_off();
                if (_on_off)
                {
#if (EF1E_TEST_MODE==1)                    
                    if (test_mode_flag == 0)
                    {
                        test_mode_flag = 1;
                        test_mode_count = 0;
                        display_test_mode(test_mode_flag);
                    }
                    else
                    {
                        test_mode_flag = 0;
                        test_mode_count = 0;
                        display_test_mode(test_mode_flag);
                    }
#elif (EF1E_TEST_MODE == 4)
                    if (test_mode_index == 0)
                    {
                        test_mode_index++;
                        test_mode_step = 0;
                        test_error_mode_index(test_mode_index, &test_mode_step);
                    }
                    else
                    {
                       
                        test_mode_step = 0;
                    }
#endif
#if USE_ISP_LAW_BY_KEY
                    isp_event_post(EV_ISP_WRITE_LAWS);
#endif
#if EF1E_TEST_WARNING_MODE
                    if (g_test_warning_mode >=1)
                    {
                    	DEBUG_LOGI("rewrn off\r\n");
                    	g_test_warning_mode = 0;
                    	set_StreamingMirrReWnSwt(0);
                    }
                    else
                    {
                    	DEBUG_LOGI("rewrn on\r\n");
                    	set_StreamingMirrReWnSwt(1);
                    	g_test_warning_mode = 1;
                    }
#endif
                }
       break;
            case EVENT_ID_KEY_DOUBLE_CLICK:
#if (EF1E_TEST_MODE==1)
                if (double_click_test_mode_flag == 0)
                {
                    DEBUG_LOGI("EVENT_ID_KEY_DOUBLE_CLICK enter\r\n");
                    disp_on_off(1);
                    display_test_screen(0, 0, 0, 0); //off osd
                    double_click_test_mode_flag = 1;
                    //display_test_mode(test_mode_flag);
                }else{
                    DEBUG_LOGI("EVENT_ID_KEY_DOUBLE_CLICK exit\r\n");
                    double_click_test_mode_flag = 0;
                }
#endif
                break;
            }
        }

#if (EF1E_TEST_MODE==1)        
        if (test_mode_flag == TEST_MODE_FLAG_MAX)
        {
            test_mode_count++;
            DEBUG_LOGI("test_mode_count=%d\r\n", test_mode_count);
            if (test_mode_count > 20)
            {
                display_test_mode(TEST_MODE_FLAG_MAX);
                test_mode_count = 0;
            }
        }
#endif        
    }
}
