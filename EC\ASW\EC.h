#ifndef EC_H_
#define EC_H_

#include <stdint.h>
#include "filter.h"
#include "db.h"

/**********************************************************************************************
* Local types
**********************************************************************************************/
typedef struct
{
    uint16_t gls_value;
    uint16_t als_value;
    uint16_t gls_average;
    uint16_t als_average;
    RingBuffer_t als_ring_buffer;
    RingBuffer_t gls_ring_buffer;

    uint16_t  gls_history;
    uint16_t  als_history;
    uint16_t  gls_final;
    uint16_t  als_final;

    uint8_t vin_value;
    uint16_t ec_value;
    uint16_t ec_fb_voltage;
    uint16_t iec_pwm;
    uint16_t oec_pwm;

    uint16_t target;
    uint8_t  bandwidth;

    uint16_t bl_target;
	uint16_t bl_current;

    uint8_t 	 first_skip;
	uint8_t      bl_changing;
}SensorData_t;

typedef union
{
    uint16_t Word;

    struct{
        uint8_t 	anti_piracy_lock:
        1;
        uint8_t 	first_running:
        1;
        uint8_t     ec_disabled_by_pin:
        1;
        uint8_t 	ec_disabled_by_idcm:
        1;
        uint16_t 	reserved  :
        12;

    }Bits;
}StateData_t;

typedef struct
{
	uint8_t iec;
	uint8_t oec;
    uint8_t v12;
    uint8_t v5;
    uint8_t v1_8;
    uint8_t lcm;
    uint8_t board;
    uint8_t sensor;

	uint8_t iec_sick;
	uint8_t oec_sick;	
	uint8_t v12_sick;
	uint8_t v5_sick;
	uint8_t v1_8_sick;
	uint8_t board_sick;
	uint8_t backlight_sick;
	uint8_t lcm_sick;
	uint8_t sensor_sick;
}IEMDiag_t;

typedef enum
{
	PWM_CH_NONE,
	PWM_CH_IEC,
	PWM_CH_OEC,
	PWM_CH_BL
}pwm_ch_t;

typedef struct
{
	int x;
	int y;
} position_t;


void EC_handle(void);
void EC_Ctrl(void);

uint8_t getMaxGlsValue(uint8_t array[], uint16_t len, scope_t setscope, position_t *pos);
uint8_t getAverageGlsValue(uint8_t array[], uint16_t len, scope_t setscope_t);



#endif
