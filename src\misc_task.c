#include "misc_task.h"
#include "mpq3367_pwm.h"
#include "state.h"
#include "my_osd_task.h"
#include "stdint.h"
#include "adc.h"
#include "para.h"
#include "power.h"

#include "app_key_task.h"  //EF1E_TEST_MODE


static uint16_t misc_adc_task_tick;
static uint16_t msic_adc_list[ADC_MAX];
static uint16_t pwm_task_tick;

uint8_t  ADC_FRONT_Average;
signed char bright_class = 0;	
uint8_t  duty_ratio;
int8_t DutyRatio_Hand;
static uint8_t  disableFlag;
uint8_t  gOverHeatFg = 0;
static  uint8_t  pwm_set_Flag = 0;
extern i2c_cmd I2c_cmd;
extern i2c_send_data  I2c_send_data;

extern uint8_t  osd_event_flag; //osd事件
extern uint16_t  osd_err_mask; //显示掩码，置位显示，0则不显示
extern uint8_t  mpq3367_enable_state;   //背光开启标志位

extern uint8_t i2c_send_data_flag;

// 添加摄像头连接异常显示相关的变量
static uint8_t camera_error_display_flag = 0;  // 摄像头连接异常显示标志
static uint32_t camera_error_display_start_tick = 0;  // 摄像头连接异常显示开始时间
#define CAMERA_ERROR_DISPLAY_TIME_MS 3000  // 摄像头连接异常显示时间3秒

static  uint8_t  count = 0;
static  uint32_t  S = 0;      //累加和
static  uint16_t  C = 0;      //本次采样值
static  uint16_t  A = 0;      //平均值
static uint8_t first_front_adc = 15;

static uint16_t temp_adc_delay_tick = 0;
static uint8_t temp_adc_loop_flag = 0;

#if (HW_VERSION_CHECK == 1)
uint8_t  gHWVersionErr = 0;
#endif

#if (ADC_12V_TEMPERATURE_COMPENSATION == 1)
static  uint8_t   temp_borad_adc_cnt = 0;
static uint16_t   temp_borad_adc_ave = 0;
static uint32_t   temp_borad_adc_sum = 0;
uint16_t GET_ADC_TEMP_BORAD(void)
{
	uint16_t adc_board = ADC_PollRead(ADC, ADC_CHANNEL_AD8);//msic_adc_list[ADC_TEMP_BOARD];
	//adc_board = 0xfff-adc_board;
	//DEBUG_LOGI("[%s] adc_board=%d\r\n", __FUNCTION__,adc_board);
	return adc_board;
}

uint16_t GET_ADC_TEMP_BORAD_Average()
{
    if( temp_borad_adc_cnt < 50 )
    {
        temp_borad_adc_sum += GET_ADC_TEMP_BORAD();           //读取ADC数据
        temp_borad_adc_cnt++;
    }
    if( temp_borad_adc_cnt >= 50 )
    {
        temp_borad_adc_ave = temp_borad_adc_sum / 50;
        temp_borad_adc_cnt = 0;
        temp_borad_adc_sum = 0;
    }
	//DEBUG_LOGI("[%s] temp_borad_adc_ave=%d\r\n", __FUNCTION__,temp_borad_adc_ave);
    return temp_borad_adc_ave;
}
#endif

uint8_t GET_ADC_FRONT(void)
{
	uint16_t ret_or = msic_adc_list[ADC_FRONT];
	ret_or = 0xfff-ret_or;

	uint8_t ret_b = ret_or>>4;
	//DEBUG_LOGI("[%s]front_adc:[%d-%d]\r\n", __FUNCTION__,ret_or,ret_b);
	if (first_front_adc==0)
		return ret_b;
	else
	{
		first_front_adc--;
		return 0;
	}

}
 uint16_t GET_ADC_LCM(void)
{
	return msic_adc_list[ADC_LCM];
}
uint8_t averageFilter(void)
{
	C = GET_ADC_FRONT();
	if (count == 0)
	{
		A = C;
		S = A * NVALUE;
		count = 1;
	}
	S = S - A + C;              //加上本次采样值，减去上次平均值
	A = S / NVALUE;                  //计算本次平均值
	return (uint8_t)A;
}

static  uint8_t   temp_adc_cnt = 0;
static uint16_t   temp_adc_ave = 0;
static uint32_t   temp_adc_sum = 0;
uint16_t GET_ADC_LCM_Average()
{
    if( temp_adc_cnt < 50 )
    {
        temp_adc_sum += GET_ADC_LCM();           //读取ADC数据
        temp_adc_cnt++;
    }
    if( temp_adc_cnt >= 50 )
    {
        temp_adc_ave = temp_adc_sum / 50;
        temp_adc_cnt = 0;
        temp_adc_sum = 0;
    }
    return temp_adc_ave;
}

uint8_t getOverHeatFg(void)
{
	return gOverHeatFg;
}

// 设置摄像头连接异常显示标志
void set_camera_error_display_flag(uint8_t flag)
{
	if (flag) {
		camera_error_display_flag = 1;
		SysSetCurrentTick(&camera_error_display_start_tick);
		DEBUG_LOGI("Camera error display started\r\n");
	} else {
		camera_error_display_flag = 0;
		DEBUG_LOGI("Camera error display stopped\r\n");
	}
}

// 检查摄像头连接异常显示是否超时
uint8_t is_camera_error_display_timeout(void)
{
	if (camera_error_display_flag) {
		if (SysGetLapseTick(camera_error_display_start_tick) >= CAMERA_ERROR_DISPLAY_TIME_MS) {
			camera_error_display_flag = 0;
			DEBUG_LOGI("Camera error display timeout\r\n");
			return 1;
		}
	}
	return 0;
}

// 获取摄像头连接异常显示标志
uint8_t get_camera_error_display_flag(void)
{
	return camera_error_display_flag;
}

void pwm_set_init(void)
{
	if(( GET_ADC_LCM() > 0) && (GET_ADC_LCM() <= 735/*45*/))  //当开机时处于35到25的死区时，默认为最暗的亮度
	{
		pwm_set_Flag = 1;
		mpq3367_pwm_set(0x95,20,100);
	}
	else  if(GET_ADC_LCM() > 735/*45*/)//当开机时，处于56到45时，按自动亮度，
	{
		mpq3367_pwm_set(0x95,20,100);
		/*
		bright_class = MirrPara.vedio_para.StreamingMirrBriAdjmt-10;// I2c_cmd.bright-10;
		duty_ratio = (((GET_ADC_FRONT()-ADC_darkest)*60)/(ADC_brightest-ADC_darkest))+20;
		DutyRatio_Hand =duty_ratio+(4*bright_class);
		if(DutyRatio_Hand > 80) DutyRatio_Hand = 80;
		if(DutyRatio_Hand < 20) DutyRatio_Hand = 20;
		mpq3367_pwm_set(0x95,DutyRatio_Hand,100);
		*/
	}
}
void pwm_set_ajust(void)
{
	bright_class = MirrPara.vedio_para.StreamingMirrBriAdjmt-10; //I2c_cmd.bright-10;
	duty_ratio = (((ADC_FRONT_Average-ADC_darkest)*60)/(ADC_brightest-ADC_darkest))+20; 
	DutyRatio_Hand =duty_ratio+(4*bright_class);
	if(DutyRatio_Hand > 80) DutyRatio_Hand = 80;
	if(DutyRatio_Hand < 20) DutyRatio_Hand = 20;
	mpq3367_pwm_set(0x95,DutyRatio_Hand,100);
}
//最低亮度40%，最亮80%  温度达到65，背光调最低，55恢复，80关背光
void PWM_set(uint16_t time)
{
	uint16_t temp_adc = TEMP_ADC_45_DEGREE;
	ADC_FRONT_Average = averageFilter();	//鍙栧钩鍧囧��
	//DEBUG_LOGI("[%s]==========ADC_FRONT_Average=%d\r\n", __FUNCTION__,ADC_FRONT_Average);
	if(ADC_FRONT_Average < ADC_darkest)
	{
		ADC_FRONT_Average = ADC_darkest;
	}
	if(ADC_FRONT_Average > ADC_brightest)
	{
		ADC_FRONT_Average = ADC_brightest;
	}

	if(temp_adc_delay_tick == 0)
	{
		if (SysGetLapseTick(temp_adc_delay_tick) > 6*1000) //delay 6s
		{
			SysSetCurrentTick(&temp_adc_delay_tick);
			DEBUG_LOGI("[%s]##############temp_adc_delay_tick = %d\r\n", __FUNCTION__,temp_adc_delay_tick);
		}
		pwm_set_Flag = 1;
	}
	else
	{
		temp_adc = GET_ADC_LCM_Average();   //做滤波
	}
	//DEBUG_LOGI("[%s]==========temp_adc=%d\r\n", __FUNCTION__,temp_adc);
	if(temp_adc > 0 && temp_adc < TEMP_ADC_85_DEGREE)  //高于98度关背光
	{
		//DEBUG_LOGI("[%s]***********screen off,temp_adc=%d\r\n", __FUNCTION__,temp_adc);
		gOverHeatFg = 1;
		pwm_set_Flag = 1;				 			
		if(disableFlag == 0)
		{
			I2c_send_data.Error_type |= ERR_NTC;
			i2c_send_data_flag = 1;
			state_add_err(ERR_NTC);
			//osd_event_flag |= OSD_EVENT_ERR; //显示图像错误
			//osd_err_mask |= ERR_NTC;				
			disableFlag = 1;
		}
		temp_adc_loop_flag = 1;
	}	
	else if(( temp_adc > TEMP_ADC_75_DEGREE/*34*/) && (temp_adc < TEMP_ADC_55_DEGREE/*46*/))
	{
		gOverHeatFg = 0;                                    //turn on lcd backlight
		if(mpq3367_enable_state == 0 && MirrPara.StreamingMirrEnable == 1/*I2c_cmd.on_off == 1*/)
		{
			if(get_display_init_flag()>MIPI_INIT_CHECK_OVER)
				mpq3367_enable();   //set display level 0 (minmal light)
		}
		mpq3367_pwm_set(0x95,20,100); 
		//DEBUG_LOGI("[%s]***********pwm=20%,temp_adc=%d\r\n", __FUNCTION__,temp_adc);
		disableFlag = 0;
		pwm_set_Flag = 1;				 			
		temp_adc_loop_flag = 1;
	}
	else if (temp_adc >= TEMP_ADC_45_DEGREE/*56*/)
	{
		//DEBUG_LOGI("[%s]***********normal,temp_adc=%d\r\n", __FUNCTION__,temp_adc);
		gOverHeatFg = 0;       //normal
		pwm_set_Flag = 0;				 
	}
	else
	{
		//do nothing
		//DEBUG_LOGI("[%s]***********do nothing,temp_adc=%d\r\n", __FUNCTION__,temp_adc);
		if(temp_adc >= TEMP_ADC_85_DEGREE && temp_adc <= TEMP_ADC_75_DEGREE){ //dead zone, screen brigth 20%
			//DEBUG_LOGI("[%s]***********dead zone\r\n", __FUNCTION__);
			if(temp_adc_loop_flag == 0)
			{
				DEBUG_LOGI("[%s]***********dead zone, temp_adc_loop_flag==0, set screen brigth 20%\r\n", __FUNCTION__);
				mpq3367_pwm_set(0x95,20,100);
				temp_adc_loop_flag = 1;
				pwm_set_Flag=1;
			}
		}
	}
	if(pwm_set_Flag == 0)
	{
		if(mpq3367_enable_state == 0 && MirrPara.StreamingMirrEnable == 1/*I2c_cmd.on_off == 1*/)
		{
			if(get_display_init_flag()>MIPI_INIT_CHECK_OVER && lin_get_sleep_flag() == 0)
			{
				// 修改：添加摄像头连接异常显示的特殊处理
				// 即使在低功耗状态下，如果需要显示摄像头连接异常，也要启用背光
				if(get_power_state()!=POWER_OFF_2MIN && get_power_state()!=POWER_OFF_7MIN)
				{
					mpq3367_enable();
				}
				else if(get_camera_error_display_flag())
				{
					// 在低功耗状态下，如果需要显示摄像头连接异常，临时启用背光
					mpq3367_enable();
					DEBUG_LOGI("Enable backlight for camera error display in low power mode\r\n");
				}
			}
		}
	}

	// 检查摄像头连接异常显示是否超时
	if(is_camera_error_display_timeout())
	{
		// 如果摄像头连接异常显示超时，且当前处于低功耗状态，关闭背光
		if((get_power_state()==POWER_OFF_2MIN || get_power_state()==POWER_OFF_7MIN) && mpq3367_enable_state == 1)
		{
			mpq3367_disable();
			DEBUG_LOGI("Disable backlight after camera error display timeout in low power mode\r\n");
		}
	}

#if (EF1E_TEST_MODE == 1)
		extern uint8_t  double_click_test_mode_flag;
		if(double_click_test_mode_flag==1){
			DEBUG_LOGI("[%s] LCM bright 20%\r\n", __FUNCTION__);
			mpq3367_pwm_set(0x95,20,100);
		}else if(double_click_test_mode_flag==2){
			DEBUG_LOGI("[%s] LCM bright 80%\r\n", __FUNCTION__);
			mpq3367_pwm_set(0x95,80,100);
		}
		else
#endif
		pwm_set_ajust();
	}	
	if(gOverHeatFg == 0) 
	{
		I2c_send_data.Error_type  &= ~ERR_NTC;
		i2c_send_data_flag = 1;
		//osd_err_mask &= ~ERR_NTC;
		state_clear_err(ERR_NTC);
//		osd_event_flag |= OSD_EVENT_ERR; 
//		osd_err_mask |= ERR_NTC;
	}
}


uint16_t filter_lowpass(uint16_t a, uint16_t b, uint8_t sens)
{
	return a + (b / sens) - (a / sens);
}
void misc_task(void)
{
	uint8_t i=0;

//  if (SysGetLapseTick(misc_adc_task_tick) > 10)
    {
//        SysSetCurrentTick(&misc_adc_task_tick);
        uint8_t adc_list[ADC_MAX] = {	ADC_CHANNEL_AD1 ,ADC_CHANNEL_AD4 ,ADC_CHANNEL_AD5 ,ADC_CHANNEL_AD8 ,ADC_CHANNEL_AD14 };
        for(i = 0;i<ADC_MAX;i++)
        {
        	msic_adc_list[i] = filter_lowpass(msic_adc_list[i],ADC_PollRead(ADC, adc_list[i]),5);
			//DEBUG_LOGI("msic_adc_list[%x]",msic_adc_list[i]);
        }
    }
//    if (SysGetLapseTick(pwm_task_tick) > 10)
    {
//      SysSetCurrentTick(&pwm_task_tick);
        PWM_set(100);
    }

#if (HW_VERSION_CHECK == 1)
	#define RANGE 100
	uint16_t adc_hw = ADC_PollRead(ADC, ADC_CHANNEL_AD13);
	//DEBUG_LOGI("HW VERSION ADC:0x%x\r\n", adc_hw);

	#if (HW_VERSION_TYPE == HW_VERSION_TYPE_5DOT1K)
	if(adc_hw > 0x560-RANGE && adc_hw < 0x560+RANGE) //5.1k(0x55E~0x56B) 3.3k(0x3EC~0x3F7)
	#elif (HW_VERSION_TYPE == HW_VERSION_TYPE_3DOT3K)
	if(adc_hw > 0x3F0-RANGE && adc_hw < 0x3F0+RANGE)
	#endif
	{
		//hardware matched, do nothings
	}else{
		state_add_err(ERR_HW_VER);
		gHWVersionErr = 1;
		DEBUG_LOGI("HW VERSION ADC:0x%x, error!\r\n", adc_hw);
	}
#endif
}

void misc_init()
{
	ADC_ConfigType  ADC_Config = { {0} };
	/* Initialization of ADC module */
	ADC_Config.u8ClockDiv = ADC_ADIV_DIVIDE_4;
	ADC_Config.u8ClockSource = CLOCK_SOURCE_BUS_CLOCK;
	ADC_Config.u8Mode = ADC_MODE_12BIT;
	ADC_Config.sSetting.bContinuousEn = 1;
	ADC_Config.sSetting.bLongSampleEn = 1;
	ADC_Config.u16PinControl = 0x2132; 			/* Disable I/O control on ADC channel 6*/
	ADC_Init(ADC, &ADC_Config);
}
