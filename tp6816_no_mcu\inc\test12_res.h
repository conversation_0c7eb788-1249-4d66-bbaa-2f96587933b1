#ifndef TEST12_RES_H_
#define TEST12_RES_H_
#include "config.h"
#include "app_key_task.h"
// test12 Resource ID:0x6806 Version:v0.70 
#define TEST12_VERSION_CODE_OFFSET	(64L * 1024L)
#define TEST12_VERSION_SIZE	16
/* version checking for TEST12 resource */
#define TEST12_VERSION_CNT		16
#define TEST12_MAGIC_START		0x53455268L//  68524553L
#define TEST12_MAGIC_END		0x86255435L//  35542586L
#define TEST12_HEADER_START_CNT	4
#define TEST12_HEADER_END_CNT	4
#define TEST12_HEADER_START_IDX	0
#define TEST12_RES_VER_IDX		4
#define TEST12_RES_SIZE_IDX	8
#define TEST12_HEADER_END_IDX	(TEST12_VERSION_CNT - TEST12_HEADER_END_CNT)
extern void get_test12_res_version(void);


#if (USE_LCM_FLIP == 1)
// page_0 Resource Layout
#define PAGE_0_CODE_OFFSET	(TEST12_VERSION_CODE_OFFSET + TEST12_VERSION_SIZE)
// page_0 com_17 - RLs B
#define page_0_com_17_rlsatt_ptr		0x0000
#define page_0_com_17_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_com_17_rlsatt_size		8
// page_0 com_17 - RLs Image
#define page_0_com_17_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_17_rlscode_size		96

// page_0 com_16 - RLs B
#define page_0_com_16_rlsatt_ptr		0x0008
#define page_0_com_16_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000008L
#define page_0_com_16_rlsatt_size		8
// page_0 com_16 - RLs Image
// same with com_17
#define page_0_com_16_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_16_rlscode_size		96

// page_0 com_15 - RLs B
#define page_0_com_15_rlsatt_ptr		0x0010
#define page_0_com_15_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000010L
#define page_0_com_15_rlsatt_size		8
// page_0 com_15 - RLs Image
// same with com_17
#define page_0_com_15_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_15_rlscode_size		96

// page_0 com_14 - RLs B
#define page_0_com_14_rlsatt_ptr		0x0018
#define page_0_com_14_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000018L
#define page_0_com_14_rlsatt_size		8
// page_0 com_14 - RLs Image
// same with com_17
#define page_0_com_14_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_14_rlscode_size		96

// page_0 com_13 - RLs B
#define page_0_com_13_rlsatt_ptr		0x0020
#define page_0_com_13_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000020L
#define page_0_com_13_rlsatt_size		8
// page_0 com_13 - RLs Image
// same with com_17
#define page_0_com_13_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_13_rlscode_size		96

// page_0 com_12 - RLs B
#define page_0_com_12_rlsatt_ptr		0x0028
#define page_0_com_12_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000028L
#define page_0_com_12_rlsatt_size		8
// page_0 com_12 - RLs Image
// same with com_17
#define page_0_com_12_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_12_rlscode_size		96

// page_0 com_11 - RLs B
#define page_0_com_11_rlsatt_ptr		0x0030
#define page_0_com_11_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000030L
#define page_0_com_11_rlsatt_size		8
// page_0 com_11 - RLs Image
// same with com_17
#define page_0_com_11_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_11_rlscode_size		96

// page_0 com_10 - RLs B
#define page_0_com_10_rlsatt_ptr		0x0038
#define page_0_com_10_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000038L
#define page_0_com_10_rlsatt_size		8
// page_0 com_10 - RLs Image
// same with com_17
#define page_0_com_10_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_10_rlscode_size		96

// page_0 _dummy1_ - RLs B
#define page_0__dummy1__rlsatt_ptr		0x0040
#define page_0__dummy1__rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000040L
#define page_0__dummy1__rlsatt_size		4

// page_0 com_9 - RLs C
#define page_0_com_9_rlsatt_ptr		0x0044
#define page_0_com_9_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000044L
#define page_0_com_9_rlsatt_size		8
// page_0 com_9 - RLs Image
#define page_0_com_9_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_9_rlscode_size		120

// page_0 com_8 - RLs C
#define page_0_com_8_rlsatt_ptr		0x004c
#define page_0_com_8_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x0000004cL
#define page_0_com_8_rlsatt_size		8
// page_0 com_8 - RLs Image
// same with com_9
#define page_0_com_8_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_8_rlscode_size		120

// page_0 com_7 - RLs C
#define page_0_com_7_rlsatt_ptr		0x0054
#define page_0_com_7_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000054L
#define page_0_com_7_rlsatt_size		8
// page_0 com_7 - RLs Image
// same with com_9
#define page_0_com_7_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_7_rlscode_size		120

// page_0 com_6 - RLs C
#define page_0_com_6_rlsatt_ptr		0x005c
#define page_0_com_6_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x0000005cL
#define page_0_com_6_rlsatt_size		8
// page_0 com_6 - RLs Image
// same with com_9
#define page_0_com_6_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_6_rlscode_size		120

// page_0 com_5 - RLs C
#define page_0_com_5_rlsatt_ptr		0x0064
#define page_0_com_5_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000064L
#define page_0_com_5_rlsatt_size		8
// page_0 com_5 - RLs Image
// same with com_9
#define page_0_com_5_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_5_rlscode_size		120

// page_0 com_4 - RLs C
#define page_0_com_4_rlsatt_ptr		0x006c
#define page_0_com_4_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x0000006cL
#define page_0_com_4_rlsatt_size		8
// page_0 com_4 - RLs Image
// same with com_9
#define page_0_com_4_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_4_rlscode_size		120

// page_0 com_3 - RLs C
#define page_0_com_3_rlsatt_ptr		0x0074
#define page_0_com_3_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000074L
#define page_0_com_3_rlsatt_size		8
// page_0 com_3 - RLs Image
// same with com_9
#define page_0_com_3_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_3_rlscode_size		120

// page_0 com_2 - RLs C
#define page_0_com_2_rlsatt_ptr		0x007c
#define page_0_com_2_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x0000007cL
#define page_0_com_2_rlsatt_size		8
// page_0 com_2 - RLs Image
// same with com_9
#define page_0_com_2_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_2_rlscode_size		120

// page_0 _dummy2_ - RLs C
#define page_0__dummy2__rlsatt_ptr		0x0084
#define page_0__dummy2__rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000084L
#define page_0__dummy2__rlsatt_size		4

// page_0 com_0 - RLs D
#define page_0_com_0_rlsatt_ptr		0x0088
#define page_0_com_0_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000088L
#define page_0_com_0_rlsatt_size		8
// page_0 com_0 - RLs Image
#define page_0_com_0_rlscode_addr		PAGE_0_CODE_OFFSET + 0x00000204L
#define page_0_com_0_rlscode_size		3584

// page_0 com_1 - RLs D
#define page_0_com_1_rlsatt_ptr		0x0090
#define page_0_com_1_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000090L
#define page_0_com_1_rlsatt_size		8
// page_0 com_1 - RLs Image
#define page_0_com_1_rlscode_addr		PAGE_0_CODE_OFFSET + 0x00001004L
#define page_0_com_1_rlscode_size		524

// page_0 _dummy3_ - RLs D
#define page_0__dummy3__rlsatt_ptr		0x0098
#define page_0__dummy3__rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000098L
#define page_0__dummy3__rlsatt_size		4

// page_0 Row_Thread_A Components
#define page_0_Row_Thread_A_thread		0
#define page_0_Row_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Row_Thread_A_regsize		0
// page_0 Row_Thread_B Components
#define page_0_Row_Thread_B_thread		1
#define page_0_Row_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Row_Thread_B_regsize		0
// page_0 Row_Thread_C Components
#define page_0_Row_Thread_C_thread		2
#define page_0_Row_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Row_Thread_C_regsize		0
// page_0 Row_Thread_D Components
#define page_0_Row_Thread_D_thread		3
#define page_0_Row_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Row_Thread_D_regsize		0
// page_0 RLg_Thread_A Components
#define page_0_RLg_Thread_A_thread		0
#define page_0_RLg_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x0000009cL
#define page_0_RLg_Thread_A_regsize		16
// page_0 RLg_Thread_B Components
#define page_0_RLg_Thread_B_thread		1
#define page_0_RLg_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x000000acL
#define page_0_RLg_Thread_B_regsize		16
// page_0 RLg_Thread_C Components
#define page_0_RLg_Thread_C_thread		2
#define page_0_RLg_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x000000bcL
#define page_0_RLg_Thread_C_regsize		16
// page_0 RLg_Thread_D Components
#define page_0_RLg_Thread_D_thread		3
#define page_0_RLg_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x000000ccL
#define page_0_RLg_Thread_D_regsize		16
// page_0 RLs_Thread_A Components
#define page_0_RLs_Thread_A_thread		0
#define page_0_RLs_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x000000dcL
#define page_0_RLs_Thread_A_regsize		8
// page_0 RLs_Thread_B Components
#define page_0_RLs_Thread_B_thread		1
#define page_0_RLs_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x000000e4L
#define page_0_RLs_Thread_B_regsize		8
// page_0 RLs_Thread_C Components
#define page_0_RLs_Thread_C_thread		2
#define page_0_RLs_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x000000ecL
#define page_0_RLs_Thread_C_regsize		8
// page_0 RLs_Thread_D Components
#define page_0_RLs_Thread_D_thread		3
#define page_0_RLs_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x000000f4L
#define page_0_RLs_Thread_D_regsize		8
// page_0 Win_Thread_A Components
#define page_0_Win_Thread_A_thread		0
#define page_0_Win_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x000000fcL
#define page_0_Win_Thread_A_regsize		12
// page_0 Win_Thread_B Components
#define page_0_Win_Thread_B_thread		1
#define page_0_Win_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x00000108L
#define page_0_Win_Thread_B_regsize		12
// page_0 Win_Thread_C Components
#define page_0_Win_Thread_C_thread		2
#define page_0_Win_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x00000114L
#define page_0_Win_Thread_C_regsize		12
// page_0 Win_Thread_D Components
#define page_0_Win_Thread_D_thread		3
#define page_0_Win_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x00000120L
#define page_0_Win_Thread_D_regsize		12
// page_0 Mat_Thread_A Components
#define page_0_Mat_Thread_A_thread		0
#define page_0_Mat_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Mat_Thread_A_regsize		0
// page_0 Mat_Thread_B Components
#define page_0_Mat_Thread_B_thread		1
#define page_0_Mat_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Mat_Thread_B_regsize		0
// page_0 Mat_Thread_C Components
#define page_0_Mat_Thread_C_thread		2
#define page_0_Mat_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Mat_Thread_C_regsize		0
// page_0 Mat_Thread_D Components
#define page_0_Mat_Thread_D_thread		3
#define page_0_Mat_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Mat_Thread_D_regsize		0

#define PAGE_0_RES_SIZE	0x00001210L
#else
// page_0 Resource Layout
#define PAGE_0_CODE_OFFSET	(TEST12_VERSION_CODE_OFFSET + TEST12_VERSION_SIZE)
// page_0 com_17 - RLs B
#define page_0_com_17_rlsatt_ptr		0x0000
#define page_0_com_17_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_com_17_rlsatt_size		8
// page_0 com_17 - RLs Image
#define page_0_com_17_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_17_rlscode_size		96

// page_0 com_16 - RLs B
#define page_0_com_16_rlsatt_ptr		0x0008
#define page_0_com_16_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000008L
#define page_0_com_16_rlsatt_size		8
// page_0 com_16 - RLs Image
// same with com_17
#define page_0_com_16_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_16_rlscode_size		96

// page_0 com_15 - RLs B
#define page_0_com_15_rlsatt_ptr		0x0010
#define page_0_com_15_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000010L
#define page_0_com_15_rlsatt_size		8
// page_0 com_15 - RLs Image
// same with com_17
#define page_0_com_15_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_15_rlscode_size		96

// page_0 com_14 - RLs B
#define page_0_com_14_rlsatt_ptr		0x0018
#define page_0_com_14_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000018L
#define page_0_com_14_rlsatt_size		8
// page_0 com_14 - RLs Image
// same with com_17
#define page_0_com_14_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_14_rlscode_size		96

// page_0 com_13 - RLs B
#define page_0_com_13_rlsatt_ptr		0x0020
#define page_0_com_13_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000020L
#define page_0_com_13_rlsatt_size		8
// page_0 com_13 - RLs Image
// same with com_17
#define page_0_com_13_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_13_rlscode_size		96

// page_0 com_12 - RLs B
#define page_0_com_12_rlsatt_ptr		0x0028
#define page_0_com_12_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000028L
#define page_0_com_12_rlsatt_size		8
// page_0 com_12 - RLs Image
// same with com_17
#define page_0_com_12_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_12_rlscode_size		96

// page_0 com_11 - RLs B
#define page_0_com_11_rlsatt_ptr		0x0030
#define page_0_com_11_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000030L
#define page_0_com_11_rlsatt_size		8
// page_0 com_11 - RLs Image
// same with com_17
#define page_0_com_11_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_11_rlscode_size		96

// page_0 com_10 - RLs B
#define page_0_com_10_rlsatt_ptr		0x0038
#define page_0_com_10_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000038L
#define page_0_com_10_rlsatt_size		8
// page_0 com_10 - RLs Image
// same with com_17
#define page_0_com_10_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000012cL
#define page_0_com_10_rlscode_size		96

// page_0 _dummy1_ - RLs B
#define page_0__dummy1__rlsatt_ptr		0x0040
#define page_0__dummy1__rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000040L
#define page_0__dummy1__rlsatt_size		4

// page_0 com_9 - RLs C
#define page_0_com_9_rlsatt_ptr		0x0044
#define page_0_com_9_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000044L
#define page_0_com_9_rlsatt_size		8
// page_0 com_9 - RLs Image
#define page_0_com_9_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_9_rlscode_size		120

// page_0 com_8 - RLs C
#define page_0_com_8_rlsatt_ptr		0x004c
#define page_0_com_8_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x0000004cL
#define page_0_com_8_rlsatt_size		8
// page_0 com_8 - RLs Image
// same with com_9
#define page_0_com_8_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_8_rlscode_size		120

// page_0 com_7 - RLs C
#define page_0_com_7_rlsatt_ptr		0x0054
#define page_0_com_7_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000054L
#define page_0_com_7_rlsatt_size		8
// page_0 com_7 - RLs Image
// same with com_9
#define page_0_com_7_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_7_rlscode_size		120

// page_0 com_6 - RLs C
#define page_0_com_6_rlsatt_ptr		0x005c
#define page_0_com_6_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x0000005cL
#define page_0_com_6_rlsatt_size		8
// page_0 com_6 - RLs Image
// same with com_9
#define page_0_com_6_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_6_rlscode_size		120

// page_0 com_5 - RLs C
#define page_0_com_5_rlsatt_ptr		0x0064
#define page_0_com_5_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000064L
#define page_0_com_5_rlsatt_size		8
// page_0 com_5 - RLs Image
// same with com_9
#define page_0_com_5_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_5_rlscode_size		120

// page_0 com_4 - RLs C
#define page_0_com_4_rlsatt_ptr		0x006c
#define page_0_com_4_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x0000006cL
#define page_0_com_4_rlsatt_size		8
// page_0 com_4 - RLs Image
// same with com_9
#define page_0_com_4_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_4_rlscode_size		120

// page_0 com_3 - RLs C
#define page_0_com_3_rlsatt_ptr		0x0074
#define page_0_com_3_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000074L
#define page_0_com_3_rlsatt_size		8
// page_0 com_3 - RLs Image
// same with com_9
#define page_0_com_3_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_3_rlscode_size		120

// page_0 com_2 - RLs C
#define page_0_com_2_rlsatt_ptr		0x007c
#define page_0_com_2_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x0000007cL
#define page_0_com_2_rlsatt_size		8
// page_0 com_2 - RLs Image
// same with com_9
#define page_0_com_2_rlscode_addr		PAGE_0_CODE_OFFSET + 0x0000018cL
#define page_0_com_2_rlscode_size		120

// page_0 _dummy2_ - RLs C
#define page_0__dummy2__rlsatt_ptr		0x0084
#define page_0__dummy2__rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000084L
#define page_0__dummy2__rlsatt_size		4

// page_0 com_1 - RLs D
#define page_0_com_1_rlsatt_ptr		0x0088
#define page_0_com_1_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000088L
#define page_0_com_1_rlsatt_size		8
// page_0 com_1 - RLs Image
#define page_0_com_1_rlscode_addr		PAGE_0_CODE_OFFSET + 0x00000204L
#define page_0_com_1_rlscode_size		524

// page_0 com_0 - RLs D
#define page_0_com_0_rlsatt_ptr		0x0090
#define page_0_com_0_rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000090L
#define page_0_com_0_rlsatt_size		8
// page_0 com_0 - RLs Image
#define page_0_com_0_rlscode_addr		PAGE_0_CODE_OFFSET + 0x00000410L
#define page_0_com_0_rlscode_size		3584

// page_0 _dummy3_ - RLs D
#define page_0__dummy3__rlsatt_ptr		0x0098
#define page_0__dummy3__rlsatt_addr		PAGE_0_CODE_OFFSET + 0x00000098L
#define page_0__dummy3__rlsatt_size		4

// page_0 Row_Thread_A Components
#define page_0_Row_Thread_A_thread		0
#define page_0_Row_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Row_Thread_A_regsize		0
// page_0 Row_Thread_B Components
#define page_0_Row_Thread_B_thread		1
#define page_0_Row_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Row_Thread_B_regsize		0
// page_0 Row_Thread_C Components
#define page_0_Row_Thread_C_thread		2
#define page_0_Row_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Row_Thread_C_regsize		0
// page_0 Row_Thread_D Components
#define page_0_Row_Thread_D_thread		3
#define page_0_Row_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Row_Thread_D_regsize		0
// page_0 RLg_Thread_A Components
#define page_0_RLg_Thread_A_thread		0
#define page_0_RLg_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x0000009cL
#define page_0_RLg_Thread_A_regsize		16
// page_0 RLg_Thread_B Components
#define page_0_RLg_Thread_B_thread		1
#define page_0_RLg_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x000000acL
#define page_0_RLg_Thread_B_regsize		16
// page_0 RLg_Thread_C Components
#define page_0_RLg_Thread_C_thread		2
#define page_0_RLg_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x000000bcL
#define page_0_RLg_Thread_C_regsize		16
// page_0 RLg_Thread_D Components
#define page_0_RLg_Thread_D_thread		3
#define page_0_RLg_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x000000ccL
#define page_0_RLg_Thread_D_regsize		16
// page_0 RLs_Thread_A Components
#define page_0_RLs_Thread_A_thread		0
#define page_0_RLs_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x000000dcL
#define page_0_RLs_Thread_A_regsize		8
// page_0 RLs_Thread_B Components
#define page_0_RLs_Thread_B_thread		1
#define page_0_RLs_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x000000e4L
#define page_0_RLs_Thread_B_regsize		8
// page_0 RLs_Thread_C Components
#define page_0_RLs_Thread_C_thread		2
#define page_0_RLs_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x000000ecL
#define page_0_RLs_Thread_C_regsize		8
// page_0 RLs_Thread_D Components
#define page_0_RLs_Thread_D_thread		3
#define page_0_RLs_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x000000f4L
#define page_0_RLs_Thread_D_regsize		8
// page_0 Win_Thread_A Components
#define page_0_Win_Thread_A_thread		0
#define page_0_Win_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x000000fcL
#define page_0_Win_Thread_A_regsize		12
// page_0 Win_Thread_B Components
#define page_0_Win_Thread_B_thread		1
#define page_0_Win_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x00000108L
#define page_0_Win_Thread_B_regsize		12
// page_0 Win_Thread_C Components
#define page_0_Win_Thread_C_thread		2
#define page_0_Win_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x00000114L
#define page_0_Win_Thread_C_regsize		12
// page_0 Win_Thread_D Components
#define page_0_Win_Thread_D_thread		3
#define page_0_Win_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x00000120L
#define page_0_Win_Thread_D_regsize		12
// page_0 Mat_Thread_A Components
#define page_0_Mat_Thread_A_thread		0
#define page_0_Mat_Thread_A_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Mat_Thread_A_regsize		0
// page_0 Mat_Thread_B Components
#define page_0_Mat_Thread_B_thread		1
#define page_0_Mat_Thread_B_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Mat_Thread_B_regsize		0
// page_0 Mat_Thread_C Components
#define page_0_Mat_Thread_C_thread		2
#define page_0_Mat_Thread_C_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Mat_Thread_C_regsize		0
// page_0 Mat_Thread_D Components
#define page_0_Mat_Thread_D_thread		3
#define page_0_Mat_Thread_D_regaddr		PAGE_0_CODE_OFFSET + 0x00000000L
#define page_0_Mat_Thread_D_regsize		0

#define PAGE_0_RES_SIZE	0x00001210L

#endif


// page_1 Resource Layout
#define PAGE_1_CODE_OFFSET	(PAGE_0_CODE_OFFSET + PAGE_0_RES_SIZE)
// page_1 com_6 - RLs C
#define page_1_com_6_rlsatt_ptr		0x0000
#define page_1_com_6_rlsatt_addr		PAGE_1_CODE_OFFSET + 0x00000000L
#define page_1_com_6_rlsatt_size		8
// page_1 com_6 - RLs Image
#define page_1_com_6_rlscode_addr		PAGE_1_CODE_OFFSET + 0x000000d0L
#define page_1_com_6_rlscode_size		120

// page_1 com_5 - RLs C
#define page_1_com_5_rlsatt_ptr		0x0008
#define page_1_com_5_rlsatt_addr		PAGE_1_CODE_OFFSET + 0x00000008L
#define page_1_com_5_rlsatt_size		8
// page_1 com_5 - RLs Image
// same with com_6
#define page_1_com_5_rlscode_addr		PAGE_1_CODE_OFFSET + 0x000000d0L
#define page_1_com_5_rlscode_size		120

// page_1 com_4 - RLs C
#define page_1_com_4_rlsatt_ptr		0x0010
#define page_1_com_4_rlsatt_addr		PAGE_1_CODE_OFFSET + 0x00000010L
#define page_1_com_4_rlsatt_size		8
// page_1 com_4 - RLs Image
// same with com_6
#define page_1_com_4_rlscode_addr		PAGE_1_CODE_OFFSET + 0x000000d0L
#define page_1_com_4_rlscode_size		120

// page_1 com_3 - RLs C
#define page_1_com_3_rlsatt_ptr		0x0018
#define page_1_com_3_rlsatt_addr		PAGE_1_CODE_OFFSET + 0x00000018L
#define page_1_com_3_rlsatt_size		8
// page_1 com_3 - RLs Image
// same with com_6
#define page_1_com_3_rlscode_addr		PAGE_1_CODE_OFFSET + 0x000000d0L
#define page_1_com_3_rlscode_size		120

// page_1 com_2 - RLs C
#define page_1_com_2_rlsatt_ptr		0x0020
#define page_1_com_2_rlsatt_addr		PAGE_1_CODE_OFFSET + 0x00000020L
#define page_1_com_2_rlsatt_size		8
// page_1 com_2 - RLs Image
// same with com_6
#define page_1_com_2_rlscode_addr		PAGE_1_CODE_OFFSET + 0x000000d0L
#define page_1_com_2_rlscode_size		120

// page_1 com_1 - RLs C
#define page_1_com_1_rlsatt_ptr		0x0028
#define page_1_com_1_rlsatt_addr		PAGE_1_CODE_OFFSET + 0x00000028L
#define page_1_com_1_rlsatt_size		8
// page_1 com_1 - RLs Image
// same with com_6
#define page_1_com_1_rlscode_addr		PAGE_1_CODE_OFFSET + 0x000000d0L
#define page_1_com_1_rlscode_size		120

// page_1 _dummy2_ - RLs C
#define page_1__dummy2__rlsatt_ptr		0x0030
#define page_1__dummy2__rlsatt_addr		PAGE_1_CODE_OFFSET + 0x00000030L
#define page_1__dummy2__rlsatt_size		4

// page_1 com_0 - RLs D
#define page_1_com_0_rlsatt_ptr		0x0034
#define page_1_com_0_rlsatt_addr		PAGE_1_CODE_OFFSET + 0x00000034L
#define page_1_com_0_rlsatt_size		8
// page_1 com_0 - RLs Image
#define page_1_com_0_rlscode_addr		PAGE_1_CODE_OFFSET + 0x00000148L
#define page_1_com_0_rlscode_size		4292

// page_1 _dummy3_ - RLs D
#define page_1__dummy3__rlsatt_ptr		0x003c
#define page_1__dummy3__rlsatt_addr		PAGE_1_CODE_OFFSET + 0x0000003cL
#define page_1__dummy3__rlsatt_size		4

// page_1 Row_Thread_A Components
#define page_1_Row_Thread_A_thread		0
#define page_1_Row_Thread_A_regaddr		PAGE_1_CODE_OFFSET + 0x00000000L
#define page_1_Row_Thread_A_regsize		0
// page_1 Row_Thread_B Components
#define page_1_Row_Thread_B_thread		1
#define page_1_Row_Thread_B_regaddr		PAGE_1_CODE_OFFSET + 0x00000000L
#define page_1_Row_Thread_B_regsize		0
// page_1 Row_Thread_C Components
#define page_1_Row_Thread_C_thread		2
#define page_1_Row_Thread_C_regaddr		PAGE_1_CODE_OFFSET + 0x00000000L
#define page_1_Row_Thread_C_regsize		0
// page_1 Row_Thread_D Components
#define page_1_Row_Thread_D_thread		3
#define page_1_Row_Thread_D_regaddr		PAGE_1_CODE_OFFSET + 0x00000000L
#define page_1_Row_Thread_D_regsize		0
// page_1 RLg_Thread_A Components
#define page_1_RLg_Thread_A_thread		0
#define page_1_RLg_Thread_A_regaddr		PAGE_1_CODE_OFFSET + 0x00000040L
#define page_1_RLg_Thread_A_regsize		16
// page_1 RLg_Thread_B Components
#define page_1_RLg_Thread_B_thread		1
#define page_1_RLg_Thread_B_regaddr		PAGE_1_CODE_OFFSET + 0x00000050L
#define page_1_RLg_Thread_B_regsize		16
// page_1 RLg_Thread_C Components
#define page_1_RLg_Thread_C_thread		2
#define page_1_RLg_Thread_C_regaddr		PAGE_1_CODE_OFFSET + 0x00000060L
#define page_1_RLg_Thread_C_regsize		16
// page_1 RLg_Thread_D Components
#define page_1_RLg_Thread_D_thread		3
#define page_1_RLg_Thread_D_regaddr		PAGE_1_CODE_OFFSET + 0x00000070L
#define page_1_RLg_Thread_D_regsize		16
// page_1 RLs_Thread_A Components
#define page_1_RLs_Thread_A_thread		0
#define page_1_RLs_Thread_A_regaddr		PAGE_1_CODE_OFFSET + 0x00000080L
#define page_1_RLs_Thread_A_regsize		8
// page_1 RLs_Thread_B Components
#define page_1_RLs_Thread_B_thread		1
#define page_1_RLs_Thread_B_regaddr		PAGE_1_CODE_OFFSET + 0x00000088L
#define page_1_RLs_Thread_B_regsize		8
// page_1 RLs_Thread_C Components
#define page_1_RLs_Thread_C_thread		2
#define page_1_RLs_Thread_C_regaddr		PAGE_1_CODE_OFFSET + 0x00000090L
#define page_1_RLs_Thread_C_regsize		8
// page_1 RLs_Thread_D Components
#define page_1_RLs_Thread_D_thread		3
#define page_1_RLs_Thread_D_regaddr		PAGE_1_CODE_OFFSET + 0x00000098L
#define page_1_RLs_Thread_D_regsize		8
// page_1 Win_Thread_A Components
#define page_1_Win_Thread_A_thread		0
#define page_1_Win_Thread_A_regaddr		PAGE_1_CODE_OFFSET + 0x000000a0L
#define page_1_Win_Thread_A_regsize		12
// page_1 Win_Thread_B Components
#define page_1_Win_Thread_B_thread		1
#define page_1_Win_Thread_B_regaddr		PAGE_1_CODE_OFFSET + 0x000000acL
#define page_1_Win_Thread_B_regsize		12
// page_1 Win_Thread_C Components
#define page_1_Win_Thread_C_thread		2
#define page_1_Win_Thread_C_regaddr		PAGE_1_CODE_OFFSET + 0x000000b8L
#define page_1_Win_Thread_C_regsize		12
// page_1 Win_Thread_D Components
#define page_1_Win_Thread_D_thread		3
#define page_1_Win_Thread_D_regaddr		PAGE_1_CODE_OFFSET + 0x000000c4L
#define page_1_Win_Thread_D_regsize		12
// page_1 Mat_Thread_A Components
#define page_1_Mat_Thread_A_thread		0
#define page_1_Mat_Thread_A_regaddr		PAGE_1_CODE_OFFSET + 0x00000000L
#define page_1_Mat_Thread_A_regsize		0
// page_1 Mat_Thread_B Components
#define page_1_Mat_Thread_B_thread		1
#define page_1_Mat_Thread_B_regaddr		PAGE_1_CODE_OFFSET + 0x00000000L
#define page_1_Mat_Thread_B_regsize		0
// page_1 Mat_Thread_C Components
#define page_1_Mat_Thread_C_thread		2
#define page_1_Mat_Thread_C_regaddr		PAGE_1_CODE_OFFSET + 0x00000000L
#define page_1_Mat_Thread_C_regsize		0
// page_1 Mat_Thread_D Components
#define page_1_Mat_Thread_D_thread		3
#define page_1_Mat_Thread_D_regaddr		PAGE_1_CODE_OFFSET + 0x00000000L
#define page_1_Mat_Thread_D_regsize		0

#define PAGE_1_RES_SIZE	0x0000120cL


#if (USE_LCM_FLIP == 1)
// page_2 Resource Layout
#define PAGE_2_CODE_OFFSET	(PAGE_1_CODE_OFFSET + PAGE_1_RES_SIZE)
// page_2 com_7 - RLs C
#define page_2_com_7_rlsatt_ptr		0x0000
#define page_2_com_7_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_com_7_rlsatt_size		8
// page_2 com_7 - RLs Image
#define page_2_com_7_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_7_rlscode_size		120

// page_2 com_6 - RLs C
#define page_2_com_6_rlsatt_ptr		0x0008
#define page_2_com_6_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000008L
#define page_2_com_6_rlsatt_size		8
// page_2 com_6 - RLs Image
// same with com_7
#define page_2_com_6_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_6_rlscode_size		120

// page_2 com_5 - RLs C
#define page_2_com_5_rlsatt_ptr		0x0010
#define page_2_com_5_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000010L
#define page_2_com_5_rlsatt_size		8
// page_2 com_5 - RLs Image
// same with com_7
#define page_2_com_5_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_5_rlscode_size		120

// page_2 com_4 - RLs C
#define page_2_com_4_rlsatt_ptr		0x0018
#define page_2_com_4_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000018L
#define page_2_com_4_rlsatt_size		8
// page_2 com_4 - RLs Image
// same with com_7
#define page_2_com_4_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_4_rlscode_size		120

// page_2 com_3 - RLs C
#define page_2_com_3_rlsatt_ptr		0x0020
#define page_2_com_3_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000020L
#define page_2_com_3_rlsatt_size		8
// page_2 com_3 - RLs Image
// same with com_7
#define page_2_com_3_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_3_rlscode_size		120

// page_2 com_2 - RLs C
#define page_2_com_2_rlsatt_ptr		0x0028
#define page_2_com_2_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000028L
#define page_2_com_2_rlsatt_size		8
// page_2 com_2 - RLs Image
// same with com_7
#define page_2_com_2_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_2_rlscode_size		120

// page_2 com_1 - RLs C
#define page_2_com_1_rlsatt_ptr		0x0030
#define page_2_com_1_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000030L
#define page_2_com_1_rlsatt_size		8
// page_2 com_1 - RLs Image
// same with com_7
#define page_2_com_1_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_1_rlscode_size		120

// page_2 _dummy2_ - RLs C
#define page_2__dummy2__rlsatt_ptr		0x0038
#define page_2__dummy2__rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000038L
#define page_2__dummy2__rlsatt_size		4

// page_2 com_0 - RLs D
#define page_2_com_0_rlsatt_ptr		0x003c
#define page_2_com_0_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x0000003cL
#define page_2_com_0_rlsatt_size		8
// page_2 com_0 - RLs Image
#define page_2_com_0_rlscode_addr		PAGE_2_CODE_OFFSET + 0x00000150L
#define page_2_com_0_rlscode_size		5012

// page_2 _dummy3_ - RLs D
#define page_2__dummy3__rlsatt_ptr		0x0044
#define page_2__dummy3__rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000044L
#define page_2__dummy3__rlsatt_size		4

// page_2 Row_Thread_A Components
#define page_2_Row_Thread_A_thread		0
#define page_2_Row_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Row_Thread_A_regsize		0
// page_2 Row_Thread_B Components
#define page_2_Row_Thread_B_thread		1
#define page_2_Row_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Row_Thread_B_regsize		0
// page_2 Row_Thread_C Components
#define page_2_Row_Thread_C_thread		2
#define page_2_Row_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Row_Thread_C_regsize		0
// page_2 Row_Thread_D Components
#define page_2_Row_Thread_D_thread		3
#define page_2_Row_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Row_Thread_D_regsize		0
// page_2 RLg_Thread_A Components
#define page_2_RLg_Thread_A_thread		0
#define page_2_RLg_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x00000048L
#define page_2_RLg_Thread_A_regsize		16
// page_2 RLg_Thread_B Components
#define page_2_RLg_Thread_B_thread		1
#define page_2_RLg_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x00000058L
#define page_2_RLg_Thread_B_regsize		16
// page_2 RLg_Thread_C Components
#define page_2_RLg_Thread_C_thread		2
#define page_2_RLg_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x00000068L
#define page_2_RLg_Thread_C_regsize		16
// page_2 RLg_Thread_D Components
#define page_2_RLg_Thread_D_thread		3
#define page_2_RLg_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x00000078L
#define page_2_RLg_Thread_D_regsize		16
// page_2 RLs_Thread_A Components
#define page_2_RLs_Thread_A_thread		0
#define page_2_RLs_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x00000088L
#define page_2_RLs_Thread_A_regsize		8
// page_2 RLs_Thread_B Components
#define page_2_RLs_Thread_B_thread		1
#define page_2_RLs_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x00000090L
#define page_2_RLs_Thread_B_regsize		8
// page_2 RLs_Thread_C Components
#define page_2_RLs_Thread_C_thread		2
#define page_2_RLs_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x00000098L
#define page_2_RLs_Thread_C_regsize		8
// page_2 RLs_Thread_D Components
#define page_2_RLs_Thread_D_thread		3
#define page_2_RLs_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x000000a0L
#define page_2_RLs_Thread_D_regsize		8
// page_2 Win_Thread_A Components
#define page_2_Win_Thread_A_thread		0
#define page_2_Win_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x000000a8L
#define page_2_Win_Thread_A_regsize		12
// page_2 Win_Thread_B Components
#define page_2_Win_Thread_B_thread		1
#define page_2_Win_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x000000b4L
#define page_2_Win_Thread_B_regsize		12
// page_2 Win_Thread_C Components
#define page_2_Win_Thread_C_thread		2
#define page_2_Win_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x000000c0L
#define page_2_Win_Thread_C_regsize		12
// page_2 Win_Thread_D Components
#define page_2_Win_Thread_D_thread		3
#define page_2_Win_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x000000ccL
#define page_2_Win_Thread_D_regsize		12
// page_2 Mat_Thread_A Components
#define page_2_Mat_Thread_A_thread		0
#define page_2_Mat_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Mat_Thread_A_regsize		0
// page_2 Mat_Thread_B Components
#define page_2_Mat_Thread_B_thread		1
#define page_2_Mat_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Mat_Thread_B_regsize		0
// page_2 Mat_Thread_C Components
#define page_2_Mat_Thread_C_thread		2
#define page_2_Mat_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Mat_Thread_C_regsize		0
// page_2 Mat_Thread_D Components
#define page_2_Mat_Thread_D_thread		3
#define page_2_Mat_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Mat_Thread_D_regsize		0

#define PAGE_2_RES_SIZE	0x000014e4L

#else

// page_2 Resource Layout
#define PAGE_2_CODE_OFFSET	(PAGE_1_CODE_OFFSET + PAGE_1_RES_SIZE)
// page_2 com_1 - RLs C
#define page_2_com_1_rlsatt_ptr		0x0000
#define page_2_com_1_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_com_1_rlsatt_size		8
// page_2 com_1 - RLs Image
#define page_2_com_1_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_1_rlscode_size		120

// page_2 com_2 - RLs C
#define page_2_com_2_rlsatt_ptr		0x0008
#define page_2_com_2_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000008L
#define page_2_com_2_rlsatt_size		8
// page_2 com_2 - RLs Image
// same with com_1
#define page_2_com_2_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_2_rlscode_size		120

// page_2 com_3 - RLs C
#define page_2_com_3_rlsatt_ptr		0x0010
#define page_2_com_3_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000010L
#define page_2_com_3_rlsatt_size		8
// page_2 com_3 - RLs Image
// same with com_1
#define page_2_com_3_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_3_rlscode_size		120

// page_2 com_4 - RLs C
#define page_2_com_4_rlsatt_ptr		0x0018
#define page_2_com_4_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000018L
#define page_2_com_4_rlsatt_size		8
// page_2 com_4 - RLs Image
// same with com_1
#define page_2_com_4_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_4_rlscode_size		120

// page_2 com_5 - RLs C
#define page_2_com_5_rlsatt_ptr		0x0020
#define page_2_com_5_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000020L
#define page_2_com_5_rlsatt_size		8
// page_2 com_5 - RLs Image
// same with com_1
#define page_2_com_5_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_5_rlscode_size		120

// page_2 com_6 - RLs C
#define page_2_com_6_rlsatt_ptr		0x0028
#define page_2_com_6_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000028L
#define page_2_com_6_rlsatt_size		8
// page_2 com_6 - RLs Image
// same with com_1
#define page_2_com_6_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_6_rlscode_size		120

// page_2 com_7 - RLs C
#define page_2_com_7_rlsatt_ptr		0x0030
#define page_2_com_7_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000030L
#define page_2_com_7_rlsatt_size		8
// page_2 com_7 - RLs Image
// same with com_1
#define page_2_com_7_rlscode_addr		PAGE_2_CODE_OFFSET + 0x000000d8L
#define page_2_com_7_rlscode_size		120

// page_2 _dummy2_ - RLs C
#define page_2__dummy2__rlsatt_ptr		0x0038
#define page_2__dummy2__rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000038L
#define page_2__dummy2__rlsatt_size		4

// page_2 com_0 - RLs D
#define page_2_com_0_rlsatt_ptr		0x003c
#define page_2_com_0_rlsatt_addr		PAGE_2_CODE_OFFSET + 0x0000003cL
#define page_2_com_0_rlsatt_size		8
// page_2 com_0 - RLs Image
#define page_2_com_0_rlscode_addr		PAGE_2_CODE_OFFSET + 0x00000150L
#define page_2_com_0_rlscode_size		5012

// page_2 _dummy3_ - RLs D
#define page_2__dummy3__rlsatt_ptr		0x0044
#define page_2__dummy3__rlsatt_addr		PAGE_2_CODE_OFFSET + 0x00000044L
#define page_2__dummy3__rlsatt_size		4

// page_2 Row_Thread_A Components
#define page_2_Row_Thread_A_thread		0
#define page_2_Row_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Row_Thread_A_regsize		0
// page_2 Row_Thread_B Components
#define page_2_Row_Thread_B_thread		1
#define page_2_Row_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Row_Thread_B_regsize		0
// page_2 Row_Thread_C Components
#define page_2_Row_Thread_C_thread		2
#define page_2_Row_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Row_Thread_C_regsize		0
// page_2 Row_Thread_D Components
#define page_2_Row_Thread_D_thread		3
#define page_2_Row_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Row_Thread_D_regsize		0
// page_2 RLg_Thread_A Components
#define page_2_RLg_Thread_A_thread		0
#define page_2_RLg_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x00000048L
#define page_2_RLg_Thread_A_regsize		16
// page_2 RLg_Thread_B Components
#define page_2_RLg_Thread_B_thread		1
#define page_2_RLg_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x00000058L
#define page_2_RLg_Thread_B_regsize		16
// page_2 RLg_Thread_C Components
#define page_2_RLg_Thread_C_thread		2
#define page_2_RLg_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x00000068L
#define page_2_RLg_Thread_C_regsize		16
// page_2 RLg_Thread_D Components
#define page_2_RLg_Thread_D_thread		3
#define page_2_RLg_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x00000078L
#define page_2_RLg_Thread_D_regsize		16
// page_2 RLs_Thread_A Components
#define page_2_RLs_Thread_A_thread		0
#define page_2_RLs_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x00000088L
#define page_2_RLs_Thread_A_regsize		8
// page_2 RLs_Thread_B Components
#define page_2_RLs_Thread_B_thread		1
#define page_2_RLs_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x00000090L
#define page_2_RLs_Thread_B_regsize		8
// page_2 RLs_Thread_C Components
#define page_2_RLs_Thread_C_thread		2
#define page_2_RLs_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x00000098L
#define page_2_RLs_Thread_C_regsize		8
// page_2 RLs_Thread_D Components
#define page_2_RLs_Thread_D_thread		3
#define page_2_RLs_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x000000a0L
#define page_2_RLs_Thread_D_regsize		8
// page_2 Win_Thread_A Components
#define page_2_Win_Thread_A_thread		0
#define page_2_Win_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x000000a8L
#define page_2_Win_Thread_A_regsize		12
// page_2 Win_Thread_B Components
#define page_2_Win_Thread_B_thread		1
#define page_2_Win_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x000000b4L
#define page_2_Win_Thread_B_regsize		12
// page_2 Win_Thread_C Components
#define page_2_Win_Thread_C_thread		2
#define page_2_Win_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x000000c0L
#define page_2_Win_Thread_C_regsize		12
// page_2 Win_Thread_D Components
#define page_2_Win_Thread_D_thread		3
#define page_2_Win_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x000000ccL
#define page_2_Win_Thread_D_regsize		12
// page_2 Mat_Thread_A Components
#define page_2_Mat_Thread_A_thread		0
#define page_2_Mat_Thread_A_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Mat_Thread_A_regsize		0
// page_2 Mat_Thread_B Components
#define page_2_Mat_Thread_B_thread		1
#define page_2_Mat_Thread_B_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Mat_Thread_B_regsize		0
// page_2 Mat_Thread_C Components
#define page_2_Mat_Thread_C_thread		2
#define page_2_Mat_Thread_C_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Mat_Thread_C_regsize		0
// page_2 Mat_Thread_D Components
#define page_2_Mat_Thread_D_thread		3
#define page_2_Mat_Thread_D_regaddr		PAGE_2_CODE_OFFSET + 0x00000000L
#define page_2_Mat_Thread_D_regsize		0

#define PAGE_2_RES_SIZE	0x000014e4L


#endif

// page_3 Resource Layout
#define PAGE_3_CODE_OFFSET	(PAGE_2_CODE_OFFSET + PAGE_2_RES_SIZE)
// page_3 com_0 - RLg B
#define page_3_com_0_rlgatt_ptr		0x0454
#define page_3_com_0_rlgatt_addr		PAGE_3_CODE_OFFSET + 0x00000454L
#define page_3_com_0_rlgatt_size		8
// page_3 com_0 - RLg Image
#define page_3_com_0_rlgoram_ptr		0x0000
#define page_3_com_0_rlgcode_addr		PAGE_3_CODE_OFFSET + 0x00000000L
#define page_3_com_0_rlgcode_size		368

// page_3 com_1 - RLg C
#define page_3_com_1_rlgatt_ptr		0x045c
#define page_3_com_1_rlgatt_addr		PAGE_3_CODE_OFFSET + 0x0000045cL
#define page_3_com_1_rlgatt_size		8
// page_3 com_1 - RLg Image
#define page_3_com_1_rlgoram_ptr		0x0170
#define page_3_com_1_rlgcode_addr		PAGE_3_CODE_OFFSET + 0x00000170L
#define page_3_com_1_rlgcode_size		380

// page_3 com_2 - RLg D
#define page_3_com_2_rlgatt_ptr		0x0464
#define page_3_com_2_rlgatt_addr		PAGE_3_CODE_OFFSET + 0x00000464L
#define page_3_com_2_rlgatt_size		8
// page_3 com_2 - RLg Image
#define page_3_com_2_rlgoram_ptr		0x02ec
#define page_3_com_2_rlgcode_addr		PAGE_3_CODE_OFFSET + 0x000002ecL
#define page_3_com_2_rlgcode_size		360

// page_3 com_0 - RLs A
#define page_3_com_0_rlsatt_ptr		0x046c
#define page_3_com_0_rlsatt_addr		PAGE_3_CODE_OFFSET + 0x0000046cL
#define page_3_com_0_rlsatt_size		8
// page_3 com_0 - RLs Image
#define page_3_com_0_rlscode_addr		PAGE_3_CODE_OFFSET + 0x0000052cL
#define page_3_com_0_rlscode_size		356

// page_3 _dummy0_ - RLs A
#define page_3__dummy0__rlsatt_ptr		0x0474
#define page_3__dummy0__rlsatt_addr		PAGE_3_CODE_OFFSET + 0x00000474L
#define page_3__dummy0__rlsatt_size		4

// page_3 com_3 - RLs B
#define page_3_com_3_rlsatt_ptr		0x0478
#define page_3_com_3_rlsatt_addr		PAGE_3_CODE_OFFSET + 0x00000478L
#define page_3_com_3_rlsatt_size		8
// page_3 com_3 - RLs Image
#define page_3_com_3_rlscode_addr		PAGE_3_CODE_OFFSET + 0x00000690L
#define page_3_com_3_rlscode_size		4236

// page_3 _dummy1_ - RLs B
#define page_3__dummy1__rlsatt_ptr		0x0480
#define page_3__dummy1__rlsatt_addr		PAGE_3_CODE_OFFSET + 0x00000480L
#define page_3__dummy1__rlsatt_size		4

// page_3 com_1 - RLs C
#define page_3_com_1_rlsatt_ptr		0x0484
#define page_3_com_1_rlsatt_addr		PAGE_3_CODE_OFFSET + 0x00000484L
#define page_3_com_1_rlsatt_size		8
// page_3 com_1 - RLs Image
#define page_3_com_1_rlscode_addr		PAGE_3_CODE_OFFSET + 0x0000171cL
#define page_3_com_1_rlscode_size		4084

// page_3 _dummy2_ - RLs C
#define page_3__dummy2__rlsatt_ptr		0x048c
#define page_3__dummy2__rlsatt_addr		PAGE_3_CODE_OFFSET + 0x0000048cL
#define page_3__dummy2__rlsatt_size		4

// page_3 com_2 - RLs D
#define page_3_com_2_rlsatt_ptr		0x0490
#define page_3_com_2_rlsatt_addr		PAGE_3_CODE_OFFSET + 0x00000490L
#define page_3_com_2_rlsatt_size		8
// page_3 com_2 - RLs Image
#define page_3_com_2_rlscode_addr		PAGE_3_CODE_OFFSET + 0x00002710L
#define page_3_com_2_rlscode_size		2400

// page_3 _dummy3_ - RLs D
#define page_3__dummy3__rlsatt_ptr		0x0498
#define page_3__dummy3__rlsatt_addr		PAGE_3_CODE_OFFSET + 0x00000498L
#define page_3__dummy3__rlsatt_size		4

// page_3 Row_Thread_A Components
#define page_3_Row_Thread_A_thread		0
#define page_3_Row_Thread_A_regaddr		PAGE_3_CODE_OFFSET + 0x00000000L
#define page_3_Row_Thread_A_regsize		0
// page_3 Row_Thread_B Components
#define page_3_Row_Thread_B_thread		1
#define page_3_Row_Thread_B_regaddr		PAGE_3_CODE_OFFSET + 0x00000000L
#define page_3_Row_Thread_B_regsize		0
// page_3 Row_Thread_C Components
#define page_3_Row_Thread_C_thread		2
#define page_3_Row_Thread_C_regaddr		PAGE_3_CODE_OFFSET + 0x00000000L
#define page_3_Row_Thread_C_regsize		0
// page_3 Row_Thread_D Components
#define page_3_Row_Thread_D_thread		3
#define page_3_Row_Thread_D_regaddr		PAGE_3_CODE_OFFSET + 0x00000000L
#define page_3_Row_Thread_D_regsize		0
// page_3 RLg_Thread_A Components
#define page_3_RLg_Thread_A_thread		0
#define page_3_RLg_Thread_A_regaddr		PAGE_3_CODE_OFFSET + 0x0000049cL
#define page_3_RLg_Thread_A_regsize		16
// page_3 RLg_Thread_B Components
#define page_3_RLg_Thread_B_thread		1
#define page_3_RLg_Thread_B_regaddr		PAGE_3_CODE_OFFSET + 0x000004acL
#define page_3_RLg_Thread_B_regsize		16
// page_3 RLg_Thread_C Components
#define page_3_RLg_Thread_C_thread		2
#define page_3_RLg_Thread_C_regaddr		PAGE_3_CODE_OFFSET + 0x000004bcL
#define page_3_RLg_Thread_C_regsize		16
// page_3 RLg_Thread_D Components
#define page_3_RLg_Thread_D_thread		3
#define page_3_RLg_Thread_D_regaddr		PAGE_3_CODE_OFFSET + 0x000004ccL
#define page_3_RLg_Thread_D_regsize		16
// page_3 RLs_Thread_A Components
#define page_3_RLs_Thread_A_thread		0
#define page_3_RLs_Thread_A_regaddr		PAGE_3_CODE_OFFSET + 0x000004dcL
#define page_3_RLs_Thread_A_regsize		8
// page_3 RLs_Thread_B Components
#define page_3_RLs_Thread_B_thread		1
#define page_3_RLs_Thread_B_regaddr		PAGE_3_CODE_OFFSET + 0x000004e4L
#define page_3_RLs_Thread_B_regsize		8
// page_3 RLs_Thread_C Components
#define page_3_RLs_Thread_C_thread		2
#define page_3_RLs_Thread_C_regaddr		PAGE_3_CODE_OFFSET + 0x000004ecL
#define page_3_RLs_Thread_C_regsize		8
// page_3 RLs_Thread_D Components
#define page_3_RLs_Thread_D_thread		3
#define page_3_RLs_Thread_D_regaddr		PAGE_3_CODE_OFFSET + 0x000004f4L
#define page_3_RLs_Thread_D_regsize		8
// page_3 Win_Thread_A Components
#define page_3_Win_Thread_A_thread		0
#define page_3_Win_Thread_A_regaddr		PAGE_3_CODE_OFFSET + 0x000004fcL
#define page_3_Win_Thread_A_regsize		12
// page_3 Win_Thread_B Components
#define page_3_Win_Thread_B_thread		1
#define page_3_Win_Thread_B_regaddr		PAGE_3_CODE_OFFSET + 0x00000508L
#define page_3_Win_Thread_B_regsize		12
// page_3 Win_Thread_C Components
#define page_3_Win_Thread_C_thread		2
#define page_3_Win_Thread_C_regaddr		PAGE_3_CODE_OFFSET + 0x00000514L
#define page_3_Win_Thread_C_regsize		12
// page_3 Win_Thread_D Components
#define page_3_Win_Thread_D_thread		3
#define page_3_Win_Thread_D_regaddr		PAGE_3_CODE_OFFSET + 0x00000520L
#define page_3_Win_Thread_D_regsize		12
// page_3 Mat_Thread_A Components
#define page_3_Mat_Thread_A_thread		0
#define page_3_Mat_Thread_A_regaddr		PAGE_3_CODE_OFFSET + 0x00000000L
#define page_3_Mat_Thread_A_regsize		0
// page_3 Mat_Thread_B Components
#define page_3_Mat_Thread_B_thread		1
#define page_3_Mat_Thread_B_regaddr		PAGE_3_CODE_OFFSET + 0x00000000L
#define page_3_Mat_Thread_B_regsize		0
// page_3 Mat_Thread_C Components
#define page_3_Mat_Thread_C_thread		2
#define page_3_Mat_Thread_C_regaddr		PAGE_3_CODE_OFFSET + 0x00000000L
#define page_3_Mat_Thread_C_regsize		0
// page_3 Mat_Thread_D Components
#define page_3_Mat_Thread_D_thread		3
#define page_3_Mat_Thread_D_regaddr		PAGE_3_CODE_OFFSET + 0x00000000L
#define page_3_Mat_Thread_D_regsize		0

// page_3 RLg_Thread_B Components w/ Enable
#define page_3_com_0_rlg_B_ENA		( 1 << 0 )


// page_3 RLg_Thread_C Components w/ Enable
#define page_3_com_1_rlg_C_ENA		( 1 << 0 )


// page_3 RLg_Thread_D Components w/ Enable
#define page_3_com_2_rlg_D_ENA		( 1 << 0 )


#define PAGE_3_RES_SIZE	0x00003070L



// page_4 Resource Layout
#define PAGE_4_CODE_OFFSET	(PAGE_3_CODE_OFFSET + PAGE_3_RES_SIZE)
// page_4 com_0 - RLg D
#define page_4_com_0_rlgatt_ptr		0x0364
#define page_4_com_0_rlgatt_addr		PAGE_4_CODE_OFFSET + 0x00000364L
#define page_4_com_0_rlgatt_size		8
// page_4 com_0 - RLg Image
#define page_4_com_0_rlgoram_ptr		0x0000
#define page_4_com_0_rlgcode_addr		PAGE_4_CODE_OFFSET + 0x00000000L
#define page_4_com_0_rlgcode_size		868

// page_4 com_4 - RLs A
#define page_4_com_4_rlsatt_ptr		0x036c
#define page_4_com_4_rlsatt_addr		PAGE_4_CODE_OFFSET + 0x0000036cL
#define page_4_com_4_rlsatt_size		8
// page_4 com_4 - RLs Image
#define page_4_com_4_rlscode_addr		PAGE_4_CODE_OFFSET + 0x0000042cL
#define page_4_com_4_rlscode_size		2204

// page_4 _dummy0_ - RLs A
#define page_4__dummy0__rlsatt_ptr		0x0374
#define page_4__dummy0__rlsatt_addr		PAGE_4_CODE_OFFSET + 0x00000374L
#define page_4__dummy0__rlsatt_size		4

// page_4 com_3 - RLs B
#define page_4_com_3_rlsatt_ptr		0x0378
#define page_4_com_3_rlsatt_addr		PAGE_4_CODE_OFFSET + 0x00000378L
#define page_4_com_3_rlsatt_size		8
// page_4 com_3 - RLs Image
#define page_4_com_3_rlscode_addr		PAGE_4_CODE_OFFSET + 0x00000cc8L
#define page_4_com_3_rlscode_size		1796

// page_4 _dummy1_ - RLs B
#define page_4__dummy1__rlsatt_ptr		0x0380
#define page_4__dummy1__rlsatt_addr		PAGE_4_CODE_OFFSET + 0x00000380L
#define page_4__dummy1__rlsatt_size		4

// page_4 com_2 - RLs C
#define page_4_com_2_rlsatt_ptr		0x0384
#define page_4_com_2_rlsatt_addr		PAGE_4_CODE_OFFSET + 0x00000384L
#define page_4_com_2_rlsatt_size		8
// page_4 com_2 - RLs Image
#define page_4_com_2_rlscode_addr		PAGE_4_CODE_OFFSET + 0x000013ccL
#define page_4_com_2_rlscode_size		3280

// page_4 _dummy2_ - RLs C
#define page_4__dummy2__rlsatt_ptr		0x038c
#define page_4__dummy2__rlsatt_addr		PAGE_4_CODE_OFFSET + 0x0000038cL
#define page_4__dummy2__rlsatt_size		4

// page_4 com_1 - RLs D
#define page_4_com_1_rlsatt_ptr		0x0390
#define page_4_com_1_rlsatt_addr		PAGE_4_CODE_OFFSET + 0x00000390L
#define page_4_com_1_rlsatt_size		8
// page_4 com_1 - RLs Image
#define page_4_com_1_rlscode_addr		PAGE_4_CODE_OFFSET + 0x0000209cL
#define page_4_com_1_rlscode_size		2980

// page_4 _dummy3_ - RLs D
#define page_4__dummy3__rlsatt_ptr		0x0398
#define page_4__dummy3__rlsatt_addr		PAGE_4_CODE_OFFSET + 0x00000398L
#define page_4__dummy3__rlsatt_size		4

// page_4 Row_Thread_A Components
#define page_4_Row_Thread_A_thread		0
#define page_4_Row_Thread_A_regaddr		PAGE_4_CODE_OFFSET + 0x00000000L
#define page_4_Row_Thread_A_regsize		0
// page_4 Row_Thread_B Components
#define page_4_Row_Thread_B_thread		1
#define page_4_Row_Thread_B_regaddr		PAGE_4_CODE_OFFSET + 0x00000000L
#define page_4_Row_Thread_B_regsize		0
// page_4 Row_Thread_C Components
#define page_4_Row_Thread_C_thread		2
#define page_4_Row_Thread_C_regaddr		PAGE_4_CODE_OFFSET + 0x00000000L
#define page_4_Row_Thread_C_regsize		0
// page_4 Row_Thread_D Components
#define page_4_Row_Thread_D_thread		3
#define page_4_Row_Thread_D_regaddr		PAGE_4_CODE_OFFSET + 0x00000000L
#define page_4_Row_Thread_D_regsize		0
// page_4 RLg_Thread_A Components
#define page_4_RLg_Thread_A_thread		0
#define page_4_RLg_Thread_A_regaddr		PAGE_4_CODE_OFFSET + 0x0000039cL
#define page_4_RLg_Thread_A_regsize		16
// page_4 RLg_Thread_B Components
#define page_4_RLg_Thread_B_thread		1
#define page_4_RLg_Thread_B_regaddr		PAGE_4_CODE_OFFSET + 0x000003acL
#define page_4_RLg_Thread_B_regsize		16
// page_4 RLg_Thread_C Components
#define page_4_RLg_Thread_C_thread		2
#define page_4_RLg_Thread_C_regaddr		PAGE_4_CODE_OFFSET + 0x000003bcL
#define page_4_RLg_Thread_C_regsize		16
// page_4 RLg_Thread_D Components
#define page_4_RLg_Thread_D_thread		3
#define page_4_RLg_Thread_D_regaddr		PAGE_4_CODE_OFFSET + 0x000003ccL
#define page_4_RLg_Thread_D_regsize		16
// page_4 RLs_Thread_A Components
#define page_4_RLs_Thread_A_thread		0
#define page_4_RLs_Thread_A_regaddr		PAGE_4_CODE_OFFSET + 0x000003dcL
#define page_4_RLs_Thread_A_regsize		8
// page_4 RLs_Thread_B Components
#define page_4_RLs_Thread_B_thread		1
#define page_4_RLs_Thread_B_regaddr		PAGE_4_CODE_OFFSET + 0x000003e4L
#define page_4_RLs_Thread_B_regsize		8
// page_4 RLs_Thread_C Components
#define page_4_RLs_Thread_C_thread		2
#define page_4_RLs_Thread_C_regaddr		PAGE_4_CODE_OFFSET + 0x000003ecL
#define page_4_RLs_Thread_C_regsize		8
// page_4 RLs_Thread_D Components
#define page_4_RLs_Thread_D_thread		3
#define page_4_RLs_Thread_D_regaddr		PAGE_4_CODE_OFFSET + 0x000003f4L
#define page_4_RLs_Thread_D_regsize		8
// page_4 Win_Thread_A Components
#define page_4_Win_Thread_A_thread		0
#define page_4_Win_Thread_A_regaddr		PAGE_4_CODE_OFFSET + 0x000003fcL
#define page_4_Win_Thread_A_regsize		12
// page_4 Win_Thread_B Components
#define page_4_Win_Thread_B_thread		1
#define page_4_Win_Thread_B_regaddr		PAGE_4_CODE_OFFSET + 0x00000408L
#define page_4_Win_Thread_B_regsize		12
// page_4 Win_Thread_C Components
#define page_4_Win_Thread_C_thread		2
#define page_4_Win_Thread_C_regaddr		PAGE_4_CODE_OFFSET + 0x00000414L
#define page_4_Win_Thread_C_regsize		12
// page_4 Win_Thread_D Components
#define page_4_Win_Thread_D_thread		3
#define page_4_Win_Thread_D_regaddr		PAGE_4_CODE_OFFSET + 0x00000420L
#define page_4_Win_Thread_D_regsize		12
// page_4 Mat_Thread_A Components
#define page_4_Mat_Thread_A_thread		0
#define page_4_Mat_Thread_A_regaddr		PAGE_4_CODE_OFFSET + 0x00000000L
#define page_4_Mat_Thread_A_regsize		0
// page_4 Mat_Thread_B Components
#define page_4_Mat_Thread_B_thread		1
#define page_4_Mat_Thread_B_regaddr		PAGE_4_CODE_OFFSET + 0x00000000L
#define page_4_Mat_Thread_B_regsize		0
// page_4 Mat_Thread_C Components
#define page_4_Mat_Thread_C_thread		2
#define page_4_Mat_Thread_C_regaddr		PAGE_4_CODE_OFFSET + 0x00000000L
#define page_4_Mat_Thread_C_regsize		0
// page_4 Mat_Thread_D Components
#define page_4_Mat_Thread_D_thread		3
#define page_4_Mat_Thread_D_regaddr		PAGE_4_CODE_OFFSET + 0x00000000L
#define page_4_Mat_Thread_D_regsize		0

// page_4 RLg_Thread_D Components w/ Enable
#define page_4_com_0_rlg_D_ENA		( 1 << 0 )


#define PAGE_4_RES_SIZE	0x00002c40L


#if (EF1E_TEST_MODE == 4)
// page_5 Resource Layout
#define PAGE_5_CODE_OFFSET	(PAGE_4_CODE_OFFSET + PAGE_4_RES_SIZE)
// page_5 com_0 - RLg A
#define page_5_com_0_rlgatt_ptr		0x5974
#define page_5_com_0_rlgatt_addr		PAGE_5_CODE_OFFSET + 0x00005974L
#define page_5_com_0_rlgatt_size		8
// page_5 com_0 - RLg Image
#define page_5_com_0_rlgoram_ptr		0x0000
#define page_5_com_0_rlgcode_addr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_com_0_rlgcode_size		22900

// page_5 Row_Thread_A Components
#define page_5_Row_Thread_A_thread		0
#define page_5_Row_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Row_Thread_A_regsize		0
// page_5 Row_Thread_B Components
#define page_5_Row_Thread_B_thread		1
#define page_5_Row_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Row_Thread_B_regsize		0
// page_5 Row_Thread_C Components
#define page_5_Row_Thread_C_thread		2
#define page_5_Row_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Row_Thread_C_regsize		0
// page_5 Row_Thread_D Components
#define page_5_Row_Thread_D_thread		3
#define page_5_Row_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Row_Thread_D_regsize		0
// page_5 RLg_Thread_A Components
#define page_5_RLg_Thread_A_thread		0
#define page_5_RLg_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x0000597cL
#define page_5_RLg_Thread_A_regsize		16
// page_5 RLg_Thread_B Components
#define page_5_RLg_Thread_B_thread		1
#define page_5_RLg_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x0000598cL
#define page_5_RLg_Thread_B_regsize		16
// page_5 RLg_Thread_C Components
#define page_5_RLg_Thread_C_thread		2
#define page_5_RLg_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x0000599cL
#define page_5_RLg_Thread_C_regsize		16
// page_5 RLg_Thread_D Components
#define page_5_RLg_Thread_D_thread		3
#define page_5_RLg_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x000059acL
#define page_5_RLg_Thread_D_regsize		16
// page_5 RLs_Thread_A Components
#define page_5_RLs_Thread_A_thread		0
#define page_5_RLs_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x000059bcL
#define page_5_RLs_Thread_A_regsize		8
// page_5 RLs_Thread_B Components
#define page_5_RLs_Thread_B_thread		1
#define page_5_RLs_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x000059c4L
#define page_5_RLs_Thread_B_regsize		8
// page_5 RLs_Thread_C Components
#define page_5_RLs_Thread_C_thread		2
#define page_5_RLs_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x000059ccL
#define page_5_RLs_Thread_C_regsize		8
// page_5 RLs_Thread_D Components
#define page_5_RLs_Thread_D_thread		3
#define page_5_RLs_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x000059d4L
#define page_5_RLs_Thread_D_regsize		8
// page_5 Win_Thread_A Components
#define page_5_Win_Thread_A_thread		0
#define page_5_Win_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x000059dcL
#define page_5_Win_Thread_A_regsize		12
// page_5 Win_Thread_B Components
#define page_5_Win_Thread_B_thread		1
#define page_5_Win_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x000059e8L
#define page_5_Win_Thread_B_regsize		12
// page_5 Win_Thread_C Components
#define page_5_Win_Thread_C_thread		2
#define page_5_Win_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x000059f4L
#define page_5_Win_Thread_C_regsize		12
// page_5 Win_Thread_D Components
#define page_5_Win_Thread_D_thread		3
#define page_5_Win_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x00005a00L
#define page_5_Win_Thread_D_regsize		12
// page_5 Mat_Thread_A Components
#define page_5_Mat_Thread_A_thread		0
#define page_5_Mat_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Mat_Thread_A_regsize		0
// page_5 Mat_Thread_B Components
#define page_5_Mat_Thread_B_thread		1
#define page_5_Mat_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Mat_Thread_B_regsize		0
// page_5 Mat_Thread_C Components
#define page_5_Mat_Thread_C_thread		2
#define page_5_Mat_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Mat_Thread_C_regsize		0
// page_5 Mat_Thread_D Components
#define page_5_Mat_Thread_D_thread		3
#define page_5_Mat_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Mat_Thread_D_regsize		0

// page_5 RLg_Thread_A Components w/ Enable
#define page_5_com_0_rlg_A_ENA		( 1 << 0 )


#define PAGE_5_RES_SIZE	0x00005a0cL



// page_6 Resource Layout
#define PAGE_6_CODE_OFFSET	(PAGE_5_CODE_OFFSET + PAGE_5_RES_SIZE)
// page_6 com_0 - RLg A
#define page_6_com_0_rlgatt_ptr		0x62a4
#define page_6_com_0_rlgatt_addr		PAGE_6_CODE_OFFSET + 0x000062a4L
#define page_6_com_0_rlgatt_size		8
// page_6 com_0 - RLg Image
#define page_6_com_0_rlgoram_ptr		0x0000
#define page_6_com_0_rlgcode_addr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_com_0_rlgcode_size		25252

// page_6 Row_Thread_A Components
#define page_6_Row_Thread_A_thread		0
#define page_6_Row_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Row_Thread_A_regsize		0
// page_6 Row_Thread_B Components
#define page_6_Row_Thread_B_thread		1
#define page_6_Row_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Row_Thread_B_regsize		0
// page_6 Row_Thread_C Components
#define page_6_Row_Thread_C_thread		2
#define page_6_Row_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Row_Thread_C_regsize		0
// page_6 Row_Thread_D Components
#define page_6_Row_Thread_D_thread		3
#define page_6_Row_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Row_Thread_D_regsize		0
// page_6 RLg_Thread_A Components
#define page_6_RLg_Thread_A_thread		0
#define page_6_RLg_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x000062acL
#define page_6_RLg_Thread_A_regsize		16
// page_6 RLg_Thread_B Components
#define page_6_RLg_Thread_B_thread		1
#define page_6_RLg_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x000062bcL
#define page_6_RLg_Thread_B_regsize		16
// page_6 RLg_Thread_C Components
#define page_6_RLg_Thread_C_thread		2
#define page_6_RLg_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x000062ccL
#define page_6_RLg_Thread_C_regsize		16
// page_6 RLg_Thread_D Components
#define page_6_RLg_Thread_D_thread		3
#define page_6_RLg_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x000062dcL
#define page_6_RLg_Thread_D_regsize		16
// page_6 RLs_Thread_A Components
#define page_6_RLs_Thread_A_thread		0
#define page_6_RLs_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x000062ecL
#define page_6_RLs_Thread_A_regsize		8
// page_6 RLs_Thread_B Components
#define page_6_RLs_Thread_B_thread		1
#define page_6_RLs_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x000062f4L
#define page_6_RLs_Thread_B_regsize		8
// page_6 RLs_Thread_C Components
#define page_6_RLs_Thread_C_thread		2
#define page_6_RLs_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x000062fcL
#define page_6_RLs_Thread_C_regsize		8
// page_6 RLs_Thread_D Components
#define page_6_RLs_Thread_D_thread		3
#define page_6_RLs_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x00006304L
#define page_6_RLs_Thread_D_regsize		8
// page_6 Win_Thread_A Components
#define page_6_Win_Thread_A_thread		0
#define page_6_Win_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x0000630cL
#define page_6_Win_Thread_A_regsize		12
// page_6 Win_Thread_B Components
#define page_6_Win_Thread_B_thread		1
#define page_6_Win_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x00006318L
#define page_6_Win_Thread_B_regsize		12
// page_6 Win_Thread_C Components
#define page_6_Win_Thread_C_thread		2
#define page_6_Win_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x00006324L
#define page_6_Win_Thread_C_regsize		12
// page_6 Win_Thread_D Components
#define page_6_Win_Thread_D_thread		3
#define page_6_Win_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x00006330L
#define page_6_Win_Thread_D_regsize		12
// page_6 Mat_Thread_A Components
#define page_6_Mat_Thread_A_thread		0
#define page_6_Mat_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Mat_Thread_A_regsize		0
// page_6 Mat_Thread_B Components
#define page_6_Mat_Thread_B_thread		1
#define page_6_Mat_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Mat_Thread_B_regsize		0
// page_6 Mat_Thread_C Components
#define page_6_Mat_Thread_C_thread		2
#define page_6_Mat_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Mat_Thread_C_regsize		0
// page_6 Mat_Thread_D Components
#define page_6_Mat_Thread_D_thread		3
#define page_6_Mat_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Mat_Thread_D_regsize		0

// page_6 RLg_Thread_A Components w/ Enable
#define page_6_com_0_rlg_A_ENA		( 1 << 0 )


#define PAGE_6_RES_SIZE	0x0000633cL



// page_7 Resource Layout
#define PAGE_7_CODE_OFFSET	(PAGE_6_CODE_OFFSET + PAGE_6_RES_SIZE)
// page_7 com_3 - RLs A
#define page_7_com_3_rlsatt_ptr		0x0000
#define page_7_com_3_rlsatt_addr		PAGE_7_CODE_OFFSET + 0x00000000L
#define page_7_com_3_rlsatt_size		8
// page_7 com_3 - RLs Image
#define page_7_com_3_rlscode_addr		PAGE_7_CODE_OFFSET + 0x000000c0L
#define page_7_com_3_rlscode_size		856

// page_7 _dummy0_ - RLs A
#define page_7__dummy0__rlsatt_ptr		0x0008
#define page_7__dummy0__rlsatt_addr		PAGE_7_CODE_OFFSET + 0x00000008L
#define page_7__dummy0__rlsatt_size		4

// page_7 com_2 - RLs B
#define page_7_com_2_rlsatt_ptr		0x000c
#define page_7_com_2_rlsatt_addr		PAGE_7_CODE_OFFSET + 0x0000000cL
#define page_7_com_2_rlsatt_size		8
// page_7 com_2 - RLs Image
#define page_7_com_2_rlscode_addr		PAGE_7_CODE_OFFSET + 0x00000418L
#define page_7_com_2_rlscode_size		6128

// page_7 _dummy1_ - RLs B
#define page_7__dummy1__rlsatt_ptr		0x0014
#define page_7__dummy1__rlsatt_addr		PAGE_7_CODE_OFFSET + 0x00000014L
#define page_7__dummy1__rlsatt_size		4

// page_7 com_1 - RLs C
#define page_7_com_1_rlsatt_ptr		0x0018
#define page_7_com_1_rlsatt_addr		PAGE_7_CODE_OFFSET + 0x00000018L
#define page_7_com_1_rlsatt_size		8
// page_7 com_1 - RLs Image
#define page_7_com_1_rlscode_addr		PAGE_7_CODE_OFFSET + 0x00001c08L
#define page_7_com_1_rlscode_size		6084

// page_7 _dummy2_ - RLs C
#define page_7__dummy2__rlsatt_ptr		0x0020
#define page_7__dummy2__rlsatt_addr		PAGE_7_CODE_OFFSET + 0x00000020L
#define page_7__dummy2__rlsatt_size		4

// page_7 com_0 - RLs D
#define page_7_com_0_rlsatt_ptr		0x0024
#define page_7_com_0_rlsatt_addr		PAGE_7_CODE_OFFSET + 0x00000024L
#define page_7_com_0_rlsatt_size		8
// page_7 com_0 - RLs Image
#define page_7_com_0_rlscode_addr		PAGE_7_CODE_OFFSET + 0x000033ccL
#define page_7_com_0_rlscode_size		5224

// page_7 _dummy3_ - RLs D
#define page_7__dummy3__rlsatt_ptr		0x002c
#define page_7__dummy3__rlsatt_addr		PAGE_7_CODE_OFFSET + 0x0000002cL
#define page_7__dummy3__rlsatt_size		4

// page_7 Row_Thread_A Components
#define page_7_Row_Thread_A_thread		0
#define page_7_Row_Thread_A_regaddr		PAGE_7_CODE_OFFSET + 0x00000000L
#define page_7_Row_Thread_A_regsize		0
// page_7 Row_Thread_B Components
#define page_7_Row_Thread_B_thread		1
#define page_7_Row_Thread_B_regaddr		PAGE_7_CODE_OFFSET + 0x00000000L
#define page_7_Row_Thread_B_regsize		0
// page_7 Row_Thread_C Components
#define page_7_Row_Thread_C_thread		2
#define page_7_Row_Thread_C_regaddr		PAGE_7_CODE_OFFSET + 0x00000000L
#define page_7_Row_Thread_C_regsize		0
// page_7 Row_Thread_D Components
#define page_7_Row_Thread_D_thread		3
#define page_7_Row_Thread_D_regaddr		PAGE_7_CODE_OFFSET + 0x00000000L
#define page_7_Row_Thread_D_regsize		0
// page_7 RLg_Thread_A Components
#define page_7_RLg_Thread_A_thread		0
#define page_7_RLg_Thread_A_regaddr		PAGE_7_CODE_OFFSET + 0x00000030L
#define page_7_RLg_Thread_A_regsize		16
// page_7 RLg_Thread_B Components
#define page_7_RLg_Thread_B_thread		1
#define page_7_RLg_Thread_B_regaddr		PAGE_7_CODE_OFFSET + 0x00000040L
#define page_7_RLg_Thread_B_regsize		16
// page_7 RLg_Thread_C Components
#define page_7_RLg_Thread_C_thread		2
#define page_7_RLg_Thread_C_regaddr		PAGE_7_CODE_OFFSET + 0x00000050L
#define page_7_RLg_Thread_C_regsize		16
// page_7 RLg_Thread_D Components
#define page_7_RLg_Thread_D_thread		3
#define page_7_RLg_Thread_D_regaddr		PAGE_7_CODE_OFFSET + 0x00000060L
#define page_7_RLg_Thread_D_regsize		16
// page_7 RLs_Thread_A Components
#define page_7_RLs_Thread_A_thread		0
#define page_7_RLs_Thread_A_regaddr		PAGE_7_CODE_OFFSET + 0x00000070L
#define page_7_RLs_Thread_A_regsize		8
// page_7 RLs_Thread_B Components
#define page_7_RLs_Thread_B_thread		1
#define page_7_RLs_Thread_B_regaddr		PAGE_7_CODE_OFFSET + 0x00000078L
#define page_7_RLs_Thread_B_regsize		8
// page_7 RLs_Thread_C Components
#define page_7_RLs_Thread_C_thread		2
#define page_7_RLs_Thread_C_regaddr		PAGE_7_CODE_OFFSET + 0x00000080L
#define page_7_RLs_Thread_C_regsize		8
// page_7 RLs_Thread_D Components
#define page_7_RLs_Thread_D_thread		3
#define page_7_RLs_Thread_D_regaddr		PAGE_7_CODE_OFFSET + 0x00000088L
#define page_7_RLs_Thread_D_regsize		8
// page_7 Win_Thread_A Components
#define page_7_Win_Thread_A_thread		0
#define page_7_Win_Thread_A_regaddr		PAGE_7_CODE_OFFSET + 0x00000090L
#define page_7_Win_Thread_A_regsize		12
// page_7 Win_Thread_B Components
#define page_7_Win_Thread_B_thread		1
#define page_7_Win_Thread_B_regaddr		PAGE_7_CODE_OFFSET + 0x0000009cL
#define page_7_Win_Thread_B_regsize		12
// page_7 Win_Thread_C Components
#define page_7_Win_Thread_C_thread		2
#define page_7_Win_Thread_C_regaddr		PAGE_7_CODE_OFFSET + 0x000000a8L
#define page_7_Win_Thread_C_regsize		12
// page_7 Win_Thread_D Components
#define page_7_Win_Thread_D_thread		3
#define page_7_Win_Thread_D_regaddr		PAGE_7_CODE_OFFSET + 0x000000b4L
#define page_7_Win_Thread_D_regsize		12
// page_7 Mat_Thread_A Components
#define page_7_Mat_Thread_A_thread		0
#define page_7_Mat_Thread_A_regaddr		PAGE_7_CODE_OFFSET + 0x00000000L
#define page_7_Mat_Thread_A_regsize		0
// page_7 Mat_Thread_B Components
#define page_7_Mat_Thread_B_thread		1
#define page_7_Mat_Thread_B_regaddr		PAGE_7_CODE_OFFSET + 0x00000000L
#define page_7_Mat_Thread_B_regsize		0
// page_7 Mat_Thread_C Components
#define page_7_Mat_Thread_C_thread		2
#define page_7_Mat_Thread_C_regaddr		PAGE_7_CODE_OFFSET + 0x00000000L
#define page_7_Mat_Thread_C_regsize		0
// page_7 Mat_Thread_D Components
#define page_7_Mat_Thread_D_thread		3
#define page_7_Mat_Thread_D_regaddr		PAGE_7_CODE_OFFSET + 0x00000000L
#define page_7_Mat_Thread_D_regsize		0

#define PAGE_7_RES_SIZE	0x00004834L



// page_8 Resource Layout
#define PAGE_8_CODE_OFFSET	(PAGE_7_CODE_OFFSET + PAGE_7_RES_SIZE)
// page_8 com_1 - RLs C
#define page_8_com_1_rlsatt_ptr		0x0000
#define page_8_com_1_rlsatt_addr		PAGE_8_CODE_OFFSET + 0x00000000L
#define page_8_com_1_rlsatt_size		8
// page_8 com_1 - RLs Image
#define page_8_com_1_rlscode_addr		PAGE_8_CODE_OFFSET + 0x000000a8L
#define page_8_com_1_rlscode_size		5332

// page_8 _dummy2_ - RLs C
#define page_8__dummy2__rlsatt_ptr		0x0008
#define page_8__dummy2__rlsatt_addr		PAGE_8_CODE_OFFSET + 0x00000008L
#define page_8__dummy2__rlsatt_size		4

// page_8 com_0 - RLs D
#define page_8_com_0_rlsatt_ptr		0x000c
#define page_8_com_0_rlsatt_addr		PAGE_8_CODE_OFFSET + 0x0000000cL
#define page_8_com_0_rlsatt_size		8
// page_8 com_0 - RLs Image
#define page_8_com_0_rlscode_addr		PAGE_8_CODE_OFFSET + 0x0000157cL
#define page_8_com_0_rlscode_size		1940

// page_8 _dummy3_ - RLs D
#define page_8__dummy3__rlsatt_ptr		0x0014
#define page_8__dummy3__rlsatt_addr		PAGE_8_CODE_OFFSET + 0x00000014L
#define page_8__dummy3__rlsatt_size		4

// page_8 Row_Thread_A Components
#define page_8_Row_Thread_A_thread		0
#define page_8_Row_Thread_A_regaddr		PAGE_8_CODE_OFFSET + 0x00000000L
#define page_8_Row_Thread_A_regsize		0
// page_8 Row_Thread_B Components
#define page_8_Row_Thread_B_thread		1
#define page_8_Row_Thread_B_regaddr		PAGE_8_CODE_OFFSET + 0x00000000L
#define page_8_Row_Thread_B_regsize		0
// page_8 Row_Thread_C Components
#define page_8_Row_Thread_C_thread		2
#define page_8_Row_Thread_C_regaddr		PAGE_8_CODE_OFFSET + 0x00000000L
#define page_8_Row_Thread_C_regsize		0
// page_8 Row_Thread_D Components
#define page_8_Row_Thread_D_thread		3
#define page_8_Row_Thread_D_regaddr		PAGE_8_CODE_OFFSET + 0x00000000L
#define page_8_Row_Thread_D_regsize		0
// page_8 RLg_Thread_A Components
#define page_8_RLg_Thread_A_thread		0
#define page_8_RLg_Thread_A_regaddr		PAGE_8_CODE_OFFSET + 0x00000018L
#define page_8_RLg_Thread_A_regsize		16
// page_8 RLg_Thread_B Components
#define page_8_RLg_Thread_B_thread		1
#define page_8_RLg_Thread_B_regaddr		PAGE_8_CODE_OFFSET + 0x00000028L
#define page_8_RLg_Thread_B_regsize		16
// page_8 RLg_Thread_C Components
#define page_8_RLg_Thread_C_thread		2
#define page_8_RLg_Thread_C_regaddr		PAGE_8_CODE_OFFSET + 0x00000038L
#define page_8_RLg_Thread_C_regsize		16
// page_8 RLg_Thread_D Components
#define page_8_RLg_Thread_D_thread		3
#define page_8_RLg_Thread_D_regaddr		PAGE_8_CODE_OFFSET + 0x00000048L
#define page_8_RLg_Thread_D_regsize		16
// page_8 RLs_Thread_A Components
#define page_8_RLs_Thread_A_thread		0
#define page_8_RLs_Thread_A_regaddr		PAGE_8_CODE_OFFSET + 0x00000058L
#define page_8_RLs_Thread_A_regsize		8
// page_8 RLs_Thread_B Components
#define page_8_RLs_Thread_B_thread		1
#define page_8_RLs_Thread_B_regaddr		PAGE_8_CODE_OFFSET + 0x00000060L
#define page_8_RLs_Thread_B_regsize		8
// page_8 RLs_Thread_C Components
#define page_8_RLs_Thread_C_thread		2
#define page_8_RLs_Thread_C_regaddr		PAGE_8_CODE_OFFSET + 0x00000068L
#define page_8_RLs_Thread_C_regsize		8
// page_8 RLs_Thread_D Components
#define page_8_RLs_Thread_D_thread		3
#define page_8_RLs_Thread_D_regaddr		PAGE_8_CODE_OFFSET + 0x00000070L
#define page_8_RLs_Thread_D_regsize		8
// page_8 Win_Thread_A Components
#define page_8_Win_Thread_A_thread		0
#define page_8_Win_Thread_A_regaddr		PAGE_8_CODE_OFFSET + 0x00000078L
#define page_8_Win_Thread_A_regsize		12
// page_8 Win_Thread_B Components
#define page_8_Win_Thread_B_thread		1
#define page_8_Win_Thread_B_regaddr		PAGE_8_CODE_OFFSET + 0x00000084L
#define page_8_Win_Thread_B_regsize		12
// page_8 Win_Thread_C Components
#define page_8_Win_Thread_C_thread		2
#define page_8_Win_Thread_C_regaddr		PAGE_8_CODE_OFFSET + 0x00000090L
#define page_8_Win_Thread_C_regsize		12
// page_8 Win_Thread_D Components
#define page_8_Win_Thread_D_thread		3
#define page_8_Win_Thread_D_regaddr		PAGE_8_CODE_OFFSET + 0x0000009cL
#define page_8_Win_Thread_D_regsize		12
// page_8 Mat_Thread_A Components
#define page_8_Mat_Thread_A_thread		0
#define page_8_Mat_Thread_A_regaddr		PAGE_8_CODE_OFFSET + 0x00000000L
#define page_8_Mat_Thread_A_regsize		0
// page_8 Mat_Thread_B Components
#define page_8_Mat_Thread_B_thread		1
#define page_8_Mat_Thread_B_regaddr		PAGE_8_CODE_OFFSET + 0x00000000L
#define page_8_Mat_Thread_B_regsize		0
// page_8 Mat_Thread_C Components
#define page_8_Mat_Thread_C_thread		2
#define page_8_Mat_Thread_C_regaddr		PAGE_8_CODE_OFFSET + 0x00000000L
#define page_8_Mat_Thread_C_regsize		0
// page_8 Mat_Thread_D Components
#define page_8_Mat_Thread_D_thread		3
#define page_8_Mat_Thread_D_regaddr		PAGE_8_CODE_OFFSET + 0x00000000L
#define page_8_Mat_Thread_D_regsize		0

#define PAGE_8_RES_SIZE	0x00001d10L



extern void show_page_0_page(void);
extern void show_page_1_page(void);
extern void show_page_2_page(void);
extern void show_page_3_page(void);
extern void show_page_4_page(void);
extern void show_page_5_page(void);
extern void show_page_6_page(void);
extern void show_page_7_page(void);
extern void show_page_8_page(void);

#else
// page_5 Resource Layout
#define PAGE_5_CODE_OFFSET	(PAGE_4_CODE_OFFSET + PAGE_4_RES_SIZE)
// page_5 com_0 - RLs D
#define page_5_com_0_rlsatt_ptr		0x0000
#define page_5_com_0_rlsatt_addr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_com_0_rlsatt_size		8
// page_5 com_0 - RLs Image
#define page_5_com_0_rlscode_addr		PAGE_5_CODE_OFFSET + 0x0000009cL
#define page_5_com_0_rlscode_size		345128

// page_5 _dummy3_ - RLs D
#define page_5__dummy3__rlsatt_ptr		0x0008
#define page_5__dummy3__rlsatt_addr		PAGE_5_CODE_OFFSET + 0x00000008L
#define page_5__dummy3__rlsatt_size		4

// page_5 Row_Thread_A Components
#define page_5_Row_Thread_A_thread		0
#define page_5_Row_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Row_Thread_A_regsize		0
// page_5 Row_Thread_B Components
#define page_5_Row_Thread_B_thread		1
#define page_5_Row_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Row_Thread_B_regsize		0
// page_5 Row_Thread_C Components
#define page_5_Row_Thread_C_thread		2
#define page_5_Row_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Row_Thread_C_regsize		0
// page_5 Row_Thread_D Components
#define page_5_Row_Thread_D_thread		3
#define page_5_Row_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Row_Thread_D_regsize		0
// page_5 RLg_Thread_A Components
#define page_5_RLg_Thread_A_thread		0
#define page_5_RLg_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x0000000cL
#define page_5_RLg_Thread_A_regsize		16
// page_5 RLg_Thread_B Components
#define page_5_RLg_Thread_B_thread		1
#define page_5_RLg_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x0000001cL
#define page_5_RLg_Thread_B_regsize		16
// page_5 RLg_Thread_C Components
#define page_5_RLg_Thread_C_thread		2
#define page_5_RLg_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x0000002cL
#define page_5_RLg_Thread_C_regsize		16
// page_5 RLg_Thread_D Components
#define page_5_RLg_Thread_D_thread		3
#define page_5_RLg_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x0000003cL
#define page_5_RLg_Thread_D_regsize		16
// page_5 RLs_Thread_A Components
#define page_5_RLs_Thread_A_thread		0
#define page_5_RLs_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x0000004cL
#define page_5_RLs_Thread_A_regsize		8
// page_5 RLs_Thread_B Components
#define page_5_RLs_Thread_B_thread		1
#define page_5_RLs_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x00000054L
#define page_5_RLs_Thread_B_regsize		8
// page_5 RLs_Thread_C Components
#define page_5_RLs_Thread_C_thread		2
#define page_5_RLs_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x0000005cL
#define page_5_RLs_Thread_C_regsize		8
// page_5 RLs_Thread_D Components
#define page_5_RLs_Thread_D_thread		3
#define page_5_RLs_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x00000064L
#define page_5_RLs_Thread_D_regsize		8
// page_5 Win_Thread_A Components
#define page_5_Win_Thread_A_thread		0
#define page_5_Win_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x0000006cL
#define page_5_Win_Thread_A_regsize		12
// page_5 Win_Thread_B Components
#define page_5_Win_Thread_B_thread		1
#define page_5_Win_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x00000078L
#define page_5_Win_Thread_B_regsize		12
// page_5 Win_Thread_C Components
#define page_5_Win_Thread_C_thread		2
#define page_5_Win_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x00000084L
#define page_5_Win_Thread_C_regsize		12
// page_5 Win_Thread_D Components
#define page_5_Win_Thread_D_thread		3
#define page_5_Win_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x00000090L
#define page_5_Win_Thread_D_regsize		12
// page_5 Mat_Thread_A Components
#define page_5_Mat_Thread_A_thread		0
#define page_5_Mat_Thread_A_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Mat_Thread_A_regsize		0
// page_5 Mat_Thread_B Components
#define page_5_Mat_Thread_B_thread		1
#define page_5_Mat_Thread_B_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Mat_Thread_B_regsize		0
// page_5 Mat_Thread_C Components
#define page_5_Mat_Thread_C_thread		2
#define page_5_Mat_Thread_C_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Mat_Thread_C_regsize		0
// page_5 Mat_Thread_D Components
#define page_5_Mat_Thread_D_thread		3
#define page_5_Mat_Thread_D_regaddr		PAGE_5_CODE_OFFSET + 0x00000000L
#define page_5_Mat_Thread_D_regsize		0

#define PAGE_5_RES_SIZE	0x000544c4L



// page_6 Resource Layout
#define PAGE_6_CODE_OFFSET	(PAGE_5_CODE_OFFSET + PAGE_5_RES_SIZE)
// page_6 com_0 - RLs D
#define page_6_com_0_rlsatt_ptr		0x0000
#define page_6_com_0_rlsatt_addr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_com_0_rlsatt_size		8
// page_6 com_0 - RLs Image
#define page_6_com_0_rlscode_addr		PAGE_6_CODE_OFFSET + 0x0000009cL
#define page_6_com_0_rlscode_size		330928

// page_6 _dummy3_ - RLs D
#define page_6__dummy3__rlsatt_ptr		0x0008
#define page_6__dummy3__rlsatt_addr		PAGE_6_CODE_OFFSET + 0x00000008L
#define page_6__dummy3__rlsatt_size		4

// page_6 Row_Thread_A Components
#define page_6_Row_Thread_A_thread		0
#define page_6_Row_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Row_Thread_A_regsize		0
// page_6 Row_Thread_B Components
#define page_6_Row_Thread_B_thread		1
#define page_6_Row_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Row_Thread_B_regsize		0
// page_6 Row_Thread_C Components
#define page_6_Row_Thread_C_thread		2
#define page_6_Row_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Row_Thread_C_regsize		0
// page_6 Row_Thread_D Components
#define page_6_Row_Thread_D_thread		3
#define page_6_Row_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Row_Thread_D_regsize		0
// page_6 RLg_Thread_A Components
#define page_6_RLg_Thread_A_thread		0
#define page_6_RLg_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x0000000cL
#define page_6_RLg_Thread_A_regsize		16
// page_6 RLg_Thread_B Components
#define page_6_RLg_Thread_B_thread		1
#define page_6_RLg_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x0000001cL
#define page_6_RLg_Thread_B_regsize		16
// page_6 RLg_Thread_C Components
#define page_6_RLg_Thread_C_thread		2
#define page_6_RLg_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x0000002cL
#define page_6_RLg_Thread_C_regsize		16
// page_6 RLg_Thread_D Components
#define page_6_RLg_Thread_D_thread		3
#define page_6_RLg_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x0000003cL
#define page_6_RLg_Thread_D_regsize		16
// page_6 RLs_Thread_A Components
#define page_6_RLs_Thread_A_thread		0
#define page_6_RLs_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x0000004cL
#define page_6_RLs_Thread_A_regsize		8
// page_6 RLs_Thread_B Components
#define page_6_RLs_Thread_B_thread		1
#define page_6_RLs_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x00000054L
#define page_6_RLs_Thread_B_regsize		8
// page_6 RLs_Thread_C Components
#define page_6_RLs_Thread_C_thread		2
#define page_6_RLs_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x0000005cL
#define page_6_RLs_Thread_C_regsize		8
// page_6 RLs_Thread_D Components
#define page_6_RLs_Thread_D_thread		3
#define page_6_RLs_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x00000064L
#define page_6_RLs_Thread_D_regsize		8
// page_6 Win_Thread_A Components
#define page_6_Win_Thread_A_thread		0
#define page_6_Win_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x0000006cL
#define page_6_Win_Thread_A_regsize		12
// page_6 Win_Thread_B Components
#define page_6_Win_Thread_B_thread		1
#define page_6_Win_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x00000078L
#define page_6_Win_Thread_B_regsize		12
// page_6 Win_Thread_C Components
#define page_6_Win_Thread_C_thread		2
#define page_6_Win_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x00000084L
#define page_6_Win_Thread_C_regsize		12
// page_6 Win_Thread_D Components
#define page_6_Win_Thread_D_thread		3
#define page_6_Win_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x00000090L
#define page_6_Win_Thread_D_regsize		12
// page_6 Mat_Thread_A Components
#define page_6_Mat_Thread_A_thread		0
#define page_6_Mat_Thread_A_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Mat_Thread_A_regsize		0
// page_6 Mat_Thread_B Components
#define page_6_Mat_Thread_B_thread		1
#define page_6_Mat_Thread_B_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Mat_Thread_B_regsize		0
// page_6 Mat_Thread_C Components
#define page_6_Mat_Thread_C_thread		2
#define page_6_Mat_Thread_C_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Mat_Thread_C_regsize		0
// page_6 Mat_Thread_D Components
#define page_6_Mat_Thread_D_thread		3
#define page_6_Mat_Thread_D_regaddr		PAGE_6_CODE_OFFSET + 0x00000000L
#define page_6_Mat_Thread_D_regsize		0

#define PAGE_6_RES_SIZE	0x00050d4cL



extern void show_page_0_page(void);
extern void show_page_1_page(void);
extern void show_page_2_page(void);
extern void show_page_3_page(void);
extern void show_page_4_page(void);
extern void show_page_5_page(void);
extern void show_page_6_page(void);

#endif
#endif	// TEST12_RES_H_
