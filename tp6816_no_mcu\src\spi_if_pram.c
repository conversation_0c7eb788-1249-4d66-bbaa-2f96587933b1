/*
 * @file      spi_if_pram.c
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:55 created
 * $Id: spi_if_pram.c 1.1.develop.1 $
 */

#include "tp6823.h"
#include "cmd_queue.h"
#include "spi_if.h"

/* Refe to Options -> Target -> Off-chip Xdata memory:
 * When XRAMp Size = 
 *      2KB(0800H), Start = 0xF000
 *      3KB(0C00H), Start = 0xF000 
 *      4KB(1000H), Start = 0xF000
 */
/* Refe to Options -> A51 -> Misc Controls:
 *      DEFINE (XRAMPSIZE="800H")
 * When XRAMp Size = 
 *      0KB(0000H): PrgRAM Size = 4KB, Start = 0xF000 
 *      2KB(0800H): PrgRAM Size = 2KB, Start = 0xF800 
 *      3KB(0C00H): PrgRAM Size = 1KB, Start = 0xFC00 
 *      4KB(1000H): PrgRAM Size = 0KB
 */
/* Refe to Options -> LX51 Locate -> User classes: 
 *      CODE_PRAM (C:0xF800-C:0xFFFF), CONST_PRAM (C:0xF800-C:0xFFFF)
 * Specifying the memory location for all functions and const data (this file),
 * and the specified location is within the PrgRAM
 */
#if !defined(_EX_MCU_VERSION)
#pragma USERCLASS(CODE = PRAM)      // user class CODE_PRAM 
#pragma USERCLASS(CONST = PRAM)     // user class CONST_PRAM 
#endif

//#include "flash\snor_mx25l51245g.h"
//#include "flash\snor_mx25l12845g_dtr.h"
//#include "flash\snor_w25q128jv.h"
//#include "flash\snor_w25q128jv_dtr.h"
//#include "flash\snor_is25lp256.h"
//#include "flash\snor_mx25l12845g.h"
#include "spi_general_flash.h"

bit spi_watting_finish(int time)
{
    //bit tmp_EA;
	int timeout = 0;
    //tmp_EA = EA;
    //EA = 0;
	if ((read_p0(SPI_FETCH_DUMMY_REG) & 0x03) == SPI_IF_ENTER_XMODE) {
		write_p0(SPI_IF_RDBACK_SEL_REG, TRIGE_EXIT_XIP);
		write_p0(SPI_IF_RDBACK_SEL_REG, 0);
	}
    //EA = tmp_EA;
	write_p0(SPI_IF_CTRL_REG, (SPI_GET_CTRL | SPI_IF_ISSUE_BIT));
	add_time(timeout, time);
	while (SPI_ISSUE_DONE_) {
		if(is_time_outs(timeout)) {
			DBGMSG(("spi timeout\n"));
			return EXIT_FAILURE;
		}
    }
    return EXIT_SUCCESS;
}

bit spi_cmd_wr_ipram(unsigned char cmd, unsigned char *buf, unsigned char cnt)
{
    unsigned char i;
    int timeout = 0;

    SPI_SET_CMD(cmd);
    for(i = 0; i < cnt; i++)
        SPI_SET_WRD(i, buf[i]);
    if(SPI_IN_QPI())
        SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | QPI_IF_4I4O);
    else
        SPI_SET_MODE(SPI_IF_RDSR | SPI_IF_WREN | SPI_IF_1I1O);
    SPI_SET_RDD_CNT(0);
    SPI_SET_CTRL(SPI_IF_INSTR_YES | SPI_IF_WR_CNT(cnt) | SPI_IF_ISSUE_BIT);
    add_time(timeout, 500); // ms
    while (SPI_ISSUE_DONE_) {
        if(is_time_outs(timeout)) {
			return EXIT_FAILURE;
		}
    }
    return EXIT_SUCCESS;
}

void spi_delays_cfg_pram(unsigned char *tbl)
{
    unsigned char i;

    write_p0(0x9F, 0x40);       // Sel_PIO_Delay
    write_p0(0x86, tbl[0]);     // { SPI_i_Bias, SPI_i_Delay }
    write_p0(0x9F, 0x40 | 0x20);    // Sel_Fetch_Delay
    write_p0(0x86, tbl[0]);     // { SPI_i_Bias, SPI_i_Delay }
    write_p0(0x8B, tbl[1]);
    write_p0(0x8C, tbl[2]);
    if ((tbl[1] & 0x07) == 0) {
        for (i = 0; i < 3; ++i) {
            write_p0(0x8B, tbl[1] | i);
            write_p0(0x8C + i, tbl[2 + i]);
        }
    }
}

void spi_fetch_cfg_pram(unsigned char *tbl)
{
    unsigned char i;

    for (i = 0; i < 3; i++) {
        write_p0(0x8D + i, tbl[i]);
    }
}

extern bit spi_rdid_check(unsigned char mf_id, unsigned short dev_id);
extern bit spi_sr1_bit6_quad_enable_dbg(void);
extern bit spi_sr2_bit7_quad_enable_dbg(void);
extern bit spi_sr2_bit1_quad_enable_dbg(bit unrd);
extern bit spi_sr2_bit1_wr_quad_enable_dbg(void);
extern bit spi_quad_en(void);

unsigned char spi_delays_table[] = {
	0x12,	// SPI_i_Bias= 1 | SPI_i_Delay= 1
	0xE5,	// Eye_Result | Eye_Det_En | Eye_Auto
	0x10	// EyeX_Width_Sel= 0 | SPIDX_Delay= 10h
};
#define SPI_DELAYS_TABLE_SIZE   sizeof(spi_delays_table)
// Configures for SPI Fetch
// Page0: 0x8D, 0x8E, 0x8F,
unsigned char spi_fetch_table[] = {
	0xEB,	// Fetch Instruction
    0x22,   // 6 Dummy Cycle | XIP mode
	0x32,	// SPI+4i4o
};
#define SPI_FETCH_TABLE_SIZE   sizeof(spi_fetch_table)
// Configures for SPI Fetch
// Page3: 0x98, 0x99, 0x9A, 0x9B, 0x9C,
unsigned char spll_dividers_table[] = {
	0x26, 0x01, 0x01, 0x88, 0x48, 	// 120.0MHz
	// 0x2A, 0x01, 0x03, 0x08, 0x48,   // 79.2MHz
};
bit spll_init(void)
{
	short timeout = 0;
    unsigned char i;
    for (i = 0; i < 5; i++)
        write_p3((0x98 + i), spll_dividers_table[i]);
    // use SPLL or default 27MHz
    write_p3(0x9F, 0x98);
    add_time(timeout, SPI_ISSUE_TIMEOUT);
	while (read_p3(0x9F) & 0x10) {
		if (is_time_outs(timeout)) {
			DBGMSG(("spll_init timeout\n"));
			return EXIT_FAILURE;
		}
	}
	return EXIT_SUCCESS;
}

// SPI I/F init procedure begin, don't be interrupted till to the processing end
#pragma disable   /* Disable Interrupts */
void spiif_protocol_cfg(void)
{
#if !defined(SNOR_QER_BY_DETECT_ID)
#if defined(SNOR_ADD1_INSTR)
    spi_cmd_wr_ipram(SNOR_ADD1_INSTR, snor_add1_wrs, SNOR_ADD1_CNT);
#endif
#if defined(SNOR_ADD2_INSTR)
    spi_cmd_wr_ipram(SNOR_ADD2_INSTR, snor_add2_wrs, SNOR_ADD2_CNT);
#endif
#if defined(SNOR_ADD3_INSTR)
    spi_cmd_wr_ipram(SNOR_ADD3_INSTR, snor_add3_wrs, SNOR_ADD3_CNT);
#endif
#endif

    spi_delays_cfg_pram(spi_delays_table);
    spi_fetch_cfg_pram(spi_fetch_table);
    /* Note: don't change be call order for the above two functions since shadow registers. */

#if defined(SPIIF_EN_4B_ADDR)
    //SPI_4B_ENTER();
    spi_cmd_wr_ipram(read_p0(SPI_IF_INSTR_4B_ENTER_REG), 0, 0);
#endif
#if defined(SPIIF_EN_QPI_MODE)
    //SPI_QPI_ENTER();
    spi_cmd_wr_ipram(read_p0(SPI_IF_INSTR_QPI_ENTER_REG), 0, 0);
#endif
}

bit spiif_init (void)
{
#if defined(_EX_MCU_VERSION)
    write_p0(0xF7, 0x01);   // PDn_SPI_ = 1 for power up the SPI block
#endif

#if defined(SNOR_QER_BY_DETECT_ID)

    if (spi_quad_en() != EXIT_SUCCESS)
        return EXIT_FAILURE;

#else /* undefined(SNOR_QER_BY_DETECT_ID) */

    if (spi_rdid_check(SNOR_MFID, SNOR_DEVID) != EXIT_SUCCESS)
        return EXIT_FAILURE;

#if defined(SNOR_QPI_EN_CMD)
    SPI_SET_INSTR_QPI(SNOR_QPI_EN_CMD, SNOR_QPI_EX_CMD);
#endif
#if defined(SNOR_4B_EN_CMD)
    SPI_SET_INSTR_4BA(SNOR_4B_EN_CMD, SNOR_4B_EX_CMD);
#endif

#if defined(SNOR_QER_IS_SR1_BIT6_METHOD)
    if (spi_sr1_bit6_quad_enable_dbg() != EXIT_SUCCESS)
        return EXIT_FAILURE;
#elif defined(SNOR_QER_SR2_BIT7_METHOD)
    if (spi_sr2_bit7_quad_enable_dbg() != EXIT_SUCCESS)
        return EXIT_FAILURE;
#elif defined(SNOR_QER_SR2_BIT1_BUGGY_METHOD) || defined(SNOR_QER_SR2_BIT1_NO_RD_METHOD)
    if (spi_sr2_bit1_quad_enable_dbg(1) != EXIT_SUCCESS)
        return EXIT_FAILURE;
#elif defined(SNOR_QER_SR2_BIT1_METHOD)
    if (spi_sr2_bit1_quad_enable_dbg(0) != EXIT_SUCCESS)
        return EXIT_FAILURE;
#elif defined(SNOR_QER_SR2_BIT1_WR_METHOD)
    if (spi_sr2_bit1_wr_quad_enable_dbg() != EXIT_SUCCESS)
        return EXIT_FAILURE;
#endif
#endif  /* undefined(SNOR_QER_BY_DETECT_ID) */

#if defined(SPIIF_HIGHPERF_CODE)
    write_p0(0x9c, ((read_p0(0x9c) & 0x0f) | (SPIIF_HIGHPERF_CODE << 4)));
#endif
#if defined(SNOR_SW_RESET_F0)
    write_p0(0x90, (read(0x90) | 0x01));
#endif

    spiif_protocol_cfg();

    return spll_init();
}

extern unsigned char com_buffer[];
bit talk_flag;
unsigned char  BufWrPtr_cnt;
void talk_isr(void)
{
	unsigned char i;

	BufWrPtr_cnt = read_p0(0xF9);	// read_p0(REG_Cur_BufWrPtr);
	write_p0(0xF9, 0);			// write_p0(REG_Set_BufRdPtr, 0);
	for (i = 0; i < 6; ++i) {
		com_buffer[i] = read_p0(0xFB);
	}
	write_p0(0xFA, 0xEC);	// talk1_buf_clear();
	talk_flag = 1;
}
