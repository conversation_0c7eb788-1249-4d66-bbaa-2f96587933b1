/*
 * @file      mi2c.h
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:50 created
 * $Id: mi2c.h 1.1 $
 */

#ifndef MI2C_H_
#define MI2C_H_
#include "tp6823.h"
#define MI2C_SPEED_100K		0   // default
#define MI2C_SPEED_400K		1
#define MI2C_SPEED_1M		2
#define MI2C_SPEED_3d4M		3
#define MI2C_SPEED_3d4M_C	7

#define set_mi2c_speed(spd) I2CSTA = spd

bit mi2c_wr_byte(unsigned char sid, unsigned char addr, unsigned char value);
bit mi2c_rd_byte(unsigned char sid, unsigned char addr, unsigned char *value);
unsigned char mi2c_rd_byte2(unsigned char sid, unsigned char addr);

bit mi2c_rd_bytes(unsigned char sid, unsigned char addr, 
					unsigned char size, unsigned char *buf);
bit mi2c_wr_bytes(unsigned char sid, unsigned char addr, 
					unsigned char size, unsigned char *buf);

bit mi2c_2a_wr_byte(unsigned char sid, unsigned int addr, unsigned char value);
bit mi2c_2a_rd_byte(unsigned char sid, unsigned int addr, unsigned char *value);

bit mi2c_2a_wr_bytes(unsigned char sid, unsigned int addr, 
						unsigned char size, unsigned char *buf);
bit mi2c_2a_rd_bytes(unsigned char sid, unsigned int addr, 
					unsigned char size, unsigned char *buf);

bit mi2c_load_tbl(unsigned char sid, unsigned char *tbl,
					unsigned int size);
bit mi2c_2a_load_tbl(unsigned char sid, unsigned int *tbl,
					unsigned int size);

#endif /* MI2C_H_ */
