/*
 * bsp_i2c.c
 *
 *  Created on: 2022.2.26
 *      Author: lihao
 */

#include <string.h>
#include "bsp_i2c.h"
#include "i2c.h"
#include "gpio.h"

#pragma GCC push_options
#pragma GCC optimize("O0")


#if 0
static void gpio_interrupt_init()
{
	IRQ->SC &= ~IRQ_SC_IRQPDD_MASK;	// IRQ pull device enabled if IRQPE = 1.
	IRQ->SC |= IRQ_SC_IRQEDG_MASK; 	// IRQ is rising-edge or rising-edge/high-level sensitive
	IRQ->SC |= IRQ_SC_IRQPE_MASK; 	// 1 IRQ pin function is enabled
	//IRQ->SC |= IRQ_SC_IRQF_MASK; 	// 1 IRQ event is detected.  只锟斤拷
	IRQ->SC |= IRQ_SC_IRQACK_MASK; 	//只写  1 This write-only field is used to acknowledge interrupt request events (write 1 to clear IRQF).
	IRQ->SC |= IRQ_SC_IRQIE_MASK;  	//Interrupt requested whenever IRQF = 1
	IRQ->SC &= ~IRQ_SC_IRQMOD_MASK;  //0 IRQ event is detected only on falling/rising edges.

}
#endif

#define I2C4_SCL_GPIO	PTB
#define I2C4_SCL_PIN	PTB7
#define I2C4_SDA_GPIO	PTB
#define I2C4_SDA_PIN	PTB6

#define I2C_CLOCK_STRETCHING_SUPPORT 1

static void SCL_OUT(uint8_t port)
{
	switch(port){
	case 0:
		//I2C0
		CONFIG_PIN_AS_GPIO(PTA,PTA3,OUTPUT);
	case 1:
		//I2C1
		//SCL
		CONFIG_PIN_AS_GPIO(PTB,PTB1,OUTPUT);
		break;

	case 2:
		//I2C2
		//SCL
		CONFIG_PIN_AS_GPIO(PTH,PTH1,OUTPUT);
		break;

	case 3:
		//I2C3
		//SCL
		CONFIG_PIN_AS_GPIO(PTC,PTC0,OUTPUT);
		break;

	case 4:
		//I2C4
		//SCL
		CONFIG_PIN_AS_GPIO(I2C4_SCL_GPIO, I2C4_SCL_PIN, OUTPUT);
		break;
	default:break;
	}
}
static void SCL_SET(uint8_t port,uint8_t level)
{
	switch(port){
		case 0:
			//I2C0
			//SCL
			if(level){
				OUTPUT_SET(PTA,PTA3);
			}else{
				OUTPUT_CLEAR(PTA,PTA3);
			}
			break;
		case 1:
			//I2C1
			//SCL
			if(level){
				OUTPUT_SET(PTB,PTB1);
			}else{
				OUTPUT_CLEAR(PTB,PTB1);
			}
			break;

		case 2:
			//I2C2
			//SCL
			if(level){
				OUTPUT_SET(PTH,PTH1);
			}else{
				OUTPUT_CLEAR(PTH,PTH1);
			}
			break;

		case 3:
			//I2C3
			//SCL
			if(level){
				OUTPUT_SET(PTC,PTC0);
			}else{
				OUTPUT_CLEAR(PTC,PTC0);
			}
			break;

		case 4:
			//I2C4
			//SCL
			if(level){
				OUTPUT_SET(I2C4_SCL_GPIO, I2C4_SCL_PIN);
			}else{
				OUTPUT_CLEAR(I2C4_SCL_GPIO, I2C4_SCL_PIN);
			}
			break;
		default:break;

	}
}

static void SCL_IN(uint8_t port)
{
	switch (port) {
	case 0:
		//I2C0
		//SCL
		CONFIG_PIN_AS_GPIO(PTA, PTA3, INPUT);
		ENABLE_INPUT(PTA,PTA3);
		break;
	case 1:
		//I2C1
		//SCL
		CONFIG_PIN_AS_GPIO(PTB,PTB1,INPUT);
		ENABLE_INPUT(PTB,PTB1);
		break;
	case 2:
		//I2C2
		//SCL
		CONFIG_PIN_AS_GPIO(PTH,PTH1,INPUT);
		ENABLE_INPUT(PTH,PTH1);
		break;

	case 3:
		//I2C3
		//SCL
		CONFIG_PIN_AS_GPIO(PTC,PTC0,INPUT);
		ENABLE_INPUT(PTC,PTC0);
		break;

	case 4:
		//I2C4
		//SCL
		CONFIG_PIN_AS_GPIO(I2C4_SCL_GPIO,I2C4_SCL_PIN,INPUT);
		ENABLE_INPUT(I2C4_SCL_GPIO,I2C4_SCL_PIN);
		break;
	default:break;

	}
}

static uint8_t SCL_GET(uint8_t port)
{
	switch (port) {
	case 0:
		//I2C0
		return READ_INPUT(PTA, PTA3) ? 1 : 0;
	case 1:
		//I2C1
		return READ_INPUT(PTB, PTB1) ? 1 : 0;

	case 2:
		//I2C2
		return READ_INPUT(PTH, PTH1) ? 1 : 0;

	case 3:
		//I2C3
		return READ_INPUT(PTC, PTC0) ? 1 : 0;

	case 4:
		//I2C4
		return READ_INPUT(I2C4_SCL_GPIO, I2C4_SCL_PIN) ? 1 : 0;
	default:break;
	}

	return 1;
}

static void SDA_OUT(uint8_t port)
{
	switch(port){
	case 0:
		//I2C0
		//SDA
		CONFIG_PIN_AS_GPIO(PTA,PTA2,OUTPUT);
		break;
	case 1:
		//I2C1
		//SDA
		CONFIG_PIN_AS_GPIO(PTB,PTB0,OUTPUT);
		break;

	case 2:
		//I2C2
		//SDA
		CONFIG_PIN_AS_GPIO(PTH,PTH0,OUTPUT);
		break;

	case 3:
		//I2C3
		//SDA
		CONFIG_PIN_AS_GPIO(PTF,PTF7,OUTPUT);
		break;

	case 4:
		//I2C4
		//SDA
		CONFIG_PIN_AS_GPIO(I2C4_SDA_GPIO, I2C4_SDA_PIN, OUTPUT);
		break;
	default:break;
	}
}
static void SDA_SET(uint8_t port,uint8_t level)
{
	switch(port){
	case 0:
		//I2C0
		if(level){
			OUTPUT_SET(PTA,PTA2);
		}else{
			OUTPUT_CLEAR(PTA,PTA2);
		}

		break;
	case 1:
		//I2C1
		if(level){
			OUTPUT_SET(PTB,PTB0);
		}else{
			OUTPUT_CLEAR(PTB,PTB0);
		}

		break;

	case 2:
		//I2C2
		if(level){
			OUTPUT_SET(PTH,PTH0);
		}else{
			OUTPUT_CLEAR(PTH,PTH0);
		}
		break;

	case 3:
		//I2C3
		if(level){
			OUTPUT_SET(PTF,PTF7);
		}else{
			OUTPUT_CLEAR(PTF,PTF7);
		}
		break;

	case 4:
		//I2C4
		if(level){
			OUTPUT_SET(I2C4_SDA_GPIO, I2C4_SDA_PIN);
		}else{
			OUTPUT_CLEAR(I2C4_SDA_GPIO, I2C4_SDA_PIN);
		}
		break;
	default:break;
	}
}

static void SDA_IN(uint8_t port)
{
	switch(port){
	case 0:
		//I2C0
		CONFIG_PIN_AS_GPIO(PTA,PTA2,INPUT);
		ENABLE_INPUT(PTA,PTA2);
		break;
	case 1:
		//I2C1
		CONFIG_PIN_AS_GPIO(PTB,PTB0,INPUT);
		ENABLE_INPUT(PTB,PTB0);
		break;

	case 2:
		//I2C2
		//SDA
		CONFIG_PIN_AS_GPIO(PTH,PTH0,INPUT);
		ENABLE_INPUT(PTH,PTH0);
		break;

	case 3:
		//I2C3
		//SDA
		CONFIG_PIN_AS_GPIO(PTF,PTF7,INPUT);
		ENABLE_INPUT(PTF,PTF7);
		break;

	case 4:
		//I2C4
		//SDA
		ENABLE_INPUT(I2C4_SDA_GPIO, I2C4_SDA_PIN);
		CONFIG_PIN_AS_GPIO(I2C4_SDA_GPIO, I2C4_SDA_PIN, INPUT);

		break;
	default:break;
	}
}

static uint8_t SDA_GET(uint8_t port)
{
	switch(port){
	case 0:
		//I2C0
		return READ_INPUT(PTA,PTA2)? 1 : 0;
	case 1:
		//I2C1
		return READ_INPUT(PTB,PTB0)? 1 : 0;

	case 2:
		//I2C2
		return READ_INPUT(PTH,PTH0)? 1 : 0;

	case 3:
		//I2C3
		return READ_INPUT(PTF,PTF7)? 1 : 0;

	case 4:
		//I2C4
		return READ_INPUT(I2C4_SDA_GPIO, I2C4_SDA_PIN) ? 1 : 0;
	default:break;
	}

	return 1;
}


#define delay_us(x) { unsigned int _dcnt = (x*2); \
      while(_dcnt-- > 0);\
}

#define  freq   2		//defult 2
#define  Delay()  delay_us(freq)
#define  WAITTIME   80

void bsp_sim_i2c_init()
{

	uint8_t i;
#if 0
	for (i = 1; i <= 4; i++) {
		//DHU I2C閸忔娊妫�
		if (i == 2) {
			SCL_OUT(i);
			SDA_OUT(i);
			continue;
		}
		SCL_OUT(i);
		SCL_SET(i, 1);
		SDA_OUT(i);
		SDA_SET(i, 1);
	}
#else
#if 0
	SCL_IN(4);
	SDA_IN(4);
#else
	SCL_OUT(0);
	SCL_SET(0,1);
	SDA_OUT(0);
	SDA_SET(0,1);

	//SCL_OUT(4);
	//SCL_SET(4,1);
	//SDA_OUT(4);
	//SDA_SET(4,1);
	SCL_IN(4);
	SDA_IN(4);
#endif
#endif

	//Enable_Interrupt(IRQ_IRQn);
	//gpio_interrupt_init();

}

void IRQ_IRQHandler()
{

}

void i2c_start(uint8_t port)
{
	if(port == 4)
	{
		SDA_IN(port);
		Delay();
		SCL_IN(port);
		Delay();
		SDA_OUT(port);
		SDA_SET(port,0);
		Delay();
		SCL_OUT(port);
		SCL_SET(port,0);
		Delay();
	}
	else
	{
	SDA_OUT(port);
	SDA_SET(port,1);
	Delay();
	SCL_SET(port,1);
	Delay();
	SDA_SET(port,0);
	Delay();
	SCL_SET(port,0);
	Delay();
	}
}

void i2c_stop(uint8_t port)
{
	if(port==4)
	{
		SCL_OUT(port);
		SCL_SET(port,0);
		Delay();
		SDA_OUT(port);
		SDA_SET(port,0);
		Delay();
		SCL_IN(port);
		Delay();
		SDA_IN(port);
		Delay();
	}
	else
	{
	SDA_OUT(port);
	SCL_SET(port,0);
	Delay();
	SDA_SET(port,0);
	Delay();
	SCL_SET(port,1);
	Delay();
	SDA_SET(port,1);
	Delay();
	}

}

uint8_t i2c_wait_ack(uint8_t port)
{
	uint8_t ucTimeCnt= 0;

	if(port ==4)
	{
		SDA_IN(port);
		Delay();
		SCL_IN(port);
		Delay();
	#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
		Delay();
		SCL_IN(port);
		ucTimeCnt= 0;
		while (!SCL_GET(port)) {
			if(++ucTimeCnt > WAITTIME){
				i2c_stop(port);
				return 0;
			}
		} // 绛夊緟鏃堕挓绾挎媺楂�
	#endif
		ucTimeCnt= 0;
		SDA_IN(port);
		while(SDA_GET(port)){
			if(++ucTimeCnt > WAITTIME){
				i2c_stop(port);
				return 0;
			}
		}
		SCL_OUT(port);
		SCL_SET(port,0);
	}
	else
	{
	SDA_SET(port,1);
	SDA_IN(port);
	Delay();
	SCL_SET(port,1);
	Delay();
#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
	Delay();
	SCL_IN(port);
	ucTimeCnt= 0;
	while (!SCL_GET(port)) {
		if(++ucTimeCnt > WAITTIME){
			i2c_stop(port);
			return 0;
		}
	} // 绛夊緟鏃堕挓绾挎媺楂�
	SCL_OUT(port);
#endif
	ucTimeCnt= 0;
	while(SDA_GET(port)){
		if(++ucTimeCnt > WAITTIME){
			i2c_stop(port);
			return 0;
		}
	}

	SCL_SET(port,0);
	}
	return 1;
}

void i2c_send_ack(uint8_t port)
{
	uint8_t ucTimeCnt= 0;
	if(port == 4)
	{
		SDA_OUT(port);
		SDA_SET(port,0);
		Delay();
		SCL_IN(port);
	#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
		Delay();
		SCL_IN(port);
		ucTimeCnt = 0;
		while (!SCL_GET(port)) {
			if(++ucTimeCnt > WAITTIME){
				i2c_stop(port);
				return 0;
			}
		} // 绛夊緟鏃堕挓绾挎媺楂�
	#endif
		Delay();
		SCL_OUT(port);
		SCL_SET(port,0);
	}
	else
	{
	SDA_OUT(port);
	SDA_SET(port,0);
	Delay();
	SCL_SET(port, 1);
#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
	Delay();
	SCL_IN(port);
	ucTimeCnt = 0;
	while (!SCL_GET(port)) {
		if(++ucTimeCnt > WAITTIME){
			i2c_stop(port);
			return 0;
		}
	} // 绛夊緟鏃堕挓绾挎媺楂�
	SCL_OUT(port);
#endif
	Delay();
	SCL_SET(port,0);
	}

}
void i2c_send_noack(uint8_t port)
{
	uint8_t ucTimeCnt= 0;
	if (port == 4)
	{
		SDA_IN(port);
		Delay();
		SCL_IN(port);
	#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
		Delay();
		SCL_IN(port);
		ucTimeCnt = 0;
		while (!SCL_GET(port)) {
			if(++ucTimeCnt > WAITTIME){
				i2c_stop(port);
				return 0;
			}
		} // 绛夊緟鏃堕挓绾挎媺楂�
	#endif
		Delay();
		SCL_OUT(port);
		SCL_SET(port, 0);
	}
	else
	{
	SDA_OUT(port);
	SDA_SET(port,1);
	Delay();
	SCL_SET(port, 1);
#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
	Delay();
	SCL_IN(port);
	ucTimeCnt = 0;
	while (!SCL_GET(port)) {
		if(++ucTimeCnt > WAITTIME){
			i2c_stop(port);
			return 0;
		}
	} // 绛夊緟鏃堕挓绾挎媺楂�
	SCL_OUT(port);
#endif
	Delay();
	SCL_SET(port, 0);
	}
}

void  i2c_send_byte(uint8_t port, uint8_t data)
{

	uint8_t i = 8;
	uint8_t ucTimeCnt= 0;
	if(port ==4)
	{
			while(i--)
			{
				SCL_OUT(port);
				SCL_SET(port,0);
				Delay();
				if(data & 0x80)
				{
					SDA_IN(port);
				}
				else
				{
					SDA_OUT(port);
					SDA_SET(port,0);
				}

				Delay();
				data <<= 1;
				SCL_IN(port);
		#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
			Delay();
			SCL_IN(port);
			ucTimeCnt = 0;
			while (!SCL_GET(port)) {
				if(++ucTimeCnt > WAITTIME){
					i2c_stop(port);
					return 0;
				}
			} // 绛夊緟鏃堕挓绾挎媺楂�
		#endif
				Delay();
				SCL_OUT(port);
				SCL_SET(port,0);
				Delay();
			}
	}
	else
	{
	SDA_OUT(port);
	while(i--)
	{
		SCL_SET(port,0);
		Delay();
		Delay();
		if(data & 0x80)
		{
			SDA_SET(port,1);
		}
		else
		{
			SDA_SET(port,0);
		}

		Delay();
		data <<= 1;
		SCL_SET(port, 1);
#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
	Delay();
	SCL_IN(port);
	ucTimeCnt = 0;
	while (!SCL_GET(port)) {
		if(++ucTimeCnt > WAITTIME){
			i2c_stop(port);
			return 0;
		}
	} // 绛夊緟鏃堕挓绾挎媺楂�
	SCL_OUT(port);
#endif
		Delay();
		SCL_SET(port,0);
		Delay();
	}
	}
}

uint8_t i2c_recv_byte(uint8_t port)
{
	uint8_t i = 8;
	uint8_t data = 0;
	uint8_t ucTimeCnt= 0;
	if(port == 4)
	{
		SDA_IN(port);
		while(i--)
		{
			data <<= 1;
			SCL_OUT(port);
			SCL_SET(port,0);
			Delay();
			SCL_IN(port);
#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
			Delay();
			SCL_IN(port);
			ucTimeCnt = 0;
			while (!SCL_GET(port)) {
				if(++ucTimeCnt > WAITTIME*5){
					i2c_stop(port);
					return 0;
				}
			} // 绛夊緟鏃堕挓绾挎媺楂�
#endif
			Delay();
			SDA_IN(port);
			if(SDA_GET(port))
				data |= 0x01;
		}
		SCL_OUT(port);
		SCL_SET(port,0);
	}
	else
	{
	SDA_SET(port,1);
	SDA_IN(port);
	while(i--)
	{
		data <<= 1;
		SCL_SET(port,0);
		Delay();
		SCL_SET(port, 1);
#if (I2C_CLOCK_STRETCHING_SUPPORT == 1)
	Delay();
	SCL_IN(port);
	ucTimeCnt = 0;
	while (!SCL_GET(port)) {
		if(++ucTimeCnt > WAITTIME*5){
			i2c_stop(port);
			return 0;
		}
	} // 绛夊緟鏃堕挓绾挎媺楂�
	SCL_OUT(port);
#endif
		Delay();
		if(SDA_GET(port))
			data |= 0x01;
	}

	SCL_SET(port,0);
	}

	return data;
}
#if 0
uint8_t bsp_sim_i2c_write16_bytes( uint8_t port, uint8_t  dev_addr, uint16_t reg ,const uint8_t *pdata, uint16_t num)
{
	uint16_t i;

	i2c_start(port);

	i2c_send_byte(port,dev_addr);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port,reg >> 8);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port, reg & 0xff);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	for(i=0; i<num; ++i){

		i2c_send_byte(port, pdata[i]);
		if(i2c_wait_ack(port) == 0)
		{
			i2c_stop(port);
			return 0;
		}
	}

	i2c_stop(port);
	return 1;
}
#else
uint8_t bsp_sim_i2c_write16_bytes( uint8_t port, uint8_t  dev_addr, uint16_t reg ,const uint8_t *pdata, uint16_t num)
{
	uint16_t i;

	//__disable_irq();

	i2c_start(port);

	i2c_send_byte(port,dev_addr);
	if(i2c_wait_ack(port) == 0)
	{
		goto I2cErr;
	}

	i2c_send_byte(port, reg >> 8);
	if(i2c_wait_ack(port) == 0)
	{
		goto I2cErr;
	}

	i2c_send_byte(port, reg & 0xff);
	if(i2c_wait_ack(port) == 0)
	{
		goto I2cErr;
	}

	for(i=0; i<num; ++i){

		i2c_send_byte(port, pdata[i]);
		if(i2c_wait_ack(port) == 0)
		{
			goto I2cErr;
		}
	}
	i2c_stop(port);
	//__enable_irq();
	return 1;

I2cErr:
	i2c_stop(port);
	//__enable_irq();
	return 0;
}
#endif

uint8_t bsp_sim_i2c_read16_bytes( uint8_t port, uint8_t  dev_addr, uint16_t reg, uint8_t *pdata, uint16_t num)
{
	uint16_t i;

	i2c_start(port);

	i2c_send_byte(port,dev_addr);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port,reg >> 8);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port, reg & 0xff);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	//i2c_stop(port);

	i2c_start(port);
	i2c_send_byte(port,dev_addr|0x01);

	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	for(i=0; i < num; ++i){
		pdata[i] = i2c_recv_byte(port);
		if(i < num-1){
			i2c_send_ack(port);
		}
	}

	i2c_send_noack(port);
	i2c_stop(port);

	return 1;


}

#if 0
uint8_t bsp_sim_i2c_write8_bytes( uint8_t port, uint8_t  dev_addr, uint8_t reg ,const uint8_t *pdata, uint16_t num)
{
	uint16_t i;

	i2c_start(port);

	i2c_send_byte(port,dev_addr);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port, reg);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	for(i=0; i<num; ++i){

		i2c_send_byte(port, pdata[i]);
		if(i2c_wait_ack(port) == 0)
		{
			i2c_stop(port);
			return 0;
		}
	}

	i2c_stop(port);
	return 1;
}
#else

uint8_t bsp_sim_i2c_write8_bytes( uint8_t port, uint8_t  dev_addr, uint8_t reg ,const uint8_t *pdata, uint16_t num)
{

	uint16_t i;

	//__disable_irq();

	i2c_start(port);

	i2c_send_byte(port,dev_addr);
	if(i2c_wait_ack(port) == 0)
	{
		goto I2cErr; 
	}

	i2c_send_byte(port, reg);
	if(i2c_wait_ack(port) == 0)
	{
		goto I2cErr; 
	}

	for(i=0; i<num; ++i){

		i2c_send_byte(port, pdata[i]);
		if(i2c_wait_ack(port) == 0)
		{
			goto I2cErr; 
		}
	}
	i2c_stop(port);
	//__enable_irq();
	return 1;
	
I2cErr:
	i2c_stop(port);
	//__enable_irq();
	return 0;	
}

#endif

uint8_t bsp_sim_i2c_read8_bytes( uint8_t port, uint8_t  dev_addr, uint8_t reg, uint8_t *pdata, uint16_t num)
{
	uint16_t i;

	i2c_start(port);

	i2c_send_byte(port,dev_addr);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port, reg);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	//i2c_stop(port);

	i2c_start(port);
	i2c_send_byte(port,dev_addr|0x01);

	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	for(i=0; i < num; ++i){
		pdata[i] = i2c_recv_byte(port);
		if(i < num-1){
			i2c_send_ack(port);
		}
	}

	i2c_send_noack(port);
	i2c_stop(port);

	return 1;


}

uint8_t bsp_sim_i2c_write_reg( uint8_t port, uint8_t  dev_addr, uint16_t reg, uint8_t data )
{
	i2c_start(port);

	i2c_send_byte(port,dev_addr);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port,reg >> 8);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port, reg & 0xff);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port, data);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_stop(port);

	return 1;

}

uint8_t bsp_sim_i2c_read_reg( uint8_t port,  uint8_t  dev_addr, uint16_t reg, uint8_t *data )
{
	i2c_start(port);

	i2c_send_byte(port,dev_addr);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port,reg >> 8);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port, reg & 0xff);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	//i2c_stop(port);

	i2c_start(port);
	i2c_send_byte(port,dev_addr|0x01);

	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	*data = i2c_recv_byte(port);

	i2c_send_noack(port);
	i2c_stop(port);

	return 1;

}

//for EMC TEST
uint8_t bsp_sim_i2c_write8_bytes_ext( uint8_t port, const uint8_t *pdata, uint16_t num)
{
	uint16_t i;

	i2c_start(port);

	i2c_send_byte(port, pdata[0]);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	i2c_send_byte(port, pdata[1]);
	if(i2c_wait_ack(port) == 0)
	{
		i2c_stop(port);
		return 0;
	}

	for(i=2; i<num; ++i){

		i2c_send_byte(port, pdata[i]);
		if(i2c_wait_ack(port) == 0)
		{
			i2c_stop(port);
			return 0;
		}
	}

	i2c_stop(port);
	return 1;
}


#pragma GCC pop_options


// add by zyz 20240223 for i2c ota, don't modify anything below this line!!!!!!!!
#include "config.h"
#include "flash.h"
#include "tick.h"

const uint32_t magic_start	__attribute__ ((section(".m_const_config"))) = 0x68524553L;
const uint32_t magic_end	__attribute__ ((section(".m_const_config"))) = 0x35542586L;
const uint8_t sw_version[]	__attribute__ ((section(".m_const_config"))) = NXP_VERSION;

extern void watch_dog_reset_system(void);
typedef struct
{
	uint8_t infoDataLen;				   /*Exchange inforamtion length must N * 4.*/
	uint8_t requestEnterBootloader;	   /*Request enter bootloader mode flag*/
	uint8_t downloadAPPSuccessful;	   /*downlaod APP successful flag*/
	uint32_t infoStartAddr;			   /*exchange information start address*/
	uint32_t requestEnterBootloaderAddr; /*Request enter bootloader flag address */
	uint32_t downloadAppSuccessfulAddr;  /*download APP successful flag address*/
} tBootInfo;

typedef struct
{
	/*flash programming successfull? If programming successfull, the value set TRUE, else set FALSE*/
	uint8_t isFlashProgramSuccessfull;  

	/*Is erase flash successfull? If erased flash successfull, set the TRUE, else set the FALSE.*/
	uint8_t isFlashErasedSuccessfull;   

	/*Is Flash struct data valid? If writen set the value is TRUE, else set the valid FALSE*/
	uint8_t isFlashStructValid;

	/*indicate app Counter. Before download. */
	uint8_t appCnt; 

	/* flag if fingerprint buffer */
    uint8_t aFingerPrint[17];

	/*reset handler length*/
	uint32_t appStartAddrLen;

	/*app Start address -- reset handler*/
	uint32_t appStartAddr;

	/*count CRC*/
	uint32_t crc;
}tAppFlashStatus;

const static tBootInfo gs_stBootInfo = {
	16u, /*Exchange inforamtion length must N * 4.*/
	0x5Au,
	0xA5u,
	0x1FFFF000u,
	0x1FFFF001u,
	0x1FFFF000u,
};

static tAppFlashStatus app_flash_info;

/*set information CRC */
#define SetInforCRC(xCrc) ((*(uint16_t *)(gs_stBootInfo.infoStartAddr + 14)) = (uint16_t)(xCrc))
#define APP_IMAGE_START 0x8200l

static uint8_t is_bootloader_init_done = 0;
static uint16_t counter = 0;
static uint32_t cur_tick = 0;

uint8_t check_max9295a_state(void)
{
	uint8_t ret = 0;
	uint8_t value = 0;

	ret = bsp_sim_i2c_read16_bytes(4, 0x80, 0x0D, &value, 1);
	// DEBUG_LOGI("ret = 0x%x, MAX9295a id = 0x%x\r\n", ret, value);
	if(ret == 1 && value == 0x91)
	{
		// ret = bsp_sim_i2c_read16_bytes(4, 0x80, 0x0314, &value, 1);
		// if(ret == 1 && value == 0xaa)
			return 1;
	}
	return 0;

}


void enter_bootloader_process(void)
{
	if(is_bootloader_init_done == 0)
	{
		is_bootloader_init_done = 1;
		app_flash_info = *(tAppFlashStatus *)(APP_IMAGE_START - 0x200);
		if(app_flash_info.appCnt != 0xFF)
		{
			FLASH_Init(20000000);
			app_flash_info.crc = 0x0E5A;
			app_flash_info.appCnt = 0xFF;			
			FLASH_EraseSector(APP_IMAGE_START - 0x200);
			FLASH_Program(APP_IMAGE_START - 0x200, (uint8_t *)&app_flash_info, sizeof(tAppFlashStatus));		
			watch_dog_reset_system();
		}
	}

	// SDA_IN(4);
	// if(SDA_GET(4) == 0)
	// {
	// 	cur_tick = getSysTick();
	// 	counter++;
	// 	if(counter > 5)
	// 	{
	// 		/*Enter bootloader*/
	// 		memset((uint8_t *)gs_stBootInfo.infoStartAddr, 0x00, gs_stBootInfo.infoDataLen - 2u);
	// 		SetInforCRC(0x2A2C);

	// 		*(uint8_t *)gs_stBootInfo.requestEnterBootloaderAddr = gs_stBootInfo.requestEnterBootloader;
	// 		watch_dog_reset_system();
	// 	}
	// }
	// else
	// {
	// 	if(getSysTick() - cur_tick > 1000)
	// 	{
	// 		counter = 0;
	// 	}
	// }

	if(getSysTick() - cur_tick > 1000)
	{
		cur_tick = getSysTick();
		counter++;
		if(check_max9295a_state() && counter > 5)
		{
			/*Enter bootloader*/
			memset((uint8_t *)gs_stBootInfo.infoStartAddr, 0x00, gs_stBootInfo.infoDataLen - 2u);
			SetInforCRC(0x2A2C);

			*(uint8_t *)gs_stBootInfo.requestEnterBootloaderAddr = gs_stBootInfo.requestEnterBootloader;
			watch_dog_reset_system();		
		}
	}
}

