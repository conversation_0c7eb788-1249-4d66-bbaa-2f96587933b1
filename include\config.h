/*
  Copyright (C), 2018-2019, VCAE. Co., Ltd.
  FileName:    config.c
  Description:   系统配置文件包含BSW和ASW的配置
  Version: 0.1
  Function List: 
    1. gpio_init    
    2.
  History:    
      <author>   <time>      <version >                <desc>
        benny      19/11/1        0.1                  新创建模块
*/


#ifndef _CONFIG_H_
#define _CONFIG_H_

/**
 * @brief Singnal Interface Definition
 * 
 */
#define SIGNAL_UART1 	0
#define SIGNAL_10MS 	1
#define SIGNAL_1S 		2
#define SIGNAL_LIN 		3
#define SIGNAL_POWER 	4
#define SIGNAL_STATE 	5
#define SIGNAL_TP 	    6
#define SIGNAL_VEDIO 	7
#define SIGNAL_NVRAM	8
#define SIGNAL_ISP		9
#define SIGNAL_ADC		10
#define SIGNAL_OSD		11
#define SIGNAL_VIDEO_DETECT 12
#define SIGNAL_KEY_TASK	13
#define SIGNAL_MENU_TASK	14
/**
 * @brief LIN序列码定义
 * 
 */
#define LIN_PART_NUMBER   		0x6608082124 //0x6608251124 //0x8893184213
#define LIN_SERIAL_NUMBER		0x12345678

#define USE_LIN_AND_KEY		1
#define USE_API_CODE_LOAD_APPLY_ISPCFGBIN 0    //deprecated
#define USE_API_CODE_LOAD_APPLY_ISPCFGBIN_BYLIN 1
#define USE_API_CODE_MIRROR_REWRN 1
#define USE_API_CODE_MIRROR_REWRN_FLICKING 1

//NXP
#define NXP_VERSION   "EF1E1v4.2.2N"
#define LIN_VERSION_NXP   0x34  //1字节
//GEO
#define GEO_VERSION   "EF1E1v3.0.0G"
#define LIN_VERSION_GEO                0x00  //1字节
//TP
#define LIN_VERSION_TP         0x00  //1字节
//LIN
#define LIN_VERSION (LIN_VERSION_TP<<16)|(LIN_VERSION_GEO<<8)|LIN_VERSION_NXP //3字节

/**
 * @brief softversion  S9KEAZN64 | GEO
 * 
 */
#define SOFTVERSION    NXP_VERSION"|"GEO_VERSION


#define USE_FUN_WATCHDOG		1			//看门狗
#define USE_FUN_MIRRCOMM		1			//与后视镜参数传递
#define USE_ISP_REPOWER         1			//没有图像摄像头重新上电
#define USE_ISP_REPOWER_DEBUG   0			//没有图像摄像头重新上电调试


#define USE_DEBUG_LOG			1			//开启调试信息
#define USE_ORIGIN_POWERON      0           //使用原始上电程序
#define USE_ONLY_POWERON        0           //仅启动上电程序

#define USE_TEST_VEDIO			0			//图像参数、告警设置测试
#define USE_FROZEN_PRINT        0           //打印冻结值
#define USE_GEO_TEST            0           //GEO视野视角测试
#define USE_GEO_PRINT_HEX       0           //GEO HEX数据打印
#define USE_ISP_LAW_BY_KEY		0			//ISP 测试时LAW法规触发起效
#define USE_LIN_SLEEP_MODE		0			//lin  sleep  模式启用
#define USE_TP_CHECK_P1_A9		0			//check frame lock
#define USE_LCM_FLIP			1			//屏幕翻转宏
#define USE_ISP_ANGLE_FLIP		1
#define USE_ISP_LAW_FLIP		1
#define USE_TD_TIMING_DETECT	1			//使用TD_TIMING_DETECT
#define USE_LIN_NO_RESPONSE_BY_POWER 1	     //电压超出或者超低时，需要关闭lin 数据response
#define USE_MPQ7928_125_DEFINED	1			//MPQ7928默认1.25倍限流
#define USE_SSC_PPM_REG			1
#include "printf.h"
#define SENSOR_TYPE_HIK_TYPE_1		0x01
#define SENSOR_TYPE_HIK_TYPE_2		0x02
#define SENSOR_TYPE_DH_TYPE_1		0x03
#define SENSOR_TYPE_HIK_TYPE_3		0x04
#define SENSOR_TYPE	SENSOR_TYPE_HIK_TYPE_3
#if (USE_DEBUG_LOG == 1)


//#define DEBUG_LOGI(format, ... ) printf(format, ##__VA_ARGS__)
#define DEBUG_LOGI(format, ... )  printf("(%s)%d:",__FUNCTION__, __LINE__); printf(format, ##__VA_ARGS__)

#else

#define DEBUG_LOG(format, ... )

#define DEBUG_LOGI(format, ... )

#endif

//EC
#define GEELY_EF1E 1

//for EMC TEST
#define EMC_TEST 0

#define TP_HB_DET			1
#define TP_HB_DEAD_MS		3000//		//Heart beaf max delay ms

#define LIN_TP_VERSION_QUERY    1
#define LIN_ISP_VERSION_QUERY    1
#define LIN_ISP_OTHER_QUERY    1
#define ISP_VEHICLE_TYPE_QUERY  0    //ISP车型信息查询功能，默认关闭

#define HW_VERSION_CHECK    1
#define HW_VERSION_TYPE_5DOT1K 0x01
#define HW_VERSION_TYPE_3DOT3K 0x02
#define HW_VERSION_TYPE HW_VERSION_TYPE_3DOT3K

#define OSD_EVENT_BRIGHT_ANGLE_VIEW_SKIP 1
#define EMC_SIM_SOPT0_RSTPE_SIM_SOPT0_SWDE_SET_ZERO 0
#define READ_FROZEN_DATA_FROM_TP_TIMES 2 //read 2 times, default 1 time

#define ADC_12V_TEMPERATURE_COMPENSATION    1

#endif

