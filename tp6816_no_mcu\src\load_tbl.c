/*
 * @file      load_tbl.c
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:55 created
 * $Id: load_tbl.c 1.1 $
 */

#include "tp6823.h"
#include "load_tbl.h"

/* important! size must be less than 256 */
void load_tbl_p0(unsigned char  *tbl, unsigned int size)
{
	unsigned int i = 0;
	for (i = 0; i < size; i += 2)
		write_p0(tbl[i], tbl[i + 1]);
}

void load_tbl_p1(unsigned char  *tbl, unsigned int size)
{
	unsigned int i = 0;
	for (i = 0; i < size; i += 2)
		write_p1(tbl[i], tbl[i + 1]);
}

void load_tbl_p2(unsigned char  *tbl, unsigned int size)
{
	unsigned int i = 0;
	for (i = 0; i < size; i += 2)
		write_p2(tbl[i], tbl[i + 1]);
}

void load_tbl_p3(unsigned char  *tbl, unsigned int size)
{
	unsigned int i = 0;
	for (i = 0; i < size; i += 2)
		write_p3(tbl[i], tbl[i + 1]);
}

void load_tbl_vd(unsigned char  *tbl, unsigned int size)
{
	unsigned int i = 0;
	for (i = 0; i < size; i += 2)
		write_vd(tbl[i], tbl[i + 1]);
}

#if defined(_EX_MCU_VERSION)
void load_tbl_cq(unsigned char  *tbl, unsigned int size)
{
#ifdef SUPPORT_I2C_BURST_WR
	write_p0(0xE2, 0x00);
	write_burst_p0(0xE1, tbl, size);
#else
	unsigned int i = 0;
	write_p0(0xE2, 0x00);
	for (i = 0; i < size; i++)
		write_p0(0xE1, tbl[i]);
#endif		
}

void load_tbl_cq_ptr(unsigned char ptr, unsigned char  *tbl, unsigned int size)
{
#ifdef SUPPORT_I2C_BURST_WR
	write_p0(0xE2, ptr);
	write_burst_p0(0xE1, tbl, size);
#else
	unsigned int i = 0;
	write_p0(0xE2, ptr);
	for (i = 0; i < size; i++)
		write_p0(0xE1, tbl[i]);
#endif		
}
#else
void load_tbl_cq(unsigned char  *tbl, unsigned int size)
{
	unsigned int i = 0;
	for (i = 0; i < size; i++)
		CQ_BUFFER[i] = tbl[i];
}

void load_tbl_cq_ptr(unsigned char ptr, unsigned char  *tbl, unsigned int size)
{
	unsigned int i = 0;
	for (i = 0; i < size; i++)
		CQ_BUFFER[ptr++] = tbl[i];
}
#endif
