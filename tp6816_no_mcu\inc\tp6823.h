/*
 * @file	tp6823.h
 * @brief
 * <AUTHOR>
 * @date	2021-03-10 17:20:27 created
 * $Id: tp6823.h 1.1 $
 */
#ifndef TP6823_H_
#define TP6823_H_

 /**
  * The purpose of #define macros below is to classify those functions which
  * only for internal MCU use. Enabling it for known which functions need porting
  * if developers would like running on another CPU instead of internal MCU.
  */
#define _EX_MCU_VERSION
#define bit char
#if !defined(_EX_MCU_VERSION)
  /**
   * When running on the internal MCU, the following implemented #define macros
   * to access registers of Scaler/VD.
   * These macros provide arrays or pointers to each register location.
   */
#define ORAM_START_XRAM_ADDRESS				0x0000
#define SPI_BUFFER_START_XRAM_ADDRESS		0x9000
#define CQ_BUFFER_START_XRAM_ADDRESS		0x9400
#define SCALER_CFG_P0_START_XRAM_ADDRESS	0xC000
#define SCALER_CFG_P1_START_XRAM_ADDRESS	0xC100
#define SCALER_CFG_P2_START_XRAM_ADDRESS	0xC200
#define SCALER_CFG_P3_START_XRAM_ADDRESS	0xC300
#define VD_CFG_P0_START_XRAM_ADDRESS		0xD000

#define SCALER_P0 ((unsigned char volatile xdata *) SCALER_CFG_P0_START_XRAM_ADDRESS)
#define SCALER_P1 ((unsigned char volatile xdata *) SCALER_CFG_P1_START_XRAM_ADDRESS)
#define SCALER_P2 ((unsigned char volatile xdata *) SCALER_CFG_P2_START_XRAM_ADDRESS)
#define SCALER_P3 ((unsigned char volatile xdata *) SCALER_CFG_P3_START_XRAM_ADDRESS)
#define VD_CFG ((unsigned char volatile xdata *) VD_CFG_P0_START_XRAM_ADDRESS)
#define CQ_BUFFER ((unsigned char volatile xdata *) CQ_BUFFER_START_XRAM_ADDRESS)
#define OSD_RAM ((unsigned char volatile xdata *) ORAM_START_XRAM_ADDRESS)
#define SPI_BUFFER ((unsigned char volatile xdata *) SPI_BUFFER_START_XRAM_ADDRESS)

#define write_p0(index, value)  SCALER_P0[index] = value
#define write_p1(index, value)  SCALER_P1[index] = value
#define write_p2(index, value)  SCALER_P2[index] = value
#define write_p3(index, value)  SCALER_P3[index] = value
#define write_vd(index, value)  VD_CFG[index] = value
#define read_p0(index)  SCALER_P0[index]
#define read_p1(index)  SCALER_P1[index]
#define read_p2(index)  SCALER_P2[index]
#define read_p3(index)  SCALER_P3[index]
#define read_vd(index)  VD_CFG[index]

   //#define RES_BUILTIN_CODESPACE

#else	/* _EX_MCU_VERSION */

  /**
   * When not run on the internal MCU, the following an abstraction set of functions
   * for I2C peripheral to access registers of Scaler/VD and developers need to
   * implement I2C protocol on them CPU.
   */
#include "mi2c.h"
#define SUPPORT_I2C_BURST_WR
#define TP6823_P0_SID	0xB8
#define TP6823_P1_SID	0xBA
#define TP6823_P2_SID	0xBC
#define TP6823_P3_SID	0xBE
#define TP6823_VD_SID	0x88
#define write_p0(index, value)  \
		mi2c_wr_byte(TP6823_P0_SID, index, value)
#define write_p1(index, value)  \
		mi2c_wr_byte(TP6823_P1_SID, index, value)
#define write_p2(index, value)  \
		mi2c_wr_byte(TP6823_P2_SID, index, value)
#define write_p3(index, value)  \
		mi2c_wr_byte(TP6823_P3_SID, index, value)
#define write_vd(index, value)  \
		mi2c_wr_byte(TP6823_VD_SID, index, value)
#define read_p0(index)  \
		mi2c_rd_byte2(TP6823_P0_SID, index)
#define read_p1(index)  \
		mi2c_rd_byte2(TP6823_P1_SID, index)
#define read_p2(index)  \
		mi2c_rd_byte2(TP6823_P2_SID, index)
#define read_p3(index)  \
		mi2c_rd_byte2(TP6823_P3_SID, index)
#define read_vd(index)  \
		mi2c_rd_byte2(TP6823_VD_SID, index)
#ifdef SUPPORT_I2C_BURST_WR
#define write_burst_p0(index, value, size)  \
		mi2c_wr_bytes(TP6823_P0_SID, index, size, value)
#define write_burst_p1(index, value, size)  \
		mi2c_wr_bytes(TP6823_P1_SID, index, size, value)
#define write_burst_p2(index, value, size)  \
		mi2c_wr_bytes(TP6823_P2_SID, index, size, value)
#define write_burst_p3(index, value, size)  \
		mi2c_wr_bytes(TP6823_P3_SID, index, size, value)
#define write_burst_vd(index, value, size)  \
		mi2c_wr_bytes(TP6823_VD_SID, index, size, value)
#define read_burst_p0(index, value, size)  \
		mi2c_rd_bytes(TP6823_P0_SID, index, size, value)
#define read_burst_p1(index, value, size)  \
		mi2c_rd_bytes(TP6823_P1_SID, index, size, value)
#define read_burst_p2(index, value, size)  \
		mi2c_rd_bytes(TP6823_P2_SID, index, size, value)
#define read_burst_p3(index, value, size)  \
		mi2c_rd_bytes(TP6823_P3_SID, index, size, value)
#define read_burst_vd(index, value, size)  \
		mi2c_rd_bytes(TP6823_VD_SID, index, size, value)
#endif
   //#define _NON_SPI_FLASH	// without NOR Flash

#endif

#define CHIP_ID				0x6823
#define REVISION_ID			0x00
#define BOUNDING_ID			0xF
#define L_BOUNDING_ID		0xD

#define CHIP_ID_REG			0xFE
#define read_cid()			((read_p0(CHIP_ID_REG) << 8) | (read_p0(CHIP_ID_REG+1)))
#define REVISION_ID_REG		0xFC
#define read_rid()			(read_p0(REVISION_ID_REG))
#define BOUNDING_ID_REG		0xF0
#define read_bid()			(read_p0(BOUNDING_ID_REG) >> 4)
#define IS_X_BOUNDING(X)	((read_bid() == X)? 1 : 0)
#define IS_L_BOUNDING()		IS_X_BOUNDING(L_BOUNDING_ID)
#define IS_FPGA_VERSION()	IS_X_BOUNDING(0)


#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define _DEBUG
#ifdef _DEBUG
#define DBGMSG(x)	{ printf x; }
#else
#define DBGMSG(x)
#endif

//#define assert(x)

#define EXIT_SUCCESS	0
#define EXIT_FAILURE	1

#define RET_SUCCESS		0
#define RET_FAILURE		1
#if 0
#include "OWL_DP8051.h"
#endif
#if 0
#include "tick.h"

#define read_cpu_count()	getSysTick() //((CNT1MSH << 8) | CNT1MSL)
#define is_time_outs(val)	((int) (read_cpu_count() - val) >= 0)
//#define read_cpu_count_8()	(CNT1MSL)
//#define is_time_outs_8(val)	((char) (read_cpu_count_8() - val) >= 0)
#define times_up(val)	(val == 0 ? 0 : ((int) (read_cpu_count() - val) >= 0))
#define add_time(time, val)	\
	do { \
		if((time = read_cpu_count() + val) == 0) \
			time = 1; \
	} while(0);
#else
#include "tick.h"
extern uint8_t read_cpu_count(void);
extern uint8_t is_time_outs(uint16_t _timeout);
#define add_time(time, val)	\
	do { \
		if((time = read_cpu_count() + val) == 0) \
			time = 1; \
	} while(0)
#endif
#define disable_count()		(0)
//#define _FPGA_VERSION

#define BIT(n)					( 1<<(n) )
#define BIT_SET(y, mask)		( y |=  (mask) )
#define BIT_CLR(y, mask)		( y &= ~(mask) )
#define BIT_FLIP(y, mask)		( y ^=  (mask) )
#define BIT_MASK(len)			( BIT(len)-1 )
#define BF_MASK(start, len)		( BIT_MASK(len)<<(start) )
#define BF_PREP(x, start, len)	( ((x)&BIT_MASK(len)) << (start) )
#define BFN_GET(y, _BFDEF)		( ((y)>>(_BFDEF##_ST)) & BIT_MASK(_BFDEF##_LEN) )
#define BFN_SET(y, x, _BFDEF)	( y= ((y) &~ BF_MASK(_BFDEF##_ST, _BFDEF##_LEN)) | BF_PREP(x, _BFDEF##_ST, _BFDEF##_LEN) )

#endif /* TP6823_H_ */
