#include "para.h"
#include "flash.h"
#include "null.h"
#include "bsp_nvram.h"
#include "tick.h"
#include "type.h"
#include <string.h>
#include <stdio.h>
#include "config.h"
static uint16_t  tick = 0;
nvram_file_type  config_file;
static nvram_file_type  config_file_backup;

static uint8_t nvram_check_ok(nvram_file_type * params)
{
    uint32_t  i, len, chk = 0;
    uint8_t * p;

    if (NULL == params)
        return 0;    if (cParamMark != params->mark)
        return 0;

    p = (uint8_t*)params;

    len = sizeof(nvram_file_type) - 4;

    for (i = 0; i < len; i++)
    {
        chk += *p;
        p++;
    }

    if (0 == ((chk + params->check)))
        return 1;

    return 0;
}

static uint8_t nvram_calc_check(nvram_file_type * params)
{
    uint32_t  i, len, chk = 0;
    uint8_t * p;

    if (NULL == params)
        return 0;
    if (cParamMark != params->mark)
        return 0;

    p = (uint8_t*)params;
    len = sizeof(nvram_file_type) - 4;
    chk = 0;
    for (i = 0; i < len; i++)
    {
        chk += *p;
        p++;
    }
    params->check = (0 - chk);

    return 1;
}

void nvram_reset_user_setting(void)
{
	config_file.vedio_para.StreamingMirrBriAdjmt = 10;
	config_file.vedio_para.StreamingMirrImgAdjmt = 2;
	config_file.vedio_para.StreamingMirrPosnAdjmt = 3;
	config_file.vedio_para.StreamingMirrSwt = 1;
#if (USE_API_CODE_LOAD_APPLY_ISPCFGBIN_BYLIN == 1)
	config_file.vedio_para.StreamingMirrModeSwt = 0x00;
#endif
#if (USE_API_CODE_MIRROR_REWRN == 1)
	config_file.vedio_para.StreamingMirrReWnSwt = 0x00;
	config_file.vedio_para.StreamingMirrReWrnDis = 0x00;
#endif

	para_load_default(&config_file.vedio_para);
}

void nvram_reset(void)
{
    uint16_t  i;
    uint8_t * p;

    DEBUG_LOGI("nvram_reset\r\n");
    p = (uint8_t*)&config_file;
    for (i = 0; i < sizeof(config_file); i++)
        *p++ = 0;

    // memset(&config_file, 0, sizeof(config_file));
    config_file.mark = cParamMark;
    config_file.version = NVRAM__VERSION;

    nvram_reset_user_setting();

    nvram_calc_check(&config_file);

    nvram_write();
}

void nvram_init(void)
{
    uint16_t  i;
    uint8_t * p, * s;

    // memset(&config_file, 0, sizeof(config_file));

    nvram_read();

    if (NVRAM__VERSION != config_file.version)
    {
        nvram_reset();
    }
    // memcpy(&config_file_backup, &config_file, sizeof(config_file));
    s = (uint8_t*)&config_file;
    p = (uint8_t*)&config_file_backup;
    for (i = 0; i < sizeof(config_file); i++)
        *p++ = *s++;
}

void nvram_write(void)
{
    uint8_t  j;
    nvram_calc_check(&config_file);

    DEBUG_LOGI("nvram write\r\n");
    for (j = 0; j < 2; j++)
    {
    	FLASH_EraseSector((uint32_t)(NVRAM_BASE_ADDR + (uint32_t)j * (uint32_t)NVRAM_SECTOR_SIZE));
    	FLASH_Program((uint32_t)(NVRAM_BASE_ADDR + (uint32_t)j * (uint32_t)NVRAM_SECTOR_SIZE), (uint8_t*)&config_file, sizeof(nvram_file_type));
    }
}

void nvram_read(void)
{
    uint8_t  j;
    FLASH_Init(SystemCoreClock);

    for (j = 0; j < 2; j++)
    {
    	memcpy((uint8_t*)&config_file, (uint32_t)(NVRAM_BASE_ADDR + (uint32_t)j * (uint32_t)NVRAM_SECTOR_SIZE), sizeof(nvram_file_type));

        if (nvram_check_ok(&config_file) == 1)
            break;
    }

    if (j == 2)
    {
        nvram_reset();
    }
}

void nvram_update(void)
{
    SysSetCurrentTick(&tick);
}

void nvram_task(void)
{
    if (tick == 0)
        return;

    if (SysGetLapseTick(tick) > 5000)
    {
        tick = 0;
        nvram_write();
    }
}
