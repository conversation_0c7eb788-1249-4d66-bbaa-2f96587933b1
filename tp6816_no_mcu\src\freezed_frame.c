/**
* @file		freezed_diff.c
* <AUTHOR>
* @date		Tue Jun 4 16:34:44 2019 +0800
* @version	master
*/

#include "tp6806.h"
#include "osd_func.h"
#include "freezed_frame.h"
#include "tick.h"
#include "config.h"

static uint16_t freezed_frame_tick;

#if ! defined(_EX_MCU_VERSION)
#pragma USERCLASS(CODE = PRAM)  // user class CODE_PRAM 
#pragma USERCLASS(CONST = PRAM) // user class CONST_PRAM 
#endif

#if (FRZRAM_ORD == 0)
uint8_t  PRE_FRZ_RAM[FRZRAM_BLOCK_CNT] _at_(0xF000);
#else
uint8_t  PRE_FRZ_RAM[FRZRAM_VBLOCK_CNT][FRZRAM_HBLOCK_CNT];// _at_(0xF000);
#endif


#ifdef DBG_FRZ_RAM
#if (FRZRAM_ORD == 0)
uint8_t  PRE_FRZ_RAM_CPY[256] = { 0 };
#else
uint8_t  PRE_FRZ_RAM_CPY[16][16] = { 0 };
#endif
uint8_t  pOutputDelay50msCnt = 0;
#endif

uint8_t frz_need_trans;
uint8_t *PRE_FRZ_RAM_ptr = PRE_FRZ_RAM;

static void frzen_ram()
{
    uint8_t cnt, x, y;

    //	reset point, P0_3Dh is FrzRAM Pointer when write
    write_p0(0x3D, 0x00);
    cnt = 0;
#if (FRZRAM_ORD == 0)// default
    for (y = 0; y < FRZRAM_VBLOCK_CNT; ++y)
    {
        for (x = 0; x < FRZRAM_HBLOCK_CNT; ++x)
        {
            // read value, P0_3Dh is Frz_BlockValue when read
            PRE_FRZ_RAM[cnt] = read_p0(0x3D);
            cnt++;
        }
    }
#elif(FRZRAM_ORD == 1)//invert X
    for (y = 0; y < FRZRAM_VBLOCK_CNT; y++)//y direction 0~15
    {
        for (x = FRZRAM_HBLOCK_CNT; x > 0; x--)//x direction 16~1
        {
            PRE_FRZ_RAM[y][x - 1] = read_p0(0x3D);
        }
    }
#elif(FRZRAM_ORD == 2)//invert Y
    for (y = FRZRAM_VBLOCK_CNT; y > 0; y--)//y direction 16~1
    {
        for (x = 0; x < FRZRAM_HBLOCK_CNT; x++)//x direction 0~15
        {
            PRE_FRZ_RAM[y - 1][x] = read_p0(0x3D);
        }
    }
#elif(FRZRAM_ORD == 3)//invert X and Y
    for (y = FRZRAM_VBLOCK_CNT; y > 0; y--)//y direction 16~1
    {
        for (x = FRZRAM_HBLOCK_CNT; x > 0; x--)//x direction 16~1
        {
            PRE_FRZ_RAM[y - 1][x - 1] = read_p0(0x3D);
        }
    }
#else
#endif

    /*
    for (y = 0; y < FRZRAM_VBLOCK_CNT; ++y) {
        for (x = 0; x < FRZRAM_HBLOCK_CNT; ++x) {
            DBGMSG(("%02bx ", PRE_FRZ_RAM[cnt]));
            cnt++;
        }
        DBGMSG(("\r\n"));
    }
*/

}


extern uint8_t getVideoFlag(void);
void frozen_task(void)
{

    if (SysGetLapseTick(freezed_frame_tick) >= 20)
    {
        SysSetCurrentTick(&freezed_frame_tick);
        if(getVideoFlag())
        {
        	frzen_ram();
        	//DEBUG_LOGI("get frzern_ram\r\n");
        }

    }

    /*
    if (VSi_Signal) {
        VSi_Signal = 0;

        if (frz_need_trans == 0) {
            frzen_ram();
            frz_need_trans = 1;
        }
    }
    */

}

void freezed_init(void)
{
    // Freeze Detection
////	write_p0(0x3C, 0x30);   // enable Frz_Halt & Frz_Live interrupts
    write_p0(0x3C, 0x00);   // disable Frz_Halt & Frz_Live interrupts
    //write_p0(0x3A, 0x84);	// single center-dot mode enable Freeze detection, 0xA4,
    write_p0(0x3A, 0xA4);   //average mode
    //	 write_p0(0x3A, read_p0(0x3A) & 0x7F); //Disable Freeze detection

    write_p0(0x39, read_p0(0x39) & 0x9F); //luma transition threshold   0

    // configure Pin Function Select for GPIOA1
    //write_p3(0x77, (read_p3(0x77) & 0xf3));
    //set_gpio_bus_mode(GPIO_A_PORT_NUM, GPIO_SET_CLR_MODE);
    //set_gpio_output(GPIO_A_PORT_NUM, (1 << 1)); // set GPIOA0 as output
    //set_gpio_clr(GPIO_A_PORT_NUM, (1 << 1));    // set GPIOA0 low

    //is_timingChange = 0;
    SysSetCurrentTick(&freezed_frame_tick);
    //VSi_Signal = 0;
}

/*
#define DIS_FRZ_DETECT  1

void freezed_init(void)
{

#ifdef DIS_FRZ_DETECT
 write_p0(0x3A, read_p0(0x3A) & 0x7F); //Disable Freeze detection
#else
    //Freeze Detection
    //write_p0(0x3C, 0x30);                           // enable Frz_Halt & Frz_Live interrupts
    write_p0(0x3C, 0x00);                           // disable Frz_Halt & Frz_Live interrupts

 //BIT5,6 define frozen
    //write_p0(0x3A, 0x84);     // single center-dot mode enable Freeze detection, 0xA4,
 write_p0(0x3A, (0x84|CBIT5) );  //average mode
 //write_p0(0x3A, (0x84|CBIT6) );  //transition position mode
 //write_p0(0x3A, (0x84|CBIT5|CBIT6) ); //taking turn of mode 0~2


    write_p0(0x39, read_p0(0x39) & 0x9F);           //luma transition threshold   0

    // configure Pin Function Select for GPIOA1
    //write_p3(0x77, (read_p3(0x77) & 0xf3));
    //set_gpio_bus_mode(GPIO_A_PORT_NUM, GPIO_SET_CLR_MODE);
    //set_gpio_output(GPIO_A_PORT_NUM, (1 << 1)); // set GPIOA0 as output
    //set_gpio_clr(GPIO_A_PORT_NUM, (1 << 1));   // set GPIOA0 low
    //is_timingChange = 0;
    VSi_Signal = 0;

    #endif
}
*/
