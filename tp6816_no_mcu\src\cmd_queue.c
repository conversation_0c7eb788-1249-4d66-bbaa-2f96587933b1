/*
 * @file      cmd_queue.c
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:55 created
 * $Id: cmd_queue.c 1.1 $
 */


#include "tp6823.h"
#include "cmd_queue.h"
#include "config.h"

unsigned char cq_buf_ptr = 0;

static uint16_t read_cpu_tick=0;
uint8_t read_cpu_count(void)
{
	SysSetCurrentTick(&read_cpu_tick);
	return 0;
}
uint8_t is_time_outs(uint16_t _timeout)
{
	if(SysGetLapseTick(read_cpu_tick)>=_timeout)
		return 1;
	else
		return 0;
}

void cq_init(void)
{
	write_p0(CQ_CTRL, 0x05);
	write_p0(CQ_P_PTR, 0x00);
	write_p0(CQ_E_PTR, 0x00);
	//write_p0(CQ_INT, (CQ_TOUT_IM_BIT | CQ_DONE_IM_BIT)));
	cq_buf_ptr = 0;
}

void cq_set_trig_mode(uint8_t mode)
{
    uint8_t set = read_p0(CQ_CTRL);
    //BFN_SET(set, mode, BFN_TRIG);
	set &= 0xc7;
	set |= (mode << 4);
	write_p0(CQ_CTRL, set);
}

void cq_ptr_rst(void)
{
	write_p0(CQ_CTRL, (read_p0(CQ_CTRL) | CQ_PTRRST_BIT));
    cq_buf_ptr = 0;
}

bit cq_pause_issue(short ms)
{
	short tm_out;
	uint8_t issue;

	write_p0(CQ_CTRL, (read_p0(CQ_CTRL) | CQ_TRIG_BIT));	// trigger
	tm_out = read_cpu_count() + ms; // msec
	while(!is_time_outs(tm_out)) {
		issue = read_p0(CQ_CTRL);
		if (!(issue & CQ_BUSY_BIT)){
			break;
		}
		WDOG_Feed();
	}
	if(issue & CQ_BUSY_BIT) {
		DEBUG_LOGI("[ERROR] CQ timeout!\n");
		cq_ptr_rst();
		write_p0(CQ_CTRL2, (read_p0(CQ_CTRL2) | CQ_RST_BIT));
		return EXIT_FAILURE;
	}
	write_p0(CQ_INT, (read_p0(CQ_INT) & 0x10));
	cq_ptr_rst();

    return EXIT_SUCCESS;
}

bit cq_issue(void)
{
	return cq_pause_issue(1000);	// 1 sec
}

void cq_wr_data(unsigned char val)
{
#if defined(_EX_MCU_VERSION)
	write_p0(CQ_P_PTR, cq_buf_ptr++);
	write_p0(CQ_DPORT, val);
#else
	CQ_BUFFER[cq_buf_ptr++] = val;
#endif
}

#if defined(_EX_MCU_VERSION)
void cq_wr_ptr_data(unsigned char ptr, unsigned char val)
{
	write_p0(CQ_P_PTR, ptr);
	write_p0(CQ_DPORT, val);	
}
#endif
