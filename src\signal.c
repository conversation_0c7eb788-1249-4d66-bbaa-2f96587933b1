
#include <string.h>
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <stdbool.h>

#include "derivative.h" /* include peripheral declarations */

#include "signal.h"


#define TASK_MAX   16


static uint32_t sys_signal;
static signal_fn_t signal_func_list[TASK_MAX];

static void dummy_cb_func(void) {}

void signal_post(uint8_t signal_num)
{
    sys_signal |= (1 << signal_num);
}

void signal_init(void)
{
    sys_signal = 0;
    uint8_t i = 0;
    while (i++ < TASK_MAX)
    {
        signal_func_list[i] = dummy_cb_func;
    }
}

void signal_bind(uint8_t signal_num, signal_fn_t fn)
{
    if (signal_num >= TASK_MAX)
        return;
    signal_func_list[signal_num] = fn;
}

void signal_unbind(uint8_t signal_num)
{
	if (signal_num >= TASK_MAX)
	        return;
	signal_func_list[signal_num] = dummy_cb_func;
}

void signal_process(void)
{
    uint32_t i, mask;

    if (sys_signal)
    {
        mask = 1;
        for (i = 0; i < TASK_MAX; i++)
        {
            if (sys_signal & mask)
            {
                signal_func_list[i]();
                sys_signal &= ~mask;
            }
            mask <<= 1;
        }
    }
}


event_handle_t  event_group_creat() 
{
    event_handle_t h =  (event_handle_t)malloc(sizeof(events));
    *h = 0;

    return h;
}


void event_group_post(event_handle_t h, events e)
{
    *h |= e;
}

events event_group_peek(event_handle_t h)
{
    return *h;
}

void event_group_clear(event_handle_t h, events e)
{
    *h &= ~e;
}

events event_group_wait(event_handle_t h, events e)
{
    events val =  *h & e;
    *h &= ~e;

    return val;
}

void event_group_delete(event_handle_t h)
{
    free(h);
}
