// MCU Script  File
#include "tp6823.h"
#include "load_tbl.h"
#include "mi2c.h"
#include "config.h"


#if (SENSOR_TYPE == SENSOR_TYPE_HIK_TYPE_1)
unsigned char
sB6_TP6816_MIPI_20240116[] = {
// $B6,6A,FF,FF,FF,00,00,FF
	0x6a, 0xff,
	0x6b, 0xff,
	0x6c, 0xff,
	0x6d, 0x00,
	0x6e, 0x00,
	0x6f, 0xff,
};
#define sB6_TP6816_MIPI_20240116_size	(sizeof(sB6_TP6816_MIPI_20240116))
#define load_sB6_TP6816_MIPI_20240116_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116, sB6_TP6816_MIPI_20240116_size)

// # Driving Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_1[] = {
// $B6,70,A6,00,00,00,80,70
	0x70, 0xa6,
	0x71, 0x00,
	0x72, 0x00,
	0x73, 0x00,
	0x74, 0x80,
	0x75, 0x70,
};
#define sB6_TP6816_MIPI_20240116_1_size	(sizeof(sB6_TP6816_MIPI_20240116_1))
#define load_sB6_TP6816_MIPI_20240116_1_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_1, sB6_TP6816_MIPI_20240116_1_size)

// # Pin Function Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_2[] = {
// $B6,77,17,C2,00,E5,00,00
	0x77, 0x17,
	0x78, 0xc2,
	0x79, 0x00,
	0x7a, 0xe5,
	0x7b, 0x00,
	0x7c, 0x00,
};
#define sB6_TP6816_MIPI_20240116_2_size	(sizeof(sB6_TP6816_MIPI_20240116_2))
#define load_sB6_TP6816_MIPI_20240116_2_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_2, sB6_TP6816_MIPI_20240116_2_size)

// # MIPI Rx
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_3[] = {
// $B6,20,47		# Soft-Reset PHY/CSI/FIFO
	0x20, 0x47,
// $B6,10,30,F1,55,55,55,00,08,00,0F
	0x10, 0x30,
	0x11, 0xf1,
	0x12, 0x55,
	0x13, 0x55,
	0x14, 0x55,
	0x15, 0x00,
	0x16, 0x08,
	0x17, 0x00,
	0x18, 0x0f,
// $B6,1D,FF,FF,98
	0x1d, 0xff,
	0x1e, 0xff,
	0x1f, 0x98,
// $B6,21,14,01,F0,10,3C,12
	0x21, 0x14,
	0x22, 0x01,
	0x23, 0xf0,
	0x24, 0x10,
	0x25, 0x3c,
	0x26, 0x12,
// $B6,2D,07,19,09,17,02
	0x2d, 0x07,
	0x2e, 0x19,
	0x2f, 0x09,
	0x30, 0x17,
	0x31, 0x02,
// $B6,20,40
	0x20, 0x40,
};
#define sB6_TP6816_MIPI_20240116_3_size	(sizeof(sB6_TP6816_MIPI_20240116_3))
#define load_sB6_TP6816_MIPI_20240116_3_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_3, sB6_TP6816_MIPI_20240116_3_size)

// # Main Path Digital Video Input
// Slave ID 0xb0 Register Table
unsigned char
sB0_TP6816_MIPI_20240116[] = {
// $B0,00,00		# VXi_Access_Sel = Main Path
	0x00, 0x00,
// $B0,01,0C,80,C7,80,A1,15,03,00,3F,32,86,41,A0,04,20,00,00,00,00,80,07,80,01,10,33,28,68,00,00,00
	0x01, 0x0c,
	0x02, 0x80,
	0x03, 0xc7,
	0x04, 0x80,
	0x05, 0xa1,
	0x06, 0x15,
	0x07, 0x03,
	0x08, 0x00,
	0x09, 0x3f,
	0x0a, 0x32,
	0x0b, 0x86,
	0x0c, 0x41,
	0x0d, 0xa0,
	0x0e, 0x04,
	0x0f, 0x20,
	0x10, 0x00,
	0x11, 0x00,
	0x12, 0x00,
	0x13, 0x00,
	0x14, 0x80,
	0x15, 0x07,
	0x16, 0x80,
	0x17, 0x01,
	0x18, 0x10,
	0x19, 0x33,
	0x1a, 0x28,
	0x1b, 0x68,
	0x1c, 0x00,
	0x1d, 0x00,
	0x1e, 0x00,
};
#define sB0_TP6816_MIPI_20240116_size	(sizeof(sB0_TP6816_MIPI_20240116))
#define load_sB0_TP6816_MIPI_20240116_tbl	load_tbl_p0( &sB0_TP6816_MIPI_20240116, sB0_TP6816_MIPI_20240116_size)

// # Scaling Ratio
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116[] = {
// $B2,02,00,00,01,00,00,01
	0x02, 0x00,
	0x03, 0x00,
	0x04, 0x01,
	0x05, 0x00,
	0x06, 0x00,
	0x07, 0x01,
// $B2,0C,40,00
	0x0c, 0x40,
	0x0d, 0x00,
};
#define sB2_TP6816_MIPI_20240116_size	(sizeof(sB2_TP6816_MIPI_20240116))
#define load_sB2_TP6816_MIPI_20240116_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116, sB2_TP6816_MIPI_20240116_size)

// # Pattern Gen.
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_1[] = {
// $B2,76,00,39,00,00,00
	0x76, 0x00,
	0x77, 0x39,
	0x78, 0x00,
	0x79, 0x00,
	0x7a, 0x00,
};
#define sB2_TP6816_MIPI_20240116_1_size	(sizeof(sB2_TP6816_MIPI_20240116_1))
#define load_sB2_TP6816_MIPI_20240116_1_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_1, sB2_TP6816_MIPI_20240116_1_size)

// # LineBuffer Timing
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_2[] = {
// $B2,8C,6D,29,05
	0x8c, 0x6d,
	0x8d, 0x29,
	0x8e, 0x05,
};
#define sB2_TP6816_MIPI_20240116_2_size	(sizeof(sB2_TP6816_MIPI_20240116_2))
#define load_sB2_TP6816_MIPI_20240116_2_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_2, sB2_TP6816_MIPI_20240116_2_size)

// # Panel Output Timing
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_3[] = {
// $B2,90,0A,00,20,00,80,07,F0,07,02,00,14,00,80,01,F7,01
	0x90, 0x0a,
	0x91, 0x00,
	0x92, 0x20,
	0x93, 0x00,
	0x94, 0x80,
	0x95, 0x07,
	0x96, 0xf0,
	0x97, 0x07,
	0x98, 0x02,
	0x99, 0x00,
	0x9a, 0x14,
	0x9b, 0x00,
	0x9c, 0x80,
	0x9d, 0x01,
	0x9e, 0xf7,
	0x9f, 0x01,
};
#define sB2_TP6816_MIPI_20240116_3_size	(sizeof(sB2_TP6816_MIPI_20240116_3))
#define load_sB2_TP6816_MIPI_20240116_3_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_3, sB2_TP6816_MIPI_20240116_3_size)

// # Panel Control
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_4[] = {
// $B2,A9,2C,00,00,00,00,28
	0xa9, 0x2c,
	0xaa, 0x00,
	0xab, 0x00,
	0xac, 0x00,
	0xad, 0x00,
	0xae, 0x28,
};
#define sB2_TP6816_MIPI_20240116_4_size	(sizeof(sB2_TP6816_MIPI_20240116_4))
#define load_sB2_TP6816_MIPI_20240116_4_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_4, sB2_TP6816_MIPI_20240116_4_size)

// # LVDS Tx1
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_5[] = {
// $B2,BB,01		# LvTx_Cfg_Sel = 01b: LvT1 only
	0xbb, 0x01,
// $B2,BC,B9,03,74,00
	0xbc, 0xb9,
	0xbd, 0x03,
	0xbe, 0x74,
	0xbf, 0x00,
};
#define sB2_TP6816_MIPI_20240116_5_size	(sizeof(sB2_TP6816_MIPI_20240116_5))
#define load_sB2_TP6816_MIPI_20240116_5_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_5, sB2_TP6816_MIPI_20240116_5_size)

// # DPLL
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_4[] = {
// $B6,90,41,07,02,88,46,40,66,10
	0x90, 0x41,
	0x91, 0x07,
	0x92, 0x02,
	0x93, 0x88,
	0x94, 0x46,
	0x95, 0x40,
	0x96, 0x66,
	0x97, 0x10,
};
#define sB6_TP6816_MIPI_20240116_4_size	(sizeof(sB6_TP6816_MIPI_20240116_4))
#define load_sB6_TP6816_MIPI_20240116_4_tbl	load_tbl_p3(&sB6_TP6816_MIPI_20240116_4, sB6_TP6816_MIPI_20240116_4_size)

// # Panel Type Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_5[] = {
// $B6,7D,01
	0x7d, 0x01,
};
#define sB6_TP6816_MIPI_20240116_5_size	(sizeof(sB6_TP6816_MIPI_20240116_5))
#define load_sB6_TP6816_MIPI_20240116_5_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_5, sB6_TP6816_MIPI_20240116_5_size)

// # Power Down
// Slave ID 0xb0 Register Table
unsigned char
sB0_TP6816_MIPI_20240116_1[] = {
// $B0,F6,B1
	0xf6, 0xb1,
};
#define sB0_TP6816_MIPI_20240116_1_size	(sizeof(sB0_TP6816_MIPI_20240116_1))
#define load_sB0_TP6816_MIPI_20240116_1_tbl	load_tbl_p0( &sB0_TP6816_MIPI_20240116_1, sB0_TP6816_MIPI_20240116_1_size)

// # Reset TXLVDS PLL
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_6[] = {
// $B2,BB,01,B9		# set LvT1_pll_rst
	0xbb, 0x01,
	0xbc, 0xb9,
};
#define sB2_TP6816_MIPI_20240116_6_size	(sizeof(sB2_TP6816_MIPI_20240116_6))
#define load_sB2_TP6816_MIPI_20240116_6_tbl	load_tbl_p1(&sB2_TP6816_MIPI_20240116_6, sB2_TP6816_MIPI_20240116_6_size)

#elif (SENSOR_TYPE == SENSOR_TYPE_HIK_TYPE_2)
// # Pull Enable
// Scaler Page3 Register Table
unsigned char 
sBE_TP6816_MIPI_20240311[] = {
// $BE,60,FC,FF,00,FF,F0,FF,3F,00,E6
	0x60, 0xfc,
	0x61, 0xff,
	0x62, 0x00,
	0x63, 0xff,
	0x64, 0xf0,
	0x65, 0xff,
	0x66, 0x3f,
	0x67, 0x00,
	0x68, 0xe6,
};
#define sBE_TP6816_MIPI_20240311_size	(sizeof(sBE_TP6816_MIPI_20240311))
#define load_sBE_TP6816_MIPI_20240311_tbl	load_tbl_p3(sBE_TP6816_MIPI_20240311, sBE_TP6816_MIPI_20240311_size)
// # Input Enable
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116[] = {
// $B6,6A,FF,FF,FF,00,00,FF
	0x6a, 0xff,
	0x6b, 0xff,
	0x6c, 0xff,
	0x6d, 0x00,
	0x6e, 0x00,
	0x6f, 0xff,
};
#define sB6_TP6816_MIPI_20240116_size	(sizeof(sB6_TP6816_MIPI_20240116))
#define load_sB6_TP6816_MIPI_20240116_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116, sB6_TP6816_MIPI_20240116_size)

// # Driving Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_1[] = {
// $B6,70,A6,00,00,00,80,70
	0x70, 0xa6,
	0x71, 0x00,
	0x72, 0x00,
	0x73, 0x00,
	0x74, 0x80,
	0x75, 0x70,
};
#define sB6_TP6816_MIPI_20240116_1_size	(sizeof(sB6_TP6816_MIPI_20240116_1))
#define load_sB6_TP6816_MIPI_20240116_1_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_1, sB6_TP6816_MIPI_20240116_1_size)

// # Pin Function Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_2[] = {
// $B6,77,17,C2,00,E5,00,00
	0x77, 0x17,
	0x78, 0xc2,
	0x79, 0x00,
	0x7a, 0xe5,
	0x7b, 0x00,
	0x7c, 0x00,
};
#define sB6_TP6816_MIPI_20240116_2_size	(sizeof(sB6_TP6816_MIPI_20240116_2))
#define load_sB6_TP6816_MIPI_20240116_2_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_2, sB6_TP6816_MIPI_20240116_2_size)

// # MIPI Rx
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_3[] = {
	// $B6,20,07		# Soft-Reset PHY/CSI/FIFO
		0x20, 0x07,
	// $B6,11,F1,55,55,55,00,08,80,0F,00,F0,00,40
		0x11, 0xf1,
		0x12, 0x55,
		0x13, 0x55,
		0x14, 0x55,
		0x15, 0x00,
		0x16, 0x08,
		0x17, 0x00,
		0x18, 0x0f,
		0x19, 0x02,
		0x1A, 0xF0,
		0x1B, 0x00,
		0x1C, 0x40,
	// $B6,1D,FF,FF,98
		0x1d, 0xff,
		0x1e, 0xff,
		0x1f, 0x98,
	// $B6,21,14,01,00,f0,98,08
		0x21, 0x14,
		0x22, 0x01,
		0x23, 0x00,
		0x24, 0xf0,
		0x25, 0x98,
		0x26, 0x08,
	// $B6,2D,07,19,09,65,04
		0x2d, 0x07,
		0x2e, 0x19,
		0x2f, 0x09,
		0x30, 0x65,
		0x31, 0x04,
	// $B6,10,30
		0x10, 0x30,
	// $B6,20,40
		0x20, 0x40,
};
#define sB6_TP6816_MIPI_20240116_3_size	(sizeof(sB6_TP6816_MIPI_20240116_3))
#define load_sB6_TP6816_MIPI_20240116_3_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_3, sB6_TP6816_MIPI_20240116_3_size)

// # Main Path Digital Video Input
// Slave ID 0xb0 Register Table
unsigned char
sB0_TP6816_MIPI_20240116[] = {
// $B0,00,00		# VXi_Access_Sel = Main Path
	0x00, 0x00,
// $B0,01,0C,80,C7,80,A1,15,03,00,3F,32,86,41,A0,04,20,00,00,00,00,80,07,80,01,10,33,28,68,00,00,00
	0x01, 0x0c,
	0x02, 0x80,
	0x03, 0xc7,
	0x04, 0x80,
	0x05, 0xa1,
	0x06, 0x15,
	0x07, 0x03,
	0x08, 0x00,
	0x09, 0x3f,
	0x0a, 0x32,
	0x0b, 0x86,
	0x0c, 0x41,
	0x0d, 0xa0,
	0x0e, 0x04,
	0x0f, 0x20,
	0x10, 0x00,
	0x11, 0x00,
	0x12, 0x00,
	0x13, 0x00,
	0x14, 0x80,
	0x15, 0x07,
	0x16, 0x80,
	0x17, 0x01,
	0x18, 0x10,
	0x19, 0x33,
	0x1a, 0x28,
	0x1b, 0x60,
	0x1c, 0x00,
	0x1d, 0x00,
	0x1e, 0x80,
};
#define sB0_TP6816_MIPI_20240116_size	(sizeof(sB0_TP6816_MIPI_20240116))
#define load_sB0_TP6816_MIPI_20240116_tbl	load_tbl_p0( &sB0_TP6816_MIPI_20240116, sB0_TP6816_MIPI_20240116_size)

// # Scaling Ratio
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116[] = {
// $B2,02,00,00,01,00,00,01
	0x02, 0x00,
	0x03, 0x00,
	0x04, 0x01,
	0x05, 0x00,
	0x06, 0x00,
	0x07, 0x01,
// $BA,0C,40,00
	0x0c, 0x40,
	0x0d, 0x00,
};
#define sB2_TP6816_MIPI_20240116_size	(sizeof(sB2_TP6816_MIPI_20240116))
#define load_sB2_TP6816_MIPI_20240116_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116, sB2_TP6816_MIPI_20240116_size)

// # Pattern Gen.
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_1[] = {
// $B2,76,00,39,00,00,00
	0x76, 0x00,
	0x77, 0x39,
	0x78, 0x00,
	0x79, 0x00,
	0x7a, 0x00,
};
#define sB2_TP6816_MIPI_20240116_1_size	(sizeof(sB2_TP6816_MIPI_20240116_1))
#define load_sB2_TP6816_MIPI_20240116_1_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_1, sB2_TP6816_MIPI_20240116_1_size)

// # LineBuffer Timing
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_2[] = {
// $BA,8C,BD,F7,04
	0x8c, 0xbd,
	0x8d, 0xf7,
	0x8e, 0x04,
};
#define sB2_TP6816_MIPI_20240116_2_size	(sizeof(sB2_TP6816_MIPI_20240116_2))
#define load_sB2_TP6816_MIPI_20240116_2_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_2, sB2_TP6816_MIPI_20240116_2_size)

// # Panel Output Timing
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_3[] = {
// $B2,90,0A,00,20,00,80,07,F0,07,02,00,14,00,80,01,F7,01
	0x90, 0x0a,
	0x91, 0x00,
	0x92, 0x20,
	0x93, 0x00,
	0x94, 0x80,
	0x95, 0x07,
	0x96, 0x7e,
	0x97, 0x08,
	0x98, 0x02,
	0x99, 0x00,
	0x9a, 0x14,
	0x9b, 0x00,
	0x9c, 0x80,
	0x9d, 0x01,
	0x9e, 0xe4,
	0x9f, 0x01,
};
#define sB2_TP6816_MIPI_20240116_3_size	(sizeof(sB2_TP6816_MIPI_20240116_3))
#define load_sB2_TP6816_MIPI_20240116_3_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_3, sB2_TP6816_MIPI_20240116_3_size)

// # Panel Control
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_4[] = {
// $B2,A9,2C,00,00,00,00,28
	0xa9, 0x0c,
	0xaa, 0x00,
	0xab, 0x00,
	0xac, 0x00,
	0xad, 0x00,
	0xae, 0x28,
};
#define sB2_TP6816_MIPI_20240116_4_size	(sizeof(sB2_TP6816_MIPI_20240116_4))
#define load_sB2_TP6816_MIPI_20240116_4_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_4, sB2_TP6816_MIPI_20240116_4_size)

// # LVDS Tx1
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_5[] = {
// $B2,BB,01		# LvTx_Cfg_Sel = 01b: LvT1 only
	0xbb, 0x01,
// $B2,BC,B9,03,74,00
	0xbc, 0xb9,
	0xbd, 0x03,
	0xbe, 0x74,
	0xbf, 0x00,
};
#define sB2_TP6816_MIPI_20240116_5_size	(sizeof(sB2_TP6816_MIPI_20240116_5))
#define load_sB2_TP6816_MIPI_20240116_5_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_5, sB2_TP6816_MIPI_20240116_5_size)

// # DPLL
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_4[] = {
// $B6,90,41,07,02,88,46,40,66,10
	0x90, 0x63,
	0x91, 0x0b,
	0x92, 0x02,
	0x93, 0x88,
	0x94, 0x46,
	0x95, 0x40,
	0x96, 0x66,
	0x97, 0x10,
};
#define sB6_TP6816_MIPI_20240116_4_size	(sizeof(sB6_TP6816_MIPI_20240116_4))
#define load_sB6_TP6816_MIPI_20240116_4_tbl	load_tbl_p3(&sB6_TP6816_MIPI_20240116_4, sB6_TP6816_MIPI_20240116_4_size)

// # Panel Type Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_5[] = {
// $B6,7D,01
	0x7d, 0x01,
};
#define sB6_TP6816_MIPI_20240116_5_size	(sizeof(sB6_TP6816_MIPI_20240116_5))
#define load_sB6_TP6816_MIPI_20240116_5_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_5, sB6_TP6816_MIPI_20240116_5_size)

// # Power Down
// Slave ID 0xb0 Register Table
unsigned char
sB0_TP6816_MIPI_20240116_1[] = {
// $B0,F6,B1
	0xf6, 0xb1,
};
#define sB0_TP6816_MIPI_20240116_1_size	(sizeof(sB0_TP6816_MIPI_20240116_1))
#define load_sB0_TP6816_MIPI_20240116_1_tbl	load_tbl_p0( &sB0_TP6816_MIPI_20240116_1, sB0_TP6816_MIPI_20240116_1_size)

// # Reset TXLVDS PLL
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_6[] = {
// $B2,BB,01,B9		# set LvT1_pll_rst
	0xbb, 0x01,
	0xbc, 0xb9,
};
#define sB2_TP6816_MIPI_20240116_6_size	(sizeof(sB2_TP6816_MIPI_20240116_6))
#define load_sB2_TP6816_MIPI_20240116_6_tbl	load_tbl_p1(&sB2_TP6816_MIPI_20240116_6, sB2_TP6816_MIPI_20240116_6_size)
#elif (SENSOR_TYPE == SENSOR_TYPE_DH_TYPE_1)
unsigned char
sBE_TP6816_MIPI_20240311[] = {
// $BE,60,FC,FF,00,FF,F0,FF,3F,00,E6
	0x60, 0xfc,
	0x61, 0xff,
	0x62, 0x00,
	0x63, 0xff,
	0x64, 0xf0,
	0x65, 0xff,
	0x66, 0x3f,
	0x67, 0x00,
	0x68, 0xe6,
};
#define sBE_TP6816_MIPI_20240311_size	(sizeof(sBE_TP6816_MIPI_20240311))
#define load_sBE_TP6816_MIPI_20240311_tbl	load_tbl_p3(sBE_TP6816_MIPI_20240311, sBE_TP6816_MIPI_20240311_size)
// # Input Enable
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116[] = {
// $B6,6A,FF,FF,FF,00,00,FF
	0x6a, 0xff,
	0x6b, 0xff,
	0x6c, 0xff,
	0x6d, 0x00,
	0x6e, 0x00,
	0x6f, 0xff,
};
#define sB6_TP6816_MIPI_20240116_size	(sizeof(sB6_TP6816_MIPI_20240116))
#define load_sB6_TP6816_MIPI_20240116_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116, sB6_TP6816_MIPI_20240116_size)

// # Driving Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_1[] = {
// $B6,70,A6,00,00,00,80,70
	0x70, 0xa6,
	0x71, 0x00,
	0x72, 0x00,
	0x73, 0x00,
	0x74, 0x80,
	0x75, 0x70,
};
#define sB6_TP6816_MIPI_20240116_1_size	(sizeof(sB6_TP6816_MIPI_20240116_1))
#define load_sB6_TP6816_MIPI_20240116_1_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_1, sB6_TP6816_MIPI_20240116_1_size)

// # Pin Function Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_2[] = {
// $B6,77,17,C2,00,E5,00,00
	0x77, 0x17,
	0x78, 0xc2,
	0x79, 0x00,
	0x7a, 0xe5,
	0x7b, 0x00,
	0x7c, 0x00,
};
#define sB6_TP6816_MIPI_20240116_2_size	(sizeof(sB6_TP6816_MIPI_20240116_2))
#define load_sB6_TP6816_MIPI_20240116_2_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_2, sB6_TP6816_MIPI_20240116_2_size)

// # MIPI Rx
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_3[] = {
// $B6,20,47		# Soft-Reset PHY/CSI/FIFO
	0x20, 0x47,
// $B6,10,30,F1,55,55,55,00,08,00,0F
	0x10, 0x30,
	0x11, 0xf1,
	0x12, 0x55,
	0x13, 0x55,
	0x14, 0x55,
	0x15, 0x00,
	0x16, 0x08,
	0x17, 0x00,
	0x18, 0x0f,
// $B6,1D,FF,FF,98
	0x1d, 0xff,
	0x1e, 0xff,
	0x1f, 0x98,
// $B6,21,14,01,F0,10,3C,12
	0x21, 0x14,
	0x22, 0x01,
	0x23, 0xf0,
	0x24, 0x10,
	0x25, 0x3c,
	0x26, 0x12,
// $B6,2D,07,19,09,17,02
	0x2d, 0x07,
	0x2e, 0x19,
	0x2f, 0x09,
	0x30, 0x17,
	0x31, 0x02,
// $B6,20,40
	0x20, 0x40,
};
#define sB6_TP6816_MIPI_20240116_3_size	(sizeof(sB6_TP6816_MIPI_20240116_3))
#define load_sB6_TP6816_MIPI_20240116_3_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_3, sB6_TP6816_MIPI_20240116_3_size)

// # Main Path Digital Video Input
// Slave ID 0xb0 Register Table
unsigned char
sB0_TP6816_MIPI_20240116[] = {
// $B0,00,00		# VXi_Access_Sel = Main Path
	0x00, 0x00,
// $B0,01,0C,80,C7,80,A1,15,03,00,3F,32,86,41,A0,04,20,00,00,00,00,80,07,80,01,10,33,28,68,00,00,00
	0x01, 0x0c,
	0x02, 0x80,
	0x03, 0xc7,
	0x04, 0x80,
	0x05, 0xa1,
	0x06, 0x15,
	0x07, 0x03,
	0x08, 0x00,
	0x09, 0x3f,
	0x0a, 0x32,
	0x0b, 0x86,
	0x0c, 0x31,
	0x0d, 0xa8,
	0x0e, 0x04,
	0x0f, 0x20,
	0x10, 0x00,
	0x11, 0x00,
	0x12, 0x00,
	0x13, 0x00,
	0x14, 0x80,
	0x15, 0x07,
	0x16, 0x80,
	0x17, 0x01,
	0x18, 0x10,
	0x19, 0x33,
	0x1a, 0x28,
	0x1b, 0x60,
	0x1c, 0x00,
	0x1d, 0x00,
	0x1e, 0x80,
};
#define sB0_TP6816_MIPI_20240116_size	(sizeof(sB0_TP6816_MIPI_20240116))
#define load_sB0_TP6816_MIPI_20240116_tbl	load_tbl_p0( &sB0_TP6816_MIPI_20240116, sB0_TP6816_MIPI_20240116_size)

// # Scaling Ratio
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116[] = {
// $B2,02,00,00,01,00,00,01
	0x02, 0x00,
	0x03, 0x00,
	0x04, 0x01,
	0x05, 0x00,
	0x06, 0x00,
	0x07, 0x01,
// $BA,0C,40,00
	0x0c, 0x40,
	0x0d, 0x00,
};
#define sB2_TP6816_MIPI_20240116_size	(sizeof(sB2_TP6816_MIPI_20240116))
#define load_sB2_TP6816_MIPI_20240116_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116, sB2_TP6816_MIPI_20240116_size)

// # Pattern Gen.
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_1[] = {
// $B2,76,00,39,00,00,00
	0x76, 0x00,
	0x77, 0x39,
	0x78, 0x00,
	0x79, 0x00,
	0x7a, 0x00,
};
#define sB2_TP6816_MIPI_20240116_1_size	(sizeof(sB2_TP6816_MIPI_20240116_1))
#define load_sB2_TP6816_MIPI_20240116_1_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_1, sB2_TP6816_MIPI_20240116_1_size)

// # LineBuffer Timing
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_2[] = {
// $BA,8C,BD,F7,04
	0x8c, 0x73,
	0x8d, 0xb4,
	0x8e, 0x04,
};
#define sB2_TP6816_MIPI_20240116_2_size	(sizeof(sB2_TP6816_MIPI_20240116_2))
#define load_sB2_TP6816_MIPI_20240116_2_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_2, sB2_TP6816_MIPI_20240116_2_size)

// # Panel Output Timing
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_3[] = {
// $B2,90,0A,00,20,00,80,07,F0,07,02,00,14,00,80,01,F7,01
	0x90, 0x0a,
	0x91, 0x00,
	0x92, 0x20,
	0x93, 0x00,
	0x94, 0x80,
	0x95, 0x07,
	0x96, 0x51,
	0x97, 0x09,
	0x98, 0x02,
	0x99, 0x00,
	0x9a, 0x14,
	0x9b, 0x00,
	0x9c, 0x80,
	0x9d, 0x01,
	0x9e, 0xc0,
	0x9f, 0x01,
};
#define sB2_TP6816_MIPI_20240116_3_size	(sizeof(sB2_TP6816_MIPI_20240116_3))
#define load_sB2_TP6816_MIPI_20240116_3_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_3, sB2_TP6816_MIPI_20240116_3_size)

// # Panel Control
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_4[] = {
// $B2,A9,2C,00,00,00,00,28
	0xa9, 0x0c,
	0xaa, 0x00,
	0xab, 0x00,
	0xac, 0x00,
	0xad, 0x00,
	0xae, 0x28,
};
#define sB2_TP6816_MIPI_20240116_4_size	(sizeof(sB2_TP6816_MIPI_20240116_4))
#define load_sB2_TP6816_MIPI_20240116_4_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_4, sB2_TP6816_MIPI_20240116_4_size)

// # LVDS Tx1
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_5[] = {
// $B2,BB,01		# LvTx_Cfg_Sel = 01b: LvT1 only
	0xbb, 0x01,
// $B2,BC,B9,03,74,00
	0xbc, 0xb9,
	0xbd, 0x03,
	0xbe, 0x74,
	0xbf, 0x00,
};
#define sB2_TP6816_MIPI_20240116_5_size	(sizeof(sB2_TP6816_MIPI_20240116_5))
#define load_sB2_TP6816_MIPI_20240116_5_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_5, sB2_TP6816_MIPI_20240116_5_size)

// # DPLL
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_4[] = {
// $B6,90,41,07,02,88,46,40,66,10
	0x90, 0xc9,
	0x91, 0x11,
	0x92, 0x02,
	0x93, 0x88,
	0x94, 0x46,
	0x95, 0x40,
	0x96, 0x66,
	0x97, 0x10,
};
#define sB6_TP6816_MIPI_20240116_4_size	(sizeof(sB6_TP6816_MIPI_20240116_4))
#define load_sB6_TP6816_MIPI_20240116_4_tbl	load_tbl_p3(&sB6_TP6816_MIPI_20240116_4, sB6_TP6816_MIPI_20240116_4_size)

// # Panel Type Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_5[] = {
// $B6,7D,01
	0x7d, 0x01,
};
#define sB6_TP6816_MIPI_20240116_5_size	(sizeof(sB6_TP6816_MIPI_20240116_5))
#define load_sB6_TP6816_MIPI_20240116_5_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_5, sB6_TP6816_MIPI_20240116_5_size)

// # Power Down
// Slave ID 0xb0 Register Table
unsigned char
sB0_TP6816_MIPI_20240116_1[] = {
// $B0,F6,B1
	0xf6, 0xb1,
};
#define sB0_TP6816_MIPI_20240116_1_size	(sizeof(sB0_TP6816_MIPI_20240116_1))
#define load_sB0_TP6816_MIPI_20240116_1_tbl	load_tbl_p0( &sB0_TP6816_MIPI_20240116_1, sB0_TP6816_MIPI_20240116_1_size)

// # Reset TXLVDS PLL
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_6[] = {
// $B2,BB,01,B9		# set LvT1_pll_rst
	0xbb, 0x01,
	0xbc, 0xb9,
};
#define sB2_TP6816_MIPI_20240116_6_size	(sizeof(sB2_TP6816_MIPI_20240116_6))
#define load_sB2_TP6816_MIPI_20240116_6_tbl	load_tbl_p1(&sB2_TP6816_MIPI_20240116_6, sB2_TP6816_MIPI_20240116_6_size)

#elif (SENSOR_TYPE == SENSOR_TYPE_HIK_TYPE_3)
// # Pull Enable
// Scaler Page3 Register Table
unsigned char 
sB6_TP6816_MIPI_20250414[] = {
// $B6,00,2A,00,22,88,48,00
	0x00, 0x2a,
	0x01, 0x00,
	0x02, 0x22,
	0x03, 0x88,
	0x04, 0x48,
	0x05, 0x00,
};
#define sB6_TP6816_MIPI_20250414_size	(sizeof(sB6_TP6816_MIPI_20250414))
#define load_sB6_TP6816_MIPI_20250414_tbl	load_tbl_p3(sB6_TP6816_MIPI_20250414, sB6_TP6816_MIPI_20250414_size)
unsigned char
sBE_TP6816_MIPI_20240311[] = {
// $BE,60,FC,FF,00,FF,F0,FF,3F,00,E6
	0x60, 0xfc,
	0x61, 0xff,
	0x62, 0x00,
	0x63, 0xff,
	0x64, 0xf0,
	0x65, 0xff,
	0x66, 0x3f,
	0x67, 0x00,
	0x68, 0xe6,
};
#define sBE_TP6816_MIPI_20240311_size	(sizeof(sBE_TP6816_MIPI_20240311))
#define load_sBE_TP6816_MIPI_20240311_tbl	load_tbl_p3(sBE_TP6816_MIPI_20240311, sBE_TP6816_MIPI_20240311_size)
// # Input Enable
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116[] = {
// $B6,6A,FF,FF,FF,00,00,FF
	0x6a, 0xff,
	0x6b, 0xff,
	0x6c, 0xff,
	0x6d, 0x00,
	0x6e, 0x00,
	0x6f, 0xff,
};
#define sB6_TP6816_MIPI_20240116_size	(sizeof(sB6_TP6816_MIPI_20240116))
#define load_sB6_TP6816_MIPI_20240116_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116, sB6_TP6816_MIPI_20240116_size)

// # Driving Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_1[] = {
// $B6,70,A6,00,00,00,80,70
	0x70, 0xa6,
	0x71, 0x00,
	0x72, 0x00,
	0x73, 0x00,
	0x74, 0x80,
	0x75, 0x70,
};
#define sB6_TP6816_MIPI_20240116_1_size	(sizeof(sB6_TP6816_MIPI_20240116_1))
#define load_sB6_TP6816_MIPI_20240116_1_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_1, sB6_TP6816_MIPI_20240116_1_size)

// # Pin Function Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_2[] = {
// $B6,77,17,C2,00,E5,00,00
	0x77, 0x17,
	0x78, 0xc2,
	0x79, 0x00,
	0x7a, 0xe5,
	0x7b, 0x00,
	0x7c, 0x00,
};
#define sB6_TP6816_MIPI_20240116_2_size	(sizeof(sB6_TP6816_MIPI_20240116_2))
#define load_sB6_TP6816_MIPI_20240116_2_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_2, sB6_TP6816_MIPI_20240116_2_size)

// # MIPI Rx
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_3[] = {
// $BE,20,47		# Soft-Reset PHY/CSI/FIFO
	0x20, 0x47,
// $BE,10,30,F1,55,55,55,00,08,00,0F
	0x10, 0x30,
	0x11, 0xf1,
	0x12, 0x55,
	0x13, 0x55,
	0x14, 0x55,
	0x15, 0x00,
	0x16, 0x08,
	0x17, 0x00,
	0x18, 0x0f,
// $BE,1D,FF,FF,98
	0x1d, 0xff,
	0x1e, 0xff,
	0x1f, 0x98,
// $BE,21,14,01,00,F0,98,08
	0x21, 0x14,
	0x22, 0x01,
	0x23, 0x00,
	0x24, 0xf0,
	0x25, 0x98,
	0x26, 0x08,
// $BE,2D,07,19,09,65,04
	0x2d, 0x07,
	0x2e, 0x19,
	0x2f, 0x09,
	0x30, 0x65,
	0x31, 0x04,
// $BE,20,40
	0x20, 0x40,
};
#define sB6_TP6816_MIPI_20240116_3_size	(sizeof(sB6_TP6816_MIPI_20240116_3))
#define load_sB6_TP6816_MIPI_20240116_3_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_3, sB6_TP6816_MIPI_20240116_3_size)

// # Main Path Digital Video Input
// Slave ID 0xb0 Register Table
unsigned char
sB0_TP6816_MIPI_20240116[] = {
// $B0,00,00		# VXi_Access_Sel = Main Path
	0x00, 0x00,
// $B8,01,0C,80,37,80,01,20,03,00,0A,30,86,41,A8,04,20,00,00,00,00,80,07,80,01,10,33,28,6C,00,00,80
	0x01, 0x0c,
	0x02, 0x80,
	0x03, 0x37,
	0x04, 0x80,
	0x05, 0x01,
	0x06, 0x20,
	0x07, 0x03,
	0x08, 0x00,
	0x09, 0x0a,
	0x0a, 0x30,
	0x0b, 0x86,
	0x0c, 0x41,
	0x0d, 0xa0,
	0x0e, 0x04,
	0x0f, 0x20,
	0x10, 0x00,
	0x11, 0x00,
	0x12, 0x00,
	0x13, 0x00,
	0x14, 0x80,
	0x15, 0x07,
	0x16, 0x80,
	0x17, 0x01,
	0x18, 0x10,
	0x19, 0x33,
	0x1a, 0x28,
	0x1b, 0x60,
	0x1c, 0x00,
	0x1d, 0x00,
	0x1e, 0x80,
};
#define sB0_TP6816_MIPI_20240116_size	(sizeof(sB0_TP6816_MIPI_20240116))
#define load_sB0_TP6816_MIPI_20240116_tbl	load_tbl_p0( &sB0_TP6816_MIPI_20240116, sB0_TP6816_MIPI_20240116_size)

// # Scaling Ratio
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116[] = {
// $B2,02,00,00,01,00,00,01
	0x02, 0x00,
	0x03, 0x00,
	0x04, 0x01,
	0x05, 0x00,
	0x06, 0x00,
	0x07, 0x01,
// $BA,0C,00,00
	0x0c, 0x00,
	0x0d, 0x00,
};
#define sB2_TP6816_MIPI_20240116_size	(sizeof(sB2_TP6816_MIPI_20240116))
#define load_sB2_TP6816_MIPI_20240116_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116, sB2_TP6816_MIPI_20240116_size)

// # Pattern Gen.
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_1[] = {
// $B2,76,00,39,00,00,00
	0x76, 0x00,
	0x77, 0x09,
	0x78, 0x05,
	0x79, 0x05,
	0x7a, 0x05,
};
#define sB2_TP6816_MIPI_20240116_1_size	(sizeof(sB2_TP6816_MIPI_20240116_1))
#define load_sB2_TP6816_MIPI_20240116_1_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_1, sB2_TP6816_MIPI_20240116_1_size)

// # LineBuffer Timing
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_2[] = {
// $B2,8C,B7,DA,07
	0x8c, 0xfa,
	0x8d, 0xd8,
	0x8e, 0x07,
};
#define sB2_TP6816_MIPI_20240116_2_size	(sizeof(sB2_TP6816_MIPI_20240116_2))
#define load_sB2_TP6816_MIPI_20240116_2_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_2, sB2_TP6816_MIPI_20240116_2_size)

// # Panel Output Timing
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_3[] = {
// $B2,90,0A,00,20,00,80,07,F0,07,02,00,14,00,80,01,F7,01
	0x90, 0x0a,
	0x91, 0x00,
	0x92, 0x20,
	0x93, 0x00,
	0x94, 0x80,
	0x95, 0x07,
	0x96, 0x7e,
	0x97, 0x08,
	0x98, 0x02,
	0x99, 0x00,
	0x9a, 0x14,
	0x9b, 0x00,
	0x9c, 0x80,
	0x9d, 0x01,
	0x9e, 0xe4,
	0x9f, 0x01,
};
#define sB2_TP6816_MIPI_20240116_3_size	(sizeof(sB2_TP6816_MIPI_20240116_3))
#define load_sB2_TP6816_MIPI_20240116_3_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_3, sB2_TP6816_MIPI_20240116_3_size)

// # Panel Control
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_4[] = {
// $B2,A9,2C,00,00,00,00,28
	0xa9, 0x0c,
	0xaa, 0x00,
	0xab, 0x00,
	0xac, 0x00,
	0xad, 0x00,
	0xae, 0x28,
};
#define sB2_TP6816_MIPI_20240116_4_size	(sizeof(sB2_TP6816_MIPI_20240116_4))
#define load_sB2_TP6816_MIPI_20240116_4_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_4, sB2_TP6816_MIPI_20240116_4_size)

// # LVDS Tx1
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_5[] = {
// $B2,BB,01		# LvTx_Cfg_Sel = 01b: LvT1 only
	0xbb, 0x01,
// $B2,BC,89,03,74,00
	0xbc, 0x89,
	0xbd, 0x03,
	0xbe, 0x74,
	0xbf, 0x00,
};
#define sB2_TP6816_MIPI_20240116_5_size	(sizeof(sB2_TP6816_MIPI_20240116_5))
#define load_sB2_TP6816_MIPI_20240116_5_tbl	load_tbl_p1( &sB2_TP6816_MIPI_20240116_5, sB2_TP6816_MIPI_20240116_5_size)

// # DPLL
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_4[] = {
// $B6,90,41,07,02,88,46,40,66,10
	0x90, 0x63,
	0x91, 0x0b,
	0x92, 0x02,
	0x93, 0x88,
	0x94, 0x46,
	0x95, 0x40,
	0x96, 0x66,
	0x97, 0x10,
};
#define sB6_TP6816_MIPI_20240116_4_size	(sizeof(sB6_TP6816_MIPI_20240116_4))
#define load_sB6_TP6816_MIPI_20240116_4_tbl	load_tbl_p3(&sB6_TP6816_MIPI_20240116_4, sB6_TP6816_MIPI_20240116_4_size)

// # Panel Type Select
// Slave ID 0xb6 Register Table
unsigned char
sB6_TP6816_MIPI_20240116_5[] = {
// $B6,7D,01
	0x7d, 0x01,
};
#define sB6_TP6816_MIPI_20240116_5_size	(sizeof(sB6_TP6816_MIPI_20240116_5))
#define load_sB6_TP6816_MIPI_20240116_5_tbl	load_tbl_p3( &sB6_TP6816_MIPI_20240116_5, sB6_TP6816_MIPI_20240116_5_size)

// # Power Down
// Slave ID 0xb0 Register Table
unsigned char
sB0_TP6816_MIPI_20240116_1[] = {
// $B0,F6,B1
	0xf6, 0x91,
};
#define sB0_TP6816_MIPI_20240116_1_size	(sizeof(sB0_TP6816_MIPI_20240116_1))
#define load_sB0_TP6816_MIPI_20240116_1_tbl	load_tbl_p0( &sB0_TP6816_MIPI_20240116_1, sB0_TP6816_MIPI_20240116_1_size)

// # Reset TXLVDS PLL
// Slave ID 0xb2 Register Table
unsigned char
sB2_TP6816_MIPI_20240116_6[] = {
// $B2,BB,01,B9		# set LvT1_pll_rst
	0xbb, 0x01,
	0xbc, 0x89,
};
#define sB2_TP6816_MIPI_20240116_6_size	(sizeof(sB2_TP6816_MIPI_20240116_6))
#define load_sB2_TP6816_MIPI_20240116_6_tbl	load_tbl_p1(&sB2_TP6816_MIPI_20240116_6, sB2_TP6816_MIPI_20240116_6_size)
#endif
#if (SENSOR_TYPE == SENSOR_TYPE_HIK_TYPE_3)
void load_TP6816_MIPI_20240116(void)
{
	load_sB6_TP6816_MIPI_20250414_tbl;
	load_sBE_TP6816_MIPI_20240311_tbl;
	load_sB6_TP6816_MIPI_20240116_tbl;
	load_sB6_TP6816_MIPI_20240116_1_tbl;
	load_sB6_TP6816_MIPI_20240116_2_tbl;
	load_sB6_TP6816_MIPI_20240116_3_tbl;
	load_sB0_TP6816_MIPI_20240116_tbl;
	load_sB2_TP6816_MIPI_20240116_tbl;
	load_sB2_TP6816_MIPI_20240116_1_tbl;
	load_sB2_TP6816_MIPI_20240116_2_tbl;
	load_sB2_TP6816_MIPI_20240116_3_tbl;
	load_sB2_TP6816_MIPI_20240116_4_tbl;
	load_sB2_TP6816_MIPI_20240116_5_tbl;
	load_sB6_TP6816_MIPI_20240116_4_tbl;
	load_sB6_TP6816_MIPI_20240116_5_tbl;
	load_sB0_TP6816_MIPI_20240116_1_tbl;
	load_sB2_TP6816_MIPI_20240116_6_tbl;
}
#else
void load_TP6816_MIPI_20240116(void)
{
	//load_sBE_TP6816_MIPI_20240311_tbl;
	load_sB6_TP6816_MIPI_20240116_tbl;
	load_sB6_TP6816_MIPI_20240116_1_tbl;
	load_sB6_TP6816_MIPI_20240116_2_tbl;
	load_sB6_TP6816_MIPI_20240116_3_tbl;
	load_sB0_TP6816_MIPI_20240116_tbl;
	load_sB2_TP6816_MIPI_20240116_tbl;
	//load_sB2_TP6816_MIPI_20240116_1_tbl;
	load_sB2_TP6816_MIPI_20240116_2_tbl;
	load_sB2_TP6816_MIPI_20240116_3_tbl;
	load_sB2_TP6816_MIPI_20240116_4_tbl;
	load_sB2_TP6816_MIPI_20240116_5_tbl;
	load_sB6_TP6816_MIPI_20240116_4_tbl;
	load_sB6_TP6816_MIPI_20240116_5_tbl;
	load_sB0_TP6816_MIPI_20240116_1_tbl;
	load_sB2_TP6816_MIPI_20240116_6_tbl;
}
#endif
void reset_TP6816_MIPI(void)
{
	write_p3(0x20,0x02);
	KEA_Delay(10);
	write_p3(0x20,0x40);
}
void reset_tp6815_rx(void)
{
	load_sB6_TP6816_MIPI_20240116_3_tbl;
}
void rest_tp6815_rx_lock(void)
{
	load_sB2_TP6816_MIPI_20240116_4_tbl;
}
