#include "ec.h"

#include "config.h"

extern uint8_t frozen_data[256];
extern uint8_t adc_front;
extern uint8_t adc_back;

#define GLS_DATA_LENGTH 256

SensorData_t sensor_data={0};
StateData_t  state_data={0};
IEMDiag_t   iem_diag_data={0};
uint16_t counter = 0;
uint8_t ec_handle_log=0;

position_t position={0};
uint8_t camera_gls_average = 0;

/**********************************************************************************************
* Local functions
**********************************************************************************************/
// void ECvar_Init(void)
// {
//     counter=0;

//     memset(&state_data,0,sizeof(StateData_t));
//     memset(&sensor_data,0,sizeof(SensorData_t));
//     memset(&iem_diag_data,0,sizeof(IEMDiag_t));
// }


void EC_handle(void){
    extern uint8_t GET_ADC_FRONT(void);

    if (sensor_data.first_skip<Boot_Delay)
    {
        sensor_data.first_skip++;
        if (sensor_data.first_skip==Boot_Delay-1)
        {
            //通知控制器启动成功指令
            //boot_complete();
        }
        return;
    }

    if (!state_data.Bits.first_running)
    {
        state_data.Bits.first_running=1;

        sensor_data.als_value=GET_ADC_FRONT();//adc_front;//get_als_value();
        sensor_data.gls_value=getMaxGlsValue(frozen_data, (uint16_t)GLS_DATA_LENGTH, db_main.ec_camera_scope, &position);//adc_get_gls_value();
        sensor_data.als_average=Filter_Average(&sensor_data.als_ring_buffer,
                                               sensor_data.als_value,
                                               Filter_Average_ALS_Constant);
        sensor_data.gls_average=Filter_Average(&sensor_data.gls_ring_buffer,
                                               sensor_data.gls_value,
                                               Filter_Average_GLS_Constant);

        sensor_data.gls_history=sensor_data.gls_average;
        sensor_data.als_history=sensor_data.als_average;
        sensor_data.als_final=sensor_data.als_average;
        sensor_data.gls_final=sensor_data.gls_average;

    #if (GEELY_EF1E==0)
        gpio_set_ec_1v8_enable(1);  //EC 1V8 使能
    #endif
    }
    else
    {

        if (counter%Period_ALS==0)
        {
            sensor_data.als_value=GET_ADC_FRONT();//adc_front;//get_als_value();
            sensor_data.als_average=Filter_Average(&sensor_data.als_ring_buffer,
                                                   sensor_data.als_value,
                                                   Filter_Average_ALS_Constant);
            //零修正
            sensor_data.als_average=sensor_data.als_average>=db_main.cali.F_dark?
                                    sensor_data.als_average-db_main.cali.F_dark:0;

            if (Filter_Low_Pass_ALS_Constant)
            {
                sensor_data.als_final=Filter_LowPass(sensor_data.als_history,
                                                     sensor_data.als_average,
                                                     Filter_Low_Pass_ALS_Constant);
                sensor_data.als_history=sensor_data.als_final;
            }
            else
            {
                sensor_data.als_final=sensor_data.als_average;
            }
        }

        if (counter%Period_GLS==0)
        {
            sensor_data.gls_value=getMaxGlsValue(frozen_data, (uint16_t)GLS_DATA_LENGTH, db_main.ec_camera_scope, &position);//adc_get_gls_value();
            camera_gls_average=getAverageGlsValue(frozen_data, (uint16_t)GLS_DATA_LENGTH, db_main.ec_camera_scope);
            sensor_data.gls_average=Filter_Average(&sensor_data.gls_ring_buffer,
                                                   sensor_data.gls_value,
                                                   Filter_Average_GLS_Constant);

            //零修正
            sensor_data.gls_average=sensor_data.gls_average>=db_main.cali.B_dark?
                                    sensor_data.gls_average-db_main.cali.B_dark:0;

            if (Filter_Low_Pass_GLS_Constant)
            {
                sensor_data.gls_final=Filter_LowPass(sensor_data.gls_history,
                                                     sensor_data.gls_average,
                                                     Filter_Low_Pass_GLS_Constant);
                sensor_data.gls_history=sensor_data.gls_final;
            }
            else
            {
                sensor_data.gls_final=sensor_data.gls_average;
            }
        #if 0
            //光电流校准   B-(B_light-B_ref)*B/B_ref
            {

                int32_t value32;

                value32=db_main.cali.B_light-db_main.cali.B_ref;
                value32*=sensor_data.gls_final;
                value32/=db_main.cali.B_ref;

                value32=(int32_t)sensor_data.gls_final-value32;
                if (value32>4000)
                {
                    sensor_data.gls_final=4000;
                }
                else
                {
                    sensor_data.gls_final=(uint16_t)value32;
                }

            }
            //光电流校准 end
        #endif

        }
    }


    if (counter%Period_CTRL==0)
    { 
        if(counter%(Period_CTRL*2)==0){//150*10*2=3000ms, actual period about 150ms
            ec_handle_log=1;
            if(Log_Enabled){
                printf("[%s] @@@@@@@@@@@@ SET ec_handle_log==1 @@@@@@@@@@@@\n", __FUNCTION__);
            }
        }else{
            ec_handle_log=0;
        }

        if(Log_Enabled){
            if(ec_handle_log==1){
                printf("[%s] sensor_data.als_value=%d,sensor_data.gls_value=%d,camera_gls_average=%d\n", __FUNCTION__,sensor_data.als_value,sensor_data.gls_value,camera_gls_average);
                printf("[%s] sensor_data.als_average=%d,sensor_data.gls_average=%d\n", __FUNCTION__,sensor_data.als_average,sensor_data.gls_average);
                printf("[%s] sensor_data.als_final=%d,sensor_data.gls_final=%d\r\n", __FUNCTION__,sensor_data.als_final,sensor_data.gls_final);
            }
        }
        EC_Ctrl();
        //BL_Ctrl();
    }



    if (counter>1000)
    {
        counter=0;
    }
    else
    {
        counter++;
    }
}


void EC_Ctrl(void)
{
    uint8_t gls_diff = 0;
    uint8_t CEM_ec_dimsnvty, CEM_ec_ena, CEM_ec_inhbdim;

    gls_diff = sensor_data.gls_final - camera_gls_average;

    CEM_ec_ena = get_IntrMirrCmdIntrMirrEna();
    //CEM_ec_inhbdim = get_IntrMirrCmdIntrMirrInhbDim();
    if(Log_Enabled){
        if(ec_handle_log==1){
            //printf("[%s] CEM_ec_ena=%d(0-disable 1-enable), CEM_ec_inhbdim=%d\n", __FUNCTION__,CEM_ec_ena,CEM_ec_inhbdim);
            printf("[%s] CEM_ec_ena=%d(0-disable 1-enable)\n", __FUNCTION__,CEM_ec_ena);
        }
    }


#if (GEELY_EF1E==0)
    state_data.Bits.ec_disabled_by_pin=gpio_get_ec_disable();
#endif

    if (state_data.Bits.ec_disabled_by_pin==1
		||state_data.Bits.ec_disabled_by_idcm==1
		||iem_diag_data.board_sick==1
		||iem_diag_data.sensor_sick==1
		||iem_diag_data.v12_sick==1
		||iem_diag_data.v5_sick==1
		||iem_diag_data.v1_8_sick==1
		)
    {
        sensor_data.iec_pwm=0;
    }
    else if((db_main.ec_cem_ena_disable == 0x00 && CEM_ec_ena==0x0) /*|| CEM_ec_inhbdim==0x01*/){
            sensor_data.iec_pwm = 0;
    }
    // else if (/*camera_gls_average < db_main.ec_camera_average ||*/ gls_diff < db_main.ec_camera_diff){
    //     if(Log_Enabled){
    //         if(ec_handle_log){
    //             printf("[%s] camera_gls_average=%d, gls_diff=%d < %d, no EC !!!\n", __FUNCTION__,camera_gls_average,gls_diff,db_main.ec_camera_diff);
    //         }
    //     }
    //     sensor_data.iec_pwm = 0;
    // }
    else
    {
		sensor_data.iec_pwm=get_iec_pwm(sensor_data.als_final,sensor_data.gls_final);
	}

    if (sensor_data.iec_pwm>0)
    {
        if (sensor_data.iec_pwm+db_main.pwm_out.oec_over_iec
                < db_main.pwm_out.oec_max)
        {
            sensor_data.oec_pwm=sensor_data.iec_pwm+db_main.pwm_out.oec_over_iec;
        }
        else
        {
            sensor_data.oec_pwm=db_main.pwm_out.oec_max;
        }
        //流媒体工作时内镜不工作,外镜工作
        if (db_main.mode.BITs.iem_enabled==1)
        {
            sensor_data.iec_pwm=0;
            #if (GEELY_EF1E==0)
			pwm_set_duty(PWM_CH_IEC,0);
            #endif
        }
        else
        {
            #if (GEELY_EF1E==0)
            pwm_set_duty(PWM_CH_IEC,sensor_data.iec_pwm);
            #endif
        }
        #if (GEELY_EF1E==0)
        pwm_set_duty(PWM_CH_OEC,sensor_data.oec_pwm);
        #endif
    }
    else
    {
		sensor_data.oec_pwm=0;
		sensor_data.iec_pwm=0;
	    #if (GEELY_EF1E==0)
        pwm_set_duty(PWM_CH_IEC,0);
        pwm_set_duty(PWM_CH_OEC,0);
        #endif
    }
#if 0
	//流媒体模式下防眩光的
	if (db_main.mode.BITs.iem_enabled==1)
	{
		if(db_main.iem_ec_fixed)
		{
			sensor_data.iec_pwm=db_main.iem_ec_fixed;
			pwm_set_duty(PWM_CH_IEC,sensor_data.iec_pwm);
		}
		else
		{
			pwm_set_duty(PWM_CH_IEC,0);
		}
	}
#endif

    if(Log_Enabled){
        if(ec_handle_log==1){
            printf("[%s] sensor_data.iec_pwm=%d, sensor_data.oec_pwm=%d\n", __FUNCTION__,sensor_data.iec_pwm,sensor_data.oec_pwm);
        }
    }
}


uint8_t getMaxGlsValue(uint8_t array[], uint16_t len, scope_t scope, position_t *pos) {
    uint16_t i;
    uint8_t x,y;
    uint8_t max;

    if(scope.x>=16 || scope.y>=16){
        printf("[%s] scope out of range, return !!! scope.x=%d, scope.y=%d\n", __FUNCTION__,scope.x,scope.y);
        return 0;
    }else if(scope.x+scope.width>16 || scope.y+scope.height>16){
        printf("[%s] scope out of range, return !!! scope.width=%d, scope.height=%d\n", __FUNCTION__,scope.width,scope.height);
        return 0;
    }

    max = array[0];
    for(i=1; i<len; i++){
        x = i%16;
        y = i/16;
        if(x>=scope.x && y>=scope.y && x<scope.x+scope.width && y<scope.y+scope.height){
            if(array[i] > max){
                max = array[i];
                pos->x = x;
                pos->y = y;
            }
        }else{
            //printf("[%s] out of scope !\n", __FUNCTION__);
        }
    }

    if(Log_Enabled){
        //printf("[%s] max=%#x(%d,%d)\n", __FUNCTION__,max,pos->x,pos->y);
    }
    return max;
}

uint8_t getAverageGlsValue(uint8_t array[], uint16_t len, scope_t scope){
    uint16_t i;
    uint8_t x,y;
    uint16_t sum = 0;
    uint16_t count = 0;
    uint8_t average;

    if(scope.x>=16 || scope.y>=16){
        printf("[%s] scope out of range, return !!! scope.x=%d, scope.y=%d\n", __FUNCTION__,scope.x,scope.y);
        return 0;
    }else if(scope.x+scope.width>16 || scope.y+scope.height>16){
        printf("[%s] scope out of range, return !!! scope.width=%d, scope.height=%d\n", __FUNCTION__,scope.width,scope.height);
        return 0;
    }

    for(i=0; i<len; i++){
        x = i%16;
        y = i/16;
        if(x>=scope.x && y>=scope.y && x<scope.x+scope.width && y<scope.y+scope.height){
            sum = sum + array[i];
            count++;
        }
    }
    if(sum != 0){
        average = (sum/count);
    }else{
        if(Log_Enabled){
            printf("[%s] warnning sum == 0 !!!\n", __FUNCTION__);
        }
    }

    if(Log_Enabled){
        //printf("[%s] sum=%d,average=%d,count=%d\n", __FUNCTION__,sum,average,count);
    }
    return average;
}

uint16_t get_iec_pwm_out()
{
    uint16_t iec_pwm_out;
    iec_pwm_out = sensor_data.iec_pwm;

    if(Log_Enabled){
        printf("[%s] iec_pwm_out=%#x\n", __FUNCTION__,iec_pwm_out);
    }
    return iec_pwm_out;
}

uint8_t get_oec_pwm_out()
{
    uint8_t oec_pwm_out=0;
    uint8_t oec_pwm_out_min=0;
    uint16_t oec_pwm_out_final=0;
    uint8_t CEM_ec_dimsnvty = 0x00;//get_IntrMirrCmdIntrMirrDimSnvty();
    
    oec_pwm_out = sensor_data.oec_pwm*0xFF/db_main.pwm_out.oec_max;
    oec_pwm_out_min = (db_main.pwm_out.ec_min+db_main.pwm_out.oec_over_iec)*0xFF/db_main.pwm_out.oec_max;

    if(db_main.ec_dim_snvty == 0x04){//CEM input, skip ec_dim_snvty
        if(CEM_ec_dimsnvty == 0x00){//normal
            oec_pwm_out_final = oec_pwm_out;
        }else if(CEM_ec_dimsnvty == 0x01){//darker
            oec_pwm_out_final = oec_pwm_out*(100+db_main.ec_dim_snvty_ratio)/100;
            if(oec_pwm_out_final>0xFF){
                oec_pwm_out_final = 0xFF;
            }
        }else if(CEM_ec_dimsnvty == 0x02){//lighter
            oec_pwm_out_final = oec_pwm_out*(100-db_main.ec_dim_snvty_ratio)/100;
            if(oec_pwm_out_final<oec_pwm_out_min){
                oec_pwm_out_final = oec_pwm_out_min;
            }
        }else if(CEM_ec_dimsnvty == 0x03){//inhibit
            oec_pwm_out_final = 0;
        }
    }else{//fix ec_dim_snvty
        if(db_main.ec_dim_snvty == 0x00){//normal
            oec_pwm_out_final = oec_pwm_out;
        }else if(db_main.ec_dim_snvty == 0x01){//darker
            oec_pwm_out_final = oec_pwm_out*(100+db_main.ec_dim_snvty_ratio)/100;
            if(oec_pwm_out_final>0xFF){
                oec_pwm_out_final = 0xFF;
            }
        }else if(db_main.ec_dim_snvty == 0x02){//lighter
            oec_pwm_out_final = oec_pwm_out*(100-db_main.ec_dim_snvty_ratio)/100;
            if(oec_pwm_out_final<oec_pwm_out_min){
                oec_pwm_out_final = oec_pwm_out_min;
            }
        }else if(db_main.ec_dim_snvty == 0x03){//inhibit
            oec_pwm_out_final = 0;
        }
    }

    if(Log_Enabled){
        if(ec_handle_log==1){
            printf("[%s] CEM_ec_dimsnvty=%#x\n", __FUNCTION__,CEM_ec_dimsnvty);
            printf("[%s] db_main.ec_dim_snvty=%#x,db_main.ec_cem_ena_disable=%#x,\n", __FUNCTION__,db_main.ec_dim_snvty,db_main.ec_cem_ena_disable);
            printf("[%s] oec_pwm_out=%#x,oec_pwm_out_final=%#x\n", __FUNCTION__,oec_pwm_out,oec_pwm_out_final);
            if(oec_pwm_out_final > 0){
                printf("[%s] ############################################ oec_pwm_out_final=%d\n", __FUNCTION__,oec_pwm_out_final);
                printf("[%s] ###############EC-EC-EC-EC-EC############### oec_pwm_out_final=%d\n", __FUNCTION__,oec_pwm_out_final);
                printf("[%s] ############################################ oec_pwm_out_final=%d\n", __FUNCTION__,oec_pwm_out_final);
            }
        }
    }
    return oec_pwm_out;
}
