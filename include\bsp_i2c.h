/*
 * bsp_i2c.h
 *
 *  Created on: 2022.2.26
 *      Author: lihao
 */

#ifndef BSP_I2C_H_
#define BSP_I2C_H_



#include <stdint.h>

typedef enum
{
	IIC_MODE_MASTER,
	IIC_MODE_SLAVE

}IIC_MODE_T;


/**
 * @brief 模拟I2C接口初始化
 * 
 */
void bsp_sim_i2c_init();


/**
 * @brief 模拟I2C写16位寄存器
 * 
 * @param port 	0-4
 * @param dev_addr 	从机设备地址
 * @param reg 		从机寄存器地址
 * @param pdata 	缓存指针
 * @param num 		读取字节数
 * @return uint8_t 1：成功; 0：失败
 */
uint8_t bsp_sim_i2c_write16_bytes( uint8_t port, uint8_t  dev_addr, uint16_t reg ,const uint8_t *pdata, uint16_t num);
/**
 * @brief 模拟I2C读16位寄存器
 * 
 * @param port  0-4
 * @param dev_addr 	从机设备地址
 * @param reg 		从机寄存器地址
 * @param pdata 	缓存指针
 * @param num 		读取字节数
 * @return uint8_t 1：成功; 0：失败
 */
uint8_t bsp_sim_i2c_read16_bytes( uint8_t port, uint8_t  dev_addr, uint16_t reg, uint8_t *pdata, uint16_t num);

/**
 * @brief 模拟I2C写8位寄存器
 * 
 * @param port  0-4
 * @param dev_addr 	从机设备地址
 * @param reg 		从机寄存器地址
 * @param pdata 	缓存指针
 * @param num 		读取字节数
 * @return uint8_t 1：成功; 0：失败
 */
uint8_t bsp_sim_i2c_write8_bytes( uint8_t port, uint8_t  dev_addr, uint8_t reg ,const uint8_t *pdata, uint16_t num);
/**
 * @brief 模拟I2C读8位寄存器
 * 
 * @param port  0-4
 * @param dev_addr 	从机设备地址
 * @param reg 		从机寄存器地址
 * @param pdata 	缓存指针
 * @param num 		读取字节数
 * @return uint8_t 1：成功; 0：失败
 */
uint8_t bsp_sim_i2c_read8_bytes( uint8_t port, uint8_t  dev_addr, uint8_t reg, uint8_t *pdata, uint16_t num);

/**
 * @brief 模拟I2C写8位寄存器一个字节
 * 
 * @param port  0-4
 * @param dev_addr 	从机设备地址
 * @param reg 		从机寄存器地址
 * @param pdata 	缓存指针
 * @return uint8_t 1：成功; 0：失败
 */
uint8_t bsp_sim_i2c_write_reg( uint8_t port, uint8_t  dev_addr, uint16_t reg, uint8_t data );


/**
 * @brief 模拟I2C读8位寄存器一个字节
 * 
 * @param port  0-4
 * @param dev_addr 	从机设备地址
 * @param reg 		从机寄存器地址
 * @param pdata 	缓存指针
 * @return uint8_t 1：成功; 0：失败
 */
uint8_t bsp_sim_i2c_read_reg( uint8_t port,  uint8_t  dev_addr, uint16_t reg, uint8_t *data );



#endif /* BSP_I2C_H_ */
