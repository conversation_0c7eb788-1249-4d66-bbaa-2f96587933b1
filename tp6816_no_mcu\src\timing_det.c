/*
 * @file	timing_det.c
 * @brief	
 * <AUTHOR>
 * @date	2020-06-12 12:59:55 created
 * $Id: timing_det.c 1.1 $
 */

#include "tp6823.h"
#include "timing_det.h"
#include "config.h"

unsigned short td_fxcnt;
unsigned short td_vtotal;
unsigned short td_htotal;
bit td_interlaced;
unsigned char td_det_status;
unsigned char td_det_format;

#if defined(TD_INTERUPT_ISR)
void td_isr(void)
{
	unsigned char sel;
	unsigned char td_isr_status;
	TD_GET_STATUS(td_isr_status);
	if (TD_IS_VD_LOSS(td_isr_status)) {
		TD_DET_CLOSE();
		td_det_status = TD_STATE_VS_LOSS;
		return;
	}
	if (TD_IS_SYNC_MISS(td_isr_status)) {
		// enable build-in pattern
		if (td_det_status == TD_STATE_VS_STABLE)
			td_det_status = TD_STATE_VS_LEADING;
	}
	else if (TD_IS_SHORT_VS(td_isr_status)) {
		// TODO: ...
		// enable build-in pattern
		if (td_det_status == TD_STATE_VS_STABLE)
			td_det_status = TD_STATE_VS_LEADING;
	}
	else if (TD_IS_TIME_CHG(td_isr_status)) {
		// enable build-in pattern
		if (td_det_status == TD_STATE_VS_STABLE)
			td_det_status = TD_STATE_VS_LEADING;
	}
	if (TD_IS_DET_DONE(td_isr_status)) {
		if (!TD_IS_VS_LEADING(td_isr_status)) {
			td_det_status = TD_STATE_VS_LOSS;
			return;
		}
		if (td_det_status == TD_STATE_REQ_TIMING) {
			TD_SHOW_HSVS_CNT();
			td_htotal = (read_p0(TD_H_KCNT_REG1)
				| (read_p0(TD_H_KCNT_REG2) << 8));
			td_vtotal = (read_p0(TD_V_LCNT_REG1)
				| (read_p0(TD_V_LCNT_REG2) << 8));
			td_vtotal >>= 1;
			TD_SHOW_FRAME_CNT();
			td_fxcnt = ((read_p0(TD_F_XCNT_REG2) >> 2)
				 | (read_p0(TD_F_XCNT_REG3) << 6));
			sel = read_p0(0x00);
			write_p0(0x00, 0x00);
			td_interlaced = ((read_p0(0x0D) & 0x08)? 1 : 0);
			write_p0(0x00, sel);
			td_det_status = TD_STATE_CALC_TIMING;
		}
	}	
}
#endif
void td_init(void)
{
	td_det_status = TD_STATE_NO_INPUT;
	td_det_format = TD_UNKNOWN_TIMING;
#if defined(TD_INTERUPT_ISR)
	// enable interrupts
	TD_SET_IM(TO_200MS | DET_DONE | VS_TIME_CHG | HS_TIME_CHG 
				| HS_MISSING | VS_MISSING);
#else
	TD_SET_IM(0);
#endif
}

#if defined(TD_INTERUPT_ISR)
void td_calc_timing(void)
{
	unsigned char sel = 0, fps = 0;
	unsigned short htotal = 0, vtotal = 0;

	/* Calculate the Frame Rate of Input */
#define TD_GET_FRATE(buf)  \
	do { \
		buf = (OWL_XCLK_DIV / \
			  td_fxcnt); \
	} while(0);
	TD_GET_FRATE(fps);
	DEBUG_LOGI(("[TD] FPS: %bd\n", fps));

	/* Calculate the VTotal of Input */
	vtotal = td_vtotal;
	DEBUG_LOGI(("[TD] VTotal: %d\n", vtotal));

	sel = read_p0(0x00);
	write_p0(0x00, 0x00);
#define TD_GET_HTOTAL(buf, bpp)  \
	do { \
		buf = td_htotal / (8 * bpp); \
	} while(0);
	/* Calculate the HTotal of Input */
	TD_GET_HTOTAL(htotal, TD_GET_BPP()); 
	write_p0(0x00, sel);
	DEBUG_LOGI(("[TD] HTotal: %d\n", htotal));

	td_det_format = TD_UNKNOWN_TIMING;
	if (td_interlaced) {
		DEBUG_LOGI(("[TD] Interlace\n"));

		if (VTOTAL_IS_NTSC(vtotal)) {
			TD_SET_SHORT_VS_THD(NTSC_VTOTAL);
			if (HTOTAL_IS_NTSC(htotal)) {
				DEBUG_LOGI(("[TD] NTSC\n"));
				td_det_format = TD_NTSC;
			}
		}
		else if (VTOTAL_IS_PAL(vtotal)) {
			TD_SET_SHORT_VS_THD(PAL_VTOTAL);
			if (HTOTAL_IS_PAL(htotal)) {
				DEBUG_LOGI(("[TD] PAL\n"));
				td_det_format = TD_PAL;
			}
		}
		else if (VTOTAL_IS_FHD1080I(vtotal)) {
			TD_SET_SHORT_VS_THD(FHD_1080I_VTOTAL);
			if (HTOTAL_IS_FHD1080I60(htotal)) {
				DEBUG_LOGI(("[TD] 1080I60\n"));
				td_det_format = TD_1080I60;
			}
			else if (HTOTAL_IS_FHD1080I50(htotal)) {
				DEBUG_LOGI(("[TD] 1080I50\n"));
				td_det_format = TD_1080I50;
			}
		}
	} else {
		DEBUG_LOGI(("[TD] Progressive\n"));
		
		if (VTOTAL_IS_HD720P(vtotal)) {
			TD_SET_SHORT_VS_THD(HD_720P_VTOTAL);
			if (HTOTAL_IS_HD720P60(htotal)) {
				DEBUG_LOGI(("[TD] 720P60\n"));
				td_det_format = TD_720P60;
			}
			else if (HTOTAL_IS_HD720P50(htotal)) {
				DEBUG_LOGI(("[TD] 720P50\n"));
				td_det_format = TD_720P50;
			}
		}
		else if (VTOTAL_IS_FHD1080P(vtotal)) {
			TD_SET_SHORT_VS_THD(FHD_1080P_VTOTAL);
			if (HTOTAL_IS_FHD1080P60(htotal)) {
				DEBUG_LOGI(("[TD] 1080P60\n"));
				td_det_format = TD_1080P60;
			}
			else if (HTOTAL_IS_FHD1080P50(htotal)) {
				DEBUG_LOGI(("[TD] 1080P50\n"));
				td_det_format = TD_1080P50;
			}
		}
		else if (VTOTAL_IS_SD480P(vtotal)) {
			TD_SET_SHORT_VS_THD(SD_480P_VTOTAL);
			if (VTOTAL_IS_SD480P(vtotal)) {
				DEBUG_LOGI(("[TD] 480P\n"));
				td_det_format = TD_480P;
			}
		}
		else if (VTOTAL_IS_SD576P(vtotal)) {
			TD_SET_SHORT_VS_THD(SD_576P_VTOTAL);
			if (HTOTAL_IS_SD576P(htotal)) {
				DEBUG_LOGI(("[TD] 576P\n"));
				td_det_format = TD_576P;
			}
		}
	}
	/*
	 * else if (... other timing ...)
	 */
	if (td_det_format == TD_UNKNOWN_TIMING) {
		DEBUG_LOGI(("[TD] unkown format\n"));
	}
}

#pragma disable   /* Disable Interrupts */
void td_timing_detect(void)
{
	static unsigned char td_calc_cnt = 0;
	unsigned char td_isr_status;

	if (td_det_status == TD_STATE_VS_LOSS) {
		TD_DET_CLOSE();
		td_det_status = TD_STATE_NO_INPUT;
		td_det_format = TD_UNKNOWN_TIMING;
		DEBUG_LOGI(("[TD] VS_LOSS\n"));
	}
	else if(td_det_status == TD_STATE_NO_INPUT) {
		TD_GET_STATUS(td_isr_status);
		if (TD_IS_VS_LEADING(td_isr_status)) {
			td_det_status = TD_STATE_VS_LEADING;
			TD_SET_IM(TO_200MS | DET_DONE | VS_TIME_CHG | HS_TIME_CHG 
				| HS_MISSING | VS_MISSING);
			TD_EN_MONITOR(1);
		}
	}
	else if (td_det_status != TD_STATE_VS_STABLE) {
		if (td_det_status == TD_STATE_VS_LEADING) {
			td_det_status = TD_STATE_REQ_TIMING;
			td_calc_cnt = 0;
		}
		else if(td_det_status == TD_STATE_CALC_TIMING) {
			td_calc_timing();
			td_calc_cnt++;
			if(td_det_format < TD_UNKNOWN_TIMING 
				|| td_calc_cnt > TD_CALC_TIMING_CNT_MAX) {
				td_det_status = TD_STATE_VS_STABLE;
				td_calc_cnt = 0;
				//TD_SET_IM(TO_200MS | VS_TIME_CHG | HS_TIME_CHG 
				//	| HS_MISSING | VS_MISSING);
			} else {
				td_det_status = TD_STATE_REQ_TIMING;
			}
		}
	}
}
#else
// polling
void td_timing_calc(void)
{
	unsigned char sel;
	TD_SHOW_HSVS_CNT();
	/* Calculate the HTotal of Input */
#define TD_GET_HTOTAL(buf, bpp)  \
	do { \
		buf = read_p0(TD_H_KCNT_REG1); \
		buf |= (read_p0(TD_H_KCNT_REG2) << 8); \
		buf /= (8 * bpp); \
	} while(0);

	sel = read_p0(0x00);
	write_p0(0x00, 0x00);   // set VXi_Access_Sel[1:0] to Vmi
	TD_GET_HTOTAL(td_htotal, TD_GET_BPP());  
	td_interlaced = ((read_p0(0x0D) & 0x08)? 1 : 0);
	write_p0(0x00, sel);
	 
	DEBUG_LOGI("[TD] HTotal: %d\n", td_htotal);

	/* Calculate the VTotal of Input */
#define TD_GET_VTOTAL(buf)  \
	do { \
		buf = read_p0(TD_V_LCNT_REG1); \
		buf |= (read_p0(TD_V_LCNT_REG2) << 8); \
		buf >>= 1; \
	} while(0); 
	TD_GET_VTOTAL(td_vtotal);
	DEBUG_LOGI("[TD] VTotal: %d\n", td_vtotal);

	TD_SHOW_FRAME_CNT();
#define TD_GET_FXCNT(buf)  \
	do { \
		buf = ((read_p0(TD_F_XCNT_REG2) >> 2) \
				| (read_p0(TD_F_XCNT_REG3) << 6)); \
	} while(0);
	/* Calculate the Frame Rate of Input */
#define TD_GET_FRATE(buf)  \
	do { \
		buf = (OWL_XCLK_DIV / \
				buf); \
	} while(0);
	TD_GET_FXCNT(td_fxcnt);
	TD_GET_FRATE(td_fxcnt);
	DEBUG_LOGI(("[TD] FPS: %d\n", td_fxcnt));

	td_det_format = TD_UNKNOWN_TIMING;
	if (td_interlaced) {
		DEBUG_LOGI("[TD] Interlace\n");

		if (VTOTAL_IS_NTSC(td_vtotal)) {
			TD_SET_SHORT_VS_THD(NTSC_VTOTAL);
			if (HTOTAL_IS_NTSC(td_htotal)) {
				DEBUG_LOGI("[TD] NTSC\n");
				td_det_format = TD_NTSC;
			}
		}
		else if (VTOTAL_IS_PAL(td_vtotal)) {
			TD_SET_SHORT_VS_THD(PAL_VTOTAL);
			if (HTOTAL_IS_PAL(td_htotal)) {
				DEBUG_LOGI("[TD] PAL\n");
				td_det_format = TD_PAL;
			}
		}
		else if (VTOTAL_IS_FHD1080I(td_vtotal)) {
			TD_SET_SHORT_VS_THD(FHD_1080I_VTOTAL);
			if (HTOTAL_IS_FHD1080I60(td_htotal)) {
				DEBUG_LOGI("[TD] 1080I60\n");
				td_det_format = TD_1080I60;
			}
			else if (HTOTAL_IS_FHD1080I50(td_htotal)) {
				DEBUG_LOGI("[TD] 1080I50\n");
				td_det_format = TD_1080I50;
			}
		}
		else if(VTOTAL_IS_HD384I(td_vtotal)){
					TD_SET_SHORT_VS_THD(HD_1920x384P_VTOTAL);//!!! HTotaland vtotal must be set to the de
					if(HTOTAL_IS_HD384I60(td_htotal))
						{
							DEBUG_LOGI(("[TD] 1928X384P60\n"));
							td_det_format = TD_384I;
						}
				}
	} else {
		DEBUG_LOGI("[TD] Progressive\n");
		
		if (VTOTAL_IS_HD720P(td_vtotal)) {
			TD_SET_SHORT_VS_THD(HD_720P_VTOTAL);
			if (HTOTAL_IS_HD720P60(td_htotal)) {
				DEBUG_LOGI("[TD] 720P60\n");
				td_det_format = TD_720P60;
			}
			else if (HTOTAL_IS_HD720P50(td_htotal)) {
				DEBUG_LOGI("[TD] 720P50\n");
				td_det_format = TD_720P50;
			}
		}
		else if (VTOTAL_IS_FHD1080P(td_vtotal)) {
			TD_SET_SHORT_VS_THD(FHD_1080P_VTOTAL);
			if (HTOTAL_IS_FHD1080P60(td_htotal)) {
				DEBUG_LOGI("[TD] 1080P60\n");
				td_det_format = TD_1080P60;
			}
			else if (HTOTAL_IS_FHD1080P50(td_htotal)) {
				DEBUG_LOGI(("[TD] 1080P50\n"));
				td_det_format = TD_1080P50;
			}
		}
		else if (VTOTAL_IS_SD480P(td_vtotal)) {
			TD_SET_SHORT_VS_THD(SD_480P_VTOTAL);
			if (VTOTAL_IS_SD480P(td_vtotal)) {
				DEBUG_LOGI("[TD] 480P\n");
				td_det_format = TD_480P;
			}
		}
		else if (VTOTAL_IS_SD576P(td_vtotal)) {
			TD_SET_SHORT_VS_THD(SD_576P_VTOTAL);
			if (HTOTAL_IS_SD576P(td_htotal)) {
				DEBUG_LOGI("[TD] 576P\n");
				td_det_format = TD_576P;
			}
		}
		else if(VTOTAL_IS_HD384I(td_vtotal)){
			TD_SET_SHORT_VS_THD(HD_1920x384P_VTOTAL);//!!! HTotaland vtotal must be set to the de
			if(HTOTAL_IS_HD384I60(td_htotal))
				{
					DEBUG_LOGI(("[TD] 1928X384P60\n"));
					td_det_format = TD_384I;
				}
		}
	}
	/*
	 * else if (... other timing ...)
	 */
	if (td_det_format == TD_UNKNOWN_TIMING) {
		DEBUG_LOGI("[TD] unkown format\n");
	}
}

void td_timing_detect(void)
{
	static unsigned char td_calc_cnt = 0;
	unsigned char td_isr_status;

	TD_GET_STATUS(td_isr_status);
	if (td_det_status == TD_STATE_NO_INPUT) {
		if (TD_IS_VS_LEADING(td_isr_status)) {
			td_det_status = TD_STATE_VS_LEADING;
			TD_EN_MONITOR(1);
		}
		else {
			return;
		}
	}

	if (!TD_IS_DET_DONE(td_isr_status)) {
		return;
	}
	else if (TD_IS_VD_LOSS(td_isr_status)
		|| (!TD_IS_VS_LEADING(td_isr_status))) {
		DEBUG_LOGI("[TD] VD_LOSS\n");
		td_det_format = TD_UNKNOWN_TIMING;
		td_det_status = TD_STATE_NO_INPUT;
		return;
	}
	if (TD_IS_SYNC_MISS(td_isr_status)) {
		// enable build-in pattern
		if (td_det_status == TD_STATE_VS_STABLE) {
			DEBUG_LOGI("[TD] VS/HS Missing: %bX\n", td_isr_status);
			td_det_status = TD_STATE_VS_LEADING;
		}
	}
	else if (TD_IS_SHORT_VS(td_isr_status)) {
		// TODO: ...
		// enable build-in pattern
		if (td_det_status == TD_STATE_VS_STABLE) {
			DEBUG_LOGI("[TD] Short VS: %bX\n", td_isr_status);
			#if 0 //(USE_TD_TIMING_DETECT==1)
			td_det_status = TD_STATE_VS_STABLE;	
			#else
			td_det_status = TD_STATE_VS_LEADING;
			#endif
		}
	}
	else if (TD_IS_TIME_CHG(td_isr_status)) {
		// for timing stable, the detecting again before calc. timing 
		if (td_det_status == TD_STATE_VS_STABLE) {
			DEBUG_LOGI("[TD] VS/HS Timing Changed: %bX\n", td_isr_status);
			td_det_status = TD_STATE_VS_LEADING;
		}
	}
	if(td_det_status != TD_STATE_VS_STABLE) {
		if(td_det_status != TD_STATE_CALC_TIMING) {
			if (TD_IS_MONITOR()) {
				write_p0(TD_CTRL_REG, 0);
			}
			else if (!TD_IS_BUSY()) {
				td_det_format = TD_UNKNOWN_TIMING;
				td_det_status = TD_STATE_CALC_TIMING;
				td_calc_cnt = 0;
				TD_DET_TRIGE();
			}
		}
		else if(td_det_status == TD_STATE_CALC_TIMING) {
			if (TD_IS_DET_DONE(td_isr_status)) {
				td_timing_calc();
				td_calc_cnt++;
				if(td_det_format < TD_UNKNOWN_TIMING 
					|| td_calc_cnt > TD_CALC_TIMING_CNT_MAX) {
					td_det_status = TD_STATE_VS_STABLE;
					TD_EN_MONITOR(1);
				}
				else
					TD_DET_TRIGE();
			}
		}
	}
}
#endif

unsigned short td2_fxcnt;
unsigned short td2_vtotal;
bit td2_interlaced;
unsigned char td2_det_status;
unsigned char td2_det_format;
void td2_init(unsigned char sel)
{
	write_p0(TD2_CTRL_REG, 0x40 | (sel & 0x03));
#if defined(TD2_INTERUPT_ISR)
	TD2_SET_IM(TDET2_DONE | D2_STABLE | TD2_SHORT_VS | TD2_TIMING_CHG | VS2_MISSING);
#else
	TD2_SET_IM(0);
#endif
	td2_det_status = TD_STATE_NO_INPUT;
	td2_det_format = TD_UNKNOWN_TIMING;
	TD2_EN_MONITOR(1);
}

#if defined(TD2_INTERUPT_ISR)
void td2_isr(void)
{
	unsigned char sel;
	unsigned char td2_isr_status;
	TD2_GET_STATUS(td2_isr_status);
	if (TD2_IS_VD_LOSS(td2_isr_status)) {
		TD2_DET_CLOSE();
		td2_det_status = TD_STATE_VS_LOSS;
		return;
	}
	if (TD2_IS_SYNC_MISS(td2_isr_status)) {
		// enable build-in pattern
		if (td2_det_status == TD_STATE_VS_STABLE)
			td2_det_status = TD_STATE_VS_LEADING;
	}
	else if (TD2_IS_SHORT_VS(td2_isr_status)) {
		// TODO: ...
		// enable build-in pattern
		if (td2_det_status == TD_STATE_VS_STABLE)
			td2_det_status = TD_STATE_VS_LEADING;
	}
	else if (TD2_IS_TIME_CHG(td2_isr_status)) {
		// enable build-in pattern
		if (td2_det_status == TD_STATE_VS_STABLE)
			td2_det_status = TD_STATE_VS_LEADING;
	}
	if (TD2_IS_DET_DONE(td2_isr_status)) {
		if (td2_det_status == TD_STATE_REQ_TIMING) {
			if ((td2_isr_status & TD2_STABLE) || (read_p0(0x33) & 0x80)) {
				TD2_SHOW_VS_CNT();
				td2_vtotal = (read_p0(TD2_V_LCNT_REG1)
					| (read_p0(TD2_V_LCNT_REG2) << 8));
				td2_vtotal >>= 1;
				TD2_SHOW_FRAME_CNT();
				td2_fxcnt = ((read_p0(TD2_F_XCNT_REG2) >> 2)
						| ((read_p0(TD2_F_XCNT_REG3) & 0x1f) << 6));
				sel = read_p0(0x00);
				write_p0(0x00, (read_p0(TD2_CTRL_REG) & 0x03));
				td2_interlaced = ((read_p0(0x0D) & 0x08)? 1 : 0);
				write_p0(0x00, sel);
				td2_det_status = TD_STATE_CALC_TIMING;
			}
		}	   
	}
}
void td2_calc_timing(void)
{
	unsigned char fps = 0;

	DEBUG_LOGI(("[TD2] VTotal: %d\n", td2_vtotal));

	/* Calculate the Frame Rate of Input */
#define TD2_GET_FRATE(buf)  \
	do { \
		buf = (OWL_XCLK_DIV / \
				td2_fxcnt); \
	} while(0);
	TD2_GET_FRATE(fps);
	DEBUG_LOGI(("[TD2] FPS: %bd\n", fps));
	if(td2_interlaced) {
		DEBUG_LOGI(("[TD2] Interlace\n"));
	} else {
		DEBUG_LOGI(("[TD2] Progressive\n"));
	}  

	td2_det_format = TD_UNKNOWN_TIMING;
	if (FRATE_IS_HIT(fps, 60)) {
		if(td2_interlaced) {
			if (VTOTAL_IS_NTSC(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(NTSC_VTOTAL);
				DEBUG_LOGI(("[TD2] NTSC\n"));
				td2_det_format = TD_NTSC;
			}
			else if (VTOTAL_IS_FHD1080I(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(FHD_1080I_VTOTAL);
				DEBUG_LOGI(("[TD2] 1080I60\n"));
				td2_det_format = TD_1080I60;
			}
		}
		else {
			if (VTOTAL_IS_HD720P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(HD_720P_VTOTAL);
				DEBUG_LOGI(("[TD2] 720P60\n"));
				td2_det_format = TD_720P60;
			}
			else if (VTOTAL_IS_FHD1080P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(FHD_1080P_VTOTAL);
				DEBUG_LOGI(("[TD2] 1080P60\n"));
				td2_det_format = TD_1080P60;
			}
			else if (VTOTAL_IS_SD480P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(SD_480P_VTOTAL);
				DEBUG_LOGI(("[TD2] 480P\n"));
				td2_det_format = TD_480P;
			}
		}
	}
	else if (FRATE_IS_HIT(fps, 50)) {
		if(td2_interlaced) {
			if (VTOTAL_IS_PAL(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(PAL_VTOTAL);
				DEBUG_LOGI(("[TD2] PAL\n"));
				td2_det_format = TD_PAL;
			}
			else if (VTOTAL_IS_FHD1080I(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(FHD_1080I_VTOTAL);
				DEBUG_LOGI(("[TD2] 1080I50\n"));
				td2_det_format = TD_1080I50;
			}
		}
		else {
			if (VTOTAL_IS_HD720P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(HD_720P_VTOTAL);
				DEBUG_LOGI(("[TD2] 720P50\n"));
				td2_det_format = TD_720P50;
			}
			else if (VTOTAL_IS_FHD1080P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(FHD_1080P_VTOTAL);
				DEBUG_LOGI(("[TD2] 1080P50\n"));
				td2_det_format = TD_1080P50;
			}
			else if (VTOTAL_IS_SD576P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(SD_576P_VTOTAL);
				DEBUG_LOGI(("[TD2] 576P\n"));
				td2_det_format = TD_576P;
			}
		}
	}
	/*
	 * else if (... other timing ...)
	 */
	if (td2_det_format == TD_UNKNOWN_TIMING) {
		DEBUG_LOGI(("[TD2] unkown format\n"));
	}
}
#pragma disable   /* Disable Interrupts */
void td2_timing_detect(void)
{
	static unsigned char td2_calc_cnt = 0;
	unsigned char td2_isr_status;

	if(td2_det_status == TD_STATE_VS_LOSS) {
		td2_det_status = TD_STATE_NO_INPUT;
		td2_det_format = TD_UNKNOWN_TIMING;
		DEBUG_LOGI(("[TD2] VS_LOSS\n"));
	}
	else if(td2_det_status == TD_STATE_NO_INPUT) {
		TD2_GET_STATUS(td2_isr_status);
		if(TD2_IS_VS_LEADING(td2_isr_status)) {
			td2_det_status = TD_STATE_VS_LEADING;
			//TD2_SET_IM(DET2_DONE | TD2_STABLE | TD2_TIMING_CHG | VS2_MISSING);
			TD2_SET_IM(TD2_STABLE | TD2_TIMING_CHG | VS2_MISSING);
			TD2_EN_MONITOR(1);
		}
	}
	else if (td2_det_status != TD_STATE_VS_STABLE) {
		if (td2_det_status == TD_STATE_VS_LEADING) {
			td2_det_status = TD_STATE_REQ_TIMING;
			td2_calc_cnt = 0;
			TD2_SET_IM(DET2_DONE | TD2_STABLE | TD2_SHORT_VS | TD2_TIMING_CHG | VS2_MISSING);
		}
		else if (td2_det_status == TD_STATE_CALC_TIMING) {
			td2_calc_timing();
			td2_calc_cnt++;
			if(td2_det_format < TD_UNKNOWN_TIMING 
				|| td2_calc_cnt > TD_CALC_TIMING_CNT_MAX) {
				td2_det_status = TD_STATE_VS_STABLE;
				td2_calc_cnt = 0;
				TD2_SET_IM(TD2_STABLE | TD2_SHORT_VS | TD2_TIMING_CHG | VS2_MISSING);
			} else {
				td2_det_status = TD_STATE_REQ_TIMING;
			}
		}
	}
}
#else
void td2_timing_calc(void)
{
	unsigned char sel;

	TD2_SHOW_VS_CNT();
	/* Calculate the VTotal of Input */
#define TD2_GET_VTOTAL(buf)  \
	do { \
		buf = read_p0(TD2_V_LCNT_REG1); \
		buf |= (read_p0(TD2_V_LCNT_REG2) << 8); \
		buf >>= 1; \
	} while(0); 
	TD2_GET_VTOTAL(td2_vtotal);
	DEBUG_LOGI("[TD2] VTotal: %d\n", td2_vtotal);

	TD2_SHOW_FRAME_CNT();
#define TD2_GET_FXCNT(buf)  \
	do { \
		buf = ((read_p0(TD2_F_XCNT_REG2) >> 2) \
				| ((read_p0(TD2_F_XCNT_REG3) & 0x1f) << 6)); \
	} while(0);
	/* Calculate the Frame Rate of Input */
#define TD2_GET_FRATE(buf)  \
	do { \
		buf = (OWL_XCLK_DIV / \
				buf); \
	} while(0);
	TD2_GET_FXCNT(td2_fxcnt);
	TD2_GET_FRATE(td2_fxcnt);
	DEBUG_LOGI(("[TD2] FPS: %d\n", td2_fxcnt));

	sel = read_p0(0x00);
	write_p0(0x00, (read_p0(TD2_CTRL_REG) & 0x03));
	td2_interlaced = ((read_p0(0x0D) & 0x08)? 1 : 0);
	write_p0(0x00, sel);
	if(td2_interlaced) {
		DEBUG_LOGI("[TD2] Interlace\n");
	} else {
		DEBUG_LOGI("[TD2] Progressive\n");
	}

	td2_det_format = TD_UNKNOWN_TIMING;
	if (FRATE_IS_HIT(td2_fxcnt, 60)) {
		if(td2_interlaced) {
			if (VTOTAL_IS_NTSC(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(NTSC_VTOTAL);
				DEBUG_LOGI("[TD2] NTSC\n");
				td2_det_format = TD_NTSC;
			}
			else if (VTOTAL_IS_FHD1080I(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(FHD_1080I_VTOTAL);
				DEBUG_LOGI("[TD2] 1080I60\n");
				td2_det_format = TD_1080I60;
			}
		}
		else {
			if (VTOTAL_IS_HD720P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(HD_720P_VTOTAL);
				DEBUG_LOGI("[TD2] 720P60\n");
				td2_det_format = TD_720P60;
			}
			else if (VTOTAL_IS_FHD1080P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(FHD_1080P_VTOTAL);
				DEBUG_LOGI("[TD2] 1080P60\n");
				td2_det_format = TD_1080P60;
			}
			else if (VTOTAL_IS_SD480P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(SD_480P_VTOTAL);
				DEBUG_LOGI("[TD2] 480P\n");
				td2_det_format = TD_480P;
			}
		}
	}
	else if (FRATE_IS_HIT(td2_fxcnt, 50)) {
		if(td2_interlaced) {
			if (VTOTAL_IS_PAL(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(PAL_VTOTAL);
				DEBUG_LOGI("[TD2] PAL\n");
				td2_det_format = TD_PAL;
			}
			else if (VTOTAL_IS_FHD1080I(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(FHD_1080I_VTOTAL);
				DEBUG_LOGI("[TD2] 1080I50\n");
				td2_det_format = TD_1080I50;
			}
		}
		else {
			if (VTOTAL_IS_HD720P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(HD_720P_VTOTAL);
				DEBUG_LOGI("[TD2] 720P50\n");
				td2_det_format = TD_720P50;
			}
			else if (VTOTAL_IS_FHD1080P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(FHD_1080P_VTOTAL);
				DEBUG_LOGI("[TD2] 1080P50\n");
				td2_det_format = TD_1080P50;
			}
			else if (VTOTAL_IS_SD576P(td2_vtotal)) {
				TD2_SET_SHORT_VS_THD(SD_576P_VTOTAL);
				DEBUG_LOGI("[TD2] 576P\n");
				td2_det_format = TD_576P;
			}
		}
	}
	/*
	 * else if (... other timing ...)
	 */
	if (td2_det_format == TD_UNKNOWN_TIMING) {
		DEBUG_LOGI("[TD2] unkown format\n");
	}
}

void td2_timing_detect(void)
{
	static unsigned char td2_calc_cnt = 0;
	unsigned char td2_isr_status;

	TD2_GET_STATUS(td2_isr_status);
	if (!TD2_IS_DET_DONE(td2_isr_status)) {
		if (!TD2_IS_MONITOR())
			TD2_EN_MONITOR(1);
		return;
	}
	else if (TD2_IS_VD_LOSS(td2_isr_status)) {
		//TD2_DET_CLOSE();
		if (td2_det_status != TD_STATE_NO_INPUT) {
			DEBUG_LOGI("[TD2] VD_LOSS %bX\n", td2_isr_status);
			td2_det_format = TD_UNKNOWN_TIMING;
			td2_det_status = TD_STATE_NO_INPUT;
			td2_calc_cnt = 0;
		}
		return;
	}
	if (TD2_IS_SYNC_MISS(td2_isr_status)) {
		// enable build-in pattern
		if (td2_det_status == TD_STATE_VS_STABLE) {
			DEBUG_LOGI("[TD2] VS/HS Missing: %bX\n", td2_isr_status);
			td2_det_status = TD_STATE_VS_LEADING;
			td2_calc_cnt = 0;
		}
	}
	else if (TD2_IS_SHORT_VS(td2_isr_status)) {
		// TODO: ...
		// enable build-in pattern
		if (td2_det_status == TD_STATE_VS_STABLE) {
			// stable => un-stable
			DEBUG_LOGI("[TD2] Short VS: %bX\n", td2_isr_status);
			td2_det_status = TD_STATE_VS_LEADING;
			td2_calc_cnt = 0;
		}
	}
	else if (TD2_IS_TIME_CHG(td2_isr_status)) {
		// for timing stable, the detecting again before calc. timing 
		if (td2_det_status == TD_STATE_VS_STABLE) {
			DEBUG_LOGI("[TD2] VS/HS Timing Changed: %bX\n", td2_isr_status);
			td2_det_status = TD_STATE_VS_LEADING;
			td2_calc_cnt = 0;
		}
	}	
	if(td2_det_status != TD_STATE_VS_STABLE) {
		if (TD2_IS_STABLE()) {
			td2_timing_calc();
			td2_calc_cnt++;
			if(td2_det_format < TD_UNKNOWN_TIMING 
				|| td2_calc_cnt > TD_CALC_TIMING_CNT_MAX) {
				td2_det_status = TD_STATE_VS_STABLE;
			}
		}
		else
			td2_calc_cnt = 0;
	}
}
#endif
