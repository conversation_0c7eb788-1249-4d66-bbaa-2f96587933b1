// MCU Script  File
#include "tp6806.h"
#include "load_tbl.h"
#include "mi2c.h"
// # Input Enable
// Scaler Page3 Register Table
unsigned char
sBE_panel_if_init[] = {
	// $BE,6A,FF,FC,3F,1F,3F,9F
	//	0x6a, 0xff,
	//	0x6b, 0xfc,
	//	0x6c, 0x3f,
	//	0x6d, 0x1f,
	//	0x6e, 0x3f,
	//	0x6f, 0x9f,

		//0x6a, 0xf3,
		//0x6b, 0xfc,
		//0x6c, 0x3f,
		//0x6d, 0x1d,
		//0x6e, 0x3f,
		0x6f, 0x9f,
};
#define sBE_panel_if_init_size	(sizeof(sBE_panel_if_init))
#define load_sBE_panel_if_init_tbl	load_tbl_p3(sBE_panel_if_init, sBE_panel_if_init_size)

// # Driving Select
// Scaler Page3 Register Table
unsigned char
sBE_panel_if_init_1[] = {
	// $BE,70,11,00,00,00,A0,7A
	//	0x70, 0x11,
	//	0x71, 0x00,
	//	0x72, 0x00,
	//	0x73, 0x00,
	//	0x74, 0xa0,
	//	0x75, 0x7a,

		0x70, 0x11,
		0x71, 0x00,
		0x72, 0x00,
		0x73, 0x00,
		0x74, 0xa0,
		0x75, 0x7a,
};
#define sBE_panel_if_init_1_size	(sizeof(sBE_panel_if_init_1))
#define load_sBE_panel_if_init_1_tbl	load_tbl_p3(sBE_panel_if_init_1, sBE_panel_if_init_1_size)

// # Scaling Ratio
// Scaler Page1 Register Table
unsigned char
sBA_panel_if_init_4[] = {
	// $BA,02,00,00,01,00,00,01
		0x02, 0x00,
		0x03, 0x00,
		0x04, 0x01,
		0x05, 0x00,
		0x06, 0x00,
		0x07, 0x01,
		// $BA,0C,40,80
	0x0c, 0x00,//0x40,
	0x0d, 0x80,
};
#define sBA_panel_if_init_4_size	(sizeof(sBA_panel_if_init_4))
#define load_sBA_panel_if_init_4_tbl	load_tbl_p1(sBA_panel_if_init_4, sBA_panel_if_init_4_size)





// # LineBuffer Timing
// Scaler Page1 Register Table
unsigned char
sBA_panel_if_init_5[] = {
	// $BA,8C,EC,D1,00
	0x8c, 0xBE,
	0x8d, 0x0D,
	0x8e, 0x01,
};
#define sBA_panel_if_init_5_size	(sizeof(sBA_panel_if_init_5))
#define load_sBA_panel_if_init_5_tbl	load_tbl_p1(sBA_panel_if_init_5, sBA_panel_if_init_5_size)



// # Pin Function Select
// Scaler Page3 Register Table
unsigned char
sBE_panel_if_init_2[] = {
	// $BE,77,6F,55,D4,00,82,0F
	//	0x77, 0x6f,
	//	0x78, 0x55,
	//	0x79, 0xd4,
	//	0x7a, 0x00,
	//	0x7b, 0x82,
	//	0x7c, 0x0f,

		0x77, 0x07,//0x6f,
		0x78, 0x82,
		0x79, 0x00,
		0x7a, 0x00,
		0x7b, 0x00,
		0x7c, 0x00,
};
#define sBE_panel_if_init_2_size	(sizeof(sBE_panel_if_init_2))
#define load_sBE_panel_if_init_2_tbl	load_tbl_p3(sBE_panel_if_init_2, sBE_panel_if_init_2_size)

// # Pattern Gen.
// Scaler Page1 Register Table
unsigned char
sBA_panel_if_init[] = {
	// $BA,76,80,09,00,00,00
	//	0x76, 0x00,
	//	0x77, 0x09,
	//	0x78, 0x00,
	//	0x79, 0x00,
	//	0x7a, 0x00,

		0x76, 0x00,
		0x77, 0x09,
		0x78, 0xFF,
		0x79, 0x00,
		0x7a, 0x00,
};
#define sBA_panel_if_init_size	(sizeof(sBA_panel_if_init))
#define load_sBA_panel_if_init_tbl	load_tbl_p1(sBA_panel_if_init, sBA_panel_if_init_size)

// # Panel Output Timing
// Scaler Page1 Register Table
unsigned char
sBA_panel_if_init_1[] = {
	// $BA,90,2C,00,5E,01,80,07,98,08,0A,00,1E,00,80,01,C2,01
	//	0x90, 0x02,
	//	0x91, 0x00,
	//	0x92, 0x20,
	//	0x93, 0x00,
	//	0x94, 0x20,
	//	0x95, 0x03,
	//	0x96, 0x20,
	//	0x97, 0x04,
	//	0x98, 0x02,
	//	0x99, 0x00,
	//	0x9a, 0x05,
	//	0x9b, 0x00,
	//	0x9c, 0xe0,
	//	0x9d, 0x01,
	//	0x9e, 0x0d,
	//	0x9f, 0x02,

	/*
			0x90, 0x2c,
		0x91, 0x00,
		0x92, 0x5e,
		0x93, 0x01,
		0x94, 0x80,
		0x95, 0x07,
		0x96, 0x98,
		0x97, 0x08,
		0x98, 0x0a,
		0x99, 0x00,
		0x9a, 0x1e,
		0x9b, 0x00,
		0x9c, 0x80,
		0x9d, 0x01,
		0x9e, 0xc2,
		0x9f, 0x01,
	*/

0x90, 0x02,//0x0a,
0x91, 0x00,
0x92, 0x20,
0x93, 0x00,
0x94, 0x80,
0x95, 0x07,
0x96, 0x20,//0x9e,
0x97, 0x09,//0x08,
0x98, 0x02,
0x99, 0x00,
0x9a, 0x05,//0x14,
0x9b, 0x00,
0x9c, 0x80,
0x9d, 0x01,
0x9e, 0xc0,
0x9f, 0x01,

};
#define sBA_panel_if_init_1_size	(sizeof(sBA_panel_if_init_1))
#define load_sBA_panel_if_init_1_tbl	load_tbl_p1(sBA_panel_if_init_1, sBA_panel_if_init_1_size)

// # Panel Control
// Scaler Page1 Register Table
unsigned char
sBA_panel_if_init_2[] = {
	// $BA,A9,2C,00,00,00,00,20
		0xa9, 0x0c,
		0xaa, 0x00,
		0xab, 0x00,
		0xac, 0x00,
		0xad, 0x00,
		0xae, 0x20,
};
#define sBA_panel_if_init_2_size	(sizeof(sBA_panel_if_init_2))
#define load_sBA_panel_if_init_2_tbl	load_tbl_p1(sBA_panel_if_init_2, sBA_panel_if_init_2_size)


// # LVDS Tx1
// Scaler Page1 Register Table
unsigned char
sBA_panel_if_init_3[] = {
	// $BA,BB,01		# LvTx_Cfg_Sel = 01b: LvT1 only
		0xbb, 0x01,
		// $BA,BC,39,03,35,00
			0xbc, 0xb9,
			0xbd, 0x03,
			0xbe, 0x34,
			0xbf, 0x00,
};
#define sBA_panel_if_init_3_size	(sizeof(sBA_panel_if_init_3))
#define load_sBA_panel_if_init_3_tbl	load_tbl_p1(sBA_panel_if_init_3, sBA_panel_if_init_3_size)

// # DPLL
// Scaler Page3 Register Table
unsigned char
sBE_panel_if_init_3[] = {
	// $BE,90,DA,17,02,88,46,40,66,10
	//	0x90, 0x15,
	//	0x91, 0x01,
	//	0x92, 0x02,
	//	0x93, 0x88,
	//	0x94, 0x46,
	//	0x95, 0x40,
	//	0x96, 0x66,
	//	0x97, 0x10,

	/*
			0x90, 0x15,
		0x91, 0x01,
		0x92, 0x02,
		0x93, 0x88,
		0x94, 0x46,
		0x95, 0x40,
		0x96, 0x66,
		0x97, 0x10,
	*/

0x90, 0x44,//0x7b,
0x91, 0x07,//0x0f,
0x92, 0x02,
0x93, 0x88,
0x94, 0x46,
0x95, 0x40,
0x96, 0x66,
0x97, 0x10,




};
#define sBE_panel_if_init_3_size	(sizeof(sBE_panel_if_init_3))
#define load_sBE_panel_if_init_3_tbl	load_tbl_p3(sBE_panel_if_init_3, sBE_panel_if_init_3_size)

// # Panel Type Select
// Scaler Page3 Register Table
unsigned char
sBE_panel_if_init_4[] = {
	// $BE,7D,06
		//0x7d, 0x06,
			0x7d, 0x01,
};
#define sBE_panel_if_init_4_size	(sizeof(sBE_panel_if_init_4))
#define load_sBE_panel_if_init_4_tbl	load_tbl_p3(sBE_panel_if_init_4, sBE_panel_if_init_4_size)

// # Port1 Extra
// Scaler Page3 Register Table
unsigned char
sBE_panel_if_init_5[] = {
	// $BE,44,03,FC,03
		0x44, 0x03,
		0x45, 0xfc,
		0x46, 0x03,
};
#define sBE_panel_if_init_5_size	(sizeof(sBE_panel_if_init_5))
#define load_sBE_panel_if_init_5_tbl	load_tbl_p3(sBE_panel_if_init_5, sBE_panel_if_init_5_size)


// # GPIOD
// Scaler Page3 Register Table
unsigned char
sBE_panel_if_init_6[] = {
	// $BE,D6,03,41,FF,FF,00,00,00
		0xd6, 0x03,
		0xd7, 0x41,
		0xd8, 0xff,
		0xd9, 0xff,
		0xda, 0x00,
		0xdb, 0x00,
		0xdc, 0x00,
};
#define sBE_panel_if_init_6_size	(sizeof(sBE_panel_if_init_6))
#define load_sBE_panel_if_init_6_tbl	load_tbl_p3(sBE_panel_if_init_6, sBE_panel_if_init_6_size)

// # Power Down
// Scaler Page0 Register Table
unsigned char
sB8_panel_if_init[] = {
	// $B8,F6,B1
		0xf6, 0xb1,//0xb9
};
#define sB8_panel_if_init_size	(sizeof(sB8_panel_if_init))
#define load_sB8_panel_if_init_tbl	load_tbl_p0(sB8_panel_if_init, sB8_panel_if_init_size)


void load_panel_if_init(void)
{
	//load_sBE_panel_if_init_tbl;
	load_sBE_panel_if_init_1_tbl;
	load_sBE_panel_if_init_2_tbl;
	load_sBA_panel_if_init_tbl;
	load_sBA_panel_if_init_1_tbl;
	load_sBA_panel_if_init_2_tbl;
	load_sBA_panel_if_init_3_tbl;
	load_sBA_panel_if_init_4_tbl;
	load_sBA_panel_if_init_5_tbl;

	load_sBE_panel_if_init_3_tbl;
	load_sBE_panel_if_init_4_tbl;
	//load_sBE_panel_if_init_5_tbl;
	load_sB8_panel_if_init_tbl;
}
