/*
 * para.c
 *
 *  Created on: 2022.4.7
 *      Author: mkkk9
 */
#include "para.h"
#include "flash.h"
#include "derivative.h"
#include "type.h"
#include <string.h>
#include <stdio.h>
#include "bsp_nvram.h"


mirr_para_t MirrPara;

#define PARA_ADDR 	(127*512)


#pragma pack(1)
typedef struct
{
    mirr_vedio_para_t vedio_para;
    uint8_t crc;
}para_save_t;


#pragma pack()

uint8_t crc_8( const unsigned char *input_str, size_t num_bytes );
uint8_t update_crc_8( unsigned char crc, unsigned char val );

void para_load_default(mirr_vedio_para_t *vedio_para)
{
    mirr_para_t *p = &MirrPara;

    //CemCem_Lin1Fr01
	p->IntrMirrCmdIntrMirrAsyFanCmpMag = OnOff1_Off;
	p->IntrMirrCmdDrvrSide =  RiLeTyp_Right;
	p->IntrMirrCmdIntrMirrDiagcRst = Flg1_Rst;
	p->IntrMirrCmdIntrMirrDimSnvty = IntrMirrDimSnvtyTyp_Normal;
	p->IntrMirrCmdIntrMirrEna = OnOff1_Off;
	p->IntrMirrCmdIntrMirrInhbDim = OnOff1_Off;
	p->IntrMirrCmdIntrMirrWindHeatrCmpMag = OnOff1_Off;
	
	#if 0
	//CemCem_Lin1Fr07
	//CemCem_Lin1Fr08
	//CemCem_Lin1Fr09

	p->VehModMngtGlbSafe1CarModSts1 = CarModSts1_CarModNorm;
	p->VehModMngtGlbSafe1CarModSubtypWdCarModSubtyp = 0;//0-7
	p->VehModMngtGlbSafe1Cntr = 0;
	p->VehModMngtGlbSafe1EgyLvlElecMai = 0;
	p->VehModMngtGlbSafe1EgyLvlElecSubtyp = 0;
	p->VehModMngtGlbSafe1FltEgyCnsWdSts = FltEgyCns1_NoFlt;
	p->VehModMngtGlbSafe1PwrLvlElecMai = 0;
	p->VehModMngtGlbSafe1PwrLvlElecSubtyp = 0;
	p->VehModMngtGlbSafe1UsgModSts = UsgModSts1_UsgModActv;

	#endif

	p->VehModMngtGlbSafe1PwrLvlElecSubtyp = 0;
	p->VehModMngtGlbSafe1UsgModSts = UsgModSts1_UsgModAbdnd;
	//IrmmCem_Lin1Fr01
	p->IntrMirrRespIntrMirrDimPerc = 0;
	p->IntrMirrRespIntrMirrIntFailr = FailrNoFailr1_NoFailr;
	p->IntrMirrRespResdBoolean = Boolean_FALSE;
	p->IntrMirrRespResdUInt6 = 0;
	p->IntrlMirrDispSysSts = IntrlMirrDispSysSts_Active;
	p->ErrRespIRMM = 0;

    

	//CemCem_Lin1Fr02
	//IrmmCem_Lin1Fr02
	p->vedio_para.StreamingMirrBriAdjmt = p->StreamingMirrBri= vedio_para->StreamingMirrBriAdjmt;
	p->vedio_para.StreamingMirrImgAdjmt = vedio_para->StreamingMirrImgAdjmt;
	p->vedio_para.StreamingMirrPosnAdjmt = vedio_para->StreamingMirrPosnAdjmt;
	p->vedio_para.StreamingMirrSwt = vedio_para->StreamingMirrSwt;

	MirrPara.StreamingMirrBri = MirrPara.vedio_para.StreamingMirrBriAdjmt;
	MirrPara.StreamingMirrImg = MirrPara.vedio_para.StreamingMirrImgAdjmt;
	MirrPara.StreamingMirrPosn = MirrPara.vedio_para.StreamingMirrPosnAdjmt;
	MirrPara.StreamingMirrEnable = MirrPara.vedio_para.StreamingMirrSwt;

#if (USE_API_CODE_LOAD_APPLY_ISPCFGBIN_BYLIN == 1)
	MirrPara.StreamingMirrModeSts = p->vedio_para.StreamingMirrModeSwt;
#endif

	//para_save();

}


uint8_t para_save()
{
    memcpy(&config_file.vedio_para,&MirrPara.vedio_para,sizeof(mirr_vedio_para_t));
    nvram_update();
	return 1;
}
uint8_t para_init(void)
{
	memcpy(&MirrPara.vedio_para,&config_file.vedio_para,sizeof(mirr_vedio_para_t));
	return 1;
}

//void flash_test(void)
//{
//	 	write[0] = 0;
//	    write[1] = 1;
//	    write[2] = 2;
//	    write[3] = 3;
//
//	    FLASH_Init(SystemCoreClock);
//	    FLASH_EraseSector(WRITE_ADDR);
//	    FLASH_Program(PARA_ADDR, (uint8_t *)&write, sizeof(write));
////	    fenzu   = *((volatile uint8_t*)0x);
//
////	uint16_t rtn = FLASH_Program(PARA_ADDR, (uint8_t *)&save, sizeof(save));
////	if(rtn != FLASH_ERR_SUCCESS){
////		return 0;
////	}
//
//	return 1;
//}


static const uint8_t  sht75_crc_table[] = {

	0,   49,  98,  83,  196, 245, 166, 151, 185, 136, 219, 234, 125, 76,  31,  46,
	67,  114, 33,  16,  135, 182, 229, 212, 250, 203, 152, 169, 62,  15,  92,  109,
	134, 183, 228, 213, 66,  115, 32,  17,  63,  14,  93,  108, 251, 202, 153, 168,
	197, 244, 167, 150, 1,   48,  99,  82,  124, 77,  30,  47,  184, 137, 218, 235,
	61,  12,  95,  110, 249, 200, 155, 170, 132, 181, 230, 215, 64,  113, 34,  19,
	126, 79,  28,  45,  186, 139, 216, 233, 199, 246, 165, 148, 3,   50,  97,  80,
	187, 138, 217, 232, 127, 78,  29,  44,  2,   51,  96,  81,  198, 247, 164, 149,
	248, 201, 154, 171, 60,  13,  94,  111, 65,  112, 35,  18,  133, 180, 231, 214,
	122, 75,  24,  41,  190, 143, 220, 237, 195, 242, 161, 144, 7,   54,  101, 84,
	57,  8,   91,  106, 253, 204, 159, 174, 128, 177, 226, 211, 68,  117, 38,  23,
	252, 205, 158, 175, 56,  9,   90,  107, 69,  116, 39,  22,  129, 176, 227, 210,
	191, 142, 221, 236, 123, 74,  25,  40,  6,   55,  100, 85,  194, 243, 160, 145,
	71,  118, 37,  20,  131, 178, 225, 208, 254, 207, 156, 173, 58,  11,  88,  105,
	4,   53,  102, 87,  192, 241, 162, 147, 189, 140, 223, 238, 121, 72,  27,  42,
	193, 240, 163, 146, 5,   52,  103, 86,  120, 73,  26,  43,  188, 141, 222, 239,
	130, 179, 224, 209, 70,  119, 36,  21,  59,  10,  89,  104, 255, 206, 157, 172
};

#define		CRC_START_8		0x00
uint8_t crc_8( const unsigned char *input_str, size_t num_bytes ) {

	size_t a;
	uint8_t crc;
	const unsigned char *ptr;

	crc = CRC_START_8;
	ptr = input_str;

	if ( ptr != NULL ) for (a=0; a<num_bytes; a++) {

		crc = sht75_crc_table[(*ptr++) ^ crc];
	}

	return crc;

}  /* crc_8 */

uint8_t update_crc_8( unsigned char crc, unsigned char val ) {

	return sht75_crc_table[val ^ crc];

}
