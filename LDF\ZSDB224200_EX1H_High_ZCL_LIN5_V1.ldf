/*---------GLOBAL DEFINITIONS-----------*/
LIN_description_file;
LIN_protocol_version             = "2.1";
LIN_language_version             = "2.1";
LIN_speed                        = 19.2 kbps;
/* ----------NODE DEFINITIONS---------- */
Nodes {
   Master:
      CEM,                                                   /* Master node name */
      5.0 ms,                                                /* Time base */
      1.0 ms;                                                /* Jitter */
   Slaves:
      RLSM,
      RMD,
      WMM;
}
/* ----------SIGNAL DEFINITIONS---------- */
Signals {
   /* Signal_name                : <PERSON><PERSON>,       Init,            Publisher, Subscriber(s) */
   AmbIllmnFwdStsAmblillmn1      :  9,        0x0,                 RLSM, CEM;
   AmbIllmnFwdStsAmblillmn2      :  8,        0x0,                 RLSM, CEM;
   AmbIllmnFwdStsChks8           :  8,        0x0,                 RLSM, CEM;
   AmbIllmnFwdStsCntr4           :  4,        0x0,                 RLSM, CEM;
   AmbIllmnFwdStsDataID4         :  4,        0x0,                 RLSM, CEM;
   AMBStsAmbLiIllmn              : 16,        0x0,                 RLSM, CEM;
   AMBStsChks8                   :  8,        0x0,                 RLSM, CEM;
   AMBStsCntr4                   :  4,        0x0,                 RLSM, CEM;
   AMBStsDataID4                 :  4,        0x0,                 RLSM, CEM;
   AmbTForVisy                   :  8,        0x0,                  CEM, RLSM;
   AutWinWipgCmd                 :  3,        0x0,                 RLSM, WMM,CEM;
   CbnGroupPwrReqforRMD		 :  2,        0x0, 		   CEM, RMD;
   CmptFrntWindDewT              : 11,      0x190,                 RLSM, CEM;
   CmptFrntWindT                 : 11,      0x190,                 RLSM, CEM;
   EnaOfflineMonitor             :  1,        0x0,                  CEM, RLSM;
   FWStsAmbLiIllmn               : 16,        0x0,                 RLSM, CEM;
   FWStsChks8                    :  8,        0x0,                 RLSM, CEM;
   FWStsCntr4                    :  4,        0x0,                 RLSM, CEM;
   FWStsDataID4                  :  4,        0x0,                 RLSM, CEM;
   HudSnsrErrChks8               :  8,        0x0,                 RLSM, CEM;
   HudSnsrErrCntr4               :  4,        0x0,                 RLSM, CEM;
   HudSnsrErrDataID4             :  4,        0x0,                 RLSM, CEM;
   HudSnsrErrParChk              :  1,        0x0,                 RLSM, CEM;
   HudSnsrErrSnsrErr             :  1,        0x0,                 RLSM, CEM;
   IntrMirrDimCmdDrvrSide        :  1,        0x0,                  CEM, RLSM,RMD;
   IntrMirrDimCmdIntrMirrAsyFanCmpMag:  1,        0x0,                  CEM, RLSM,RMD;
   IntrMirrDimCmdIntrMirrDiagcRst:  1,        0x1,                  CEM, RLSM,RMD;
   IntrMirrDimCmdIntrMirrDimSnvty:  2,        0x0,                  CEM, RLSM,RMD;
   IntrMirrDimCmdIntrMirrEna     :  1,        0x0,                  CEM, RLSM,RMD;
   IntrMirrDimCmdIntrMirrInhbDim :  1,        0x0,                  CEM, RLSM,RMD;
   IntrMirrDimCmdIntrMirrWindHeatrCmpMag:  1,        0x0,           CEM, RLSM,RMD;
   LiOprnMod                     :  2,        0x0,                 RLSM, CEM;
   OutdBriChks8                  :  8,        0x0,                 RLSM, CEM;
   OutdBriCntr4                  :  4,        0x0,                 RLSM, CEM;
   OutdBriDataID4                :  4,        0x0,                 RLSM, CEM;
   OutdBriSts                    :  2,        0x0,                 RLSM, CEM;
   RainDetection                 :  1,        0x0,                 RLSM, CEM;
   RainfallAmnt                  :  4,        0xe,                 RLSM, CEM;
   RainSensActvn                 :  1,        0x0,                  CEM, RLSM,WMM;
   RainSnsrDiagcRainSnsrHiTDetd  :  1,        0x0,                 RLSM, CEM;
   RainSnsrDiagcRainSnsrHiVoltDetd:  1,        0x0,                 RLSM, CEM;
   RainSnsrErrCalErr             :  1,        0x0,                 RLSM, CEM;
   RainSnsrErrCalErrActv         :  1,        0x0,                 RLSM, CEM;
   RainSnsrErrRainDetnErr        :  1,        0x0,                 RLSM, CEM;
   RainSnsrErrRainDetnErrActv    :  1,        0x0,                 RLSM, CEM;
   RainSnsrLiThd                 :  4,        0x8,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty0     :  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty1     :  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty2     :  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty3     :  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty4     :  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty5     :  4,        0x0,                  CEM, RLSM;
   RainSnsrSnvtyForUsrSnvty6     :  4,        0x0,                  CEM, RLSM;
   ReAdaptReq                    :  1,        0x0,                  CEM, RLSM;
   RelHumSnsrErr                 :  1,        0x0,                 RLSM, CEM;
   RelHumSnsrRelHum              :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10CmplEndSgn1       :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10CmplEndSgn2       :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10CmplEndSgn3       :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10CmplNr1           :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10CmplNr2           :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10CmplNr3           :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10CmplNr4           :  8,        0x0,                 RLSM, CEM;
   RLSMPartNo10CmplNr5           :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoCmplEndSgn1         :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoCmplEndSgn2         :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoCmplEndSgn3         :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoCmplNr1             :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoCmplNr2             :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoCmplNr3             :  8,        0x0,                 RLSM, CEM;
   RLSMPartNoCmplNr4             :  8,        0x0,                 RLSM, CEM;
   RLSMSerNoNr1                  :  8,        0x0,                 RLSM, CEM;
   RLSMSerNoNr2                  :  8,        0x0,                 RLSM, CEM;
   RLSMSerNoNr3                  :  8,        0x0,                 RLSM, CEM;
   RLSMSerNoNr4                  :  8,        0x0,                 RLSM, CEM;
   SolarSnsrErr                  :  1,        0x0,                 RLSM, CEM;
   SolarSnsrLeValue              :  8,        0x0,                 RLSM, CEM;
   SolarSnsrRiValue              :  8,        0x0,                 RLSM, CEM;
   StreamingMirrBri              :  4,        0xa,                  RMD, CEM;
   StreamingMirrBriAdjmt         :  4,        0xa,                  CEM, RMD;
   StreamingMirrEnable           :  1,        0x1,                  RMD, CEM;
   StreamingMirrImg              :  4,        0x0,                  RMD, CEM;
   StreamingMirrImgAdjmt         :  4,        0x0,                  CEM, RMD;
   StreamingMirrModeSts: 4, 0, RMD, CEM;
   StreamingMirrModeSwt: 4, 0, CEM, RMD;
   StreamingMirrPosn             :  4,        0x3,                  RMD, CEM;
   StreamingMirrPosnAdjmt        :  4,        0x3,                  CEM, RMD;
   StreamingMirrReWrnDis         :  4,        0x0,                  CEM, RMD;
   StreamingMirrReWrnSwt         :  1,        0x0,                  CEM, RMD;
   StreamingMirrReWrnSwtSts      :  1,        0x0,                  RMD, CEM;
   StreamingMirrSwt              :  2,        0x1,                  CEM, RMD;
   TwliBriRaw1                   : 14,        0x0,                 RLSM, CEM;
   TwliBriRawQf                  :  2,        0x1,                 RLSM, CEM;
   VehModMngtGlbSafeCarModSts    :  3,        0x0,                  CEM, RLSM,RMD;
   VehModMngtGlbSafeCarModSubtypWdCarModSubtyp:  3,        0x0,                  CEM, RLSM;
   VehModMngtGlbSafeChks8        :  8,        0x0,                  CEM, RLSM;
   VehModMngtGlbSafeCntr4        :  4,        0x0,                  CEM, RLSM;
   VehModMngtGlbSafeDataID4      :  4,        0x0,                  CEM, RLSM;
   VehModMngtGlbSafeEgyLvlElecMai:  4,        0x0,                  CEM, RLSM;
   VehModMngtGlbSafeEgyLvlElecSubtyp:  4,        0x0,                  CEM, RLSM;
   VehModMngtGlbSafeFltEgyCnsWdSts:  1,        0x0,                  CEM, RLSM;
   VehModMngtGlbSafePwrLvlElecMai:  4,        0x0,                  CEM, RLSM;
   VehModMngtGlbSafePwrLvlElecSubtyp:  4,        0x0,                  CEM, RLSM;
   VehModMngtGlbSafePwrModSts    :  4,        0x2,                  CEM, RLSM,RMD;
   VehSpdForWipg                 :  8,        0x0,                  CEM, RLSM,WMM;
   WindCorrnValAmb               :  8,        0x0,                  CEM, RLSM;
   WindCorrnValFrnt              :  8,        0x0,                  CEM, RLSM;
   WindCorrnValHud               :  8,        0x0,                  CEM, RLSM;
   WipgAutFrntMod                :  2,        0x0,                 RLSM, WMM,CEM;
   WipgPwrActvnSafeWipgPwrAcsyModSafe:  2,        0x1,                  CEM, WMM;
   WipgPwrActvnSafeWipgPwrDrvgModSafe:  2,        0x1,                  CEM, WMM;
   WiprActv                      :  1,        0x0,                  CEM, RLSM;
   WiprActvFromWMM               :  1,        0x0,                  WMM, CEM;
   WiprInPrkgPosnLo              :  1,        0x0,                  CEM, RLSM;
   WiprInPrkgPosnLoFromWMM       :  1,        0x0,                  WMM, CEM;
   WiprInWipgAr                  :  1,        0x0,                  CEM, RLSM;
   WiprInWipgArFromWMM           :  1,        0x0,                  WMM, CEM;
   WiprMotCrkAg                  :  8,        0x0,                  WMM, CEM;
   WiprMotDiagcWiprMotHiVoltDetd :  2,        0x0,                  WMM, CEM;
   WiprMotDiagcWiprMotLoVoltDetd :  2,        0x0,                  WMM, CEM;
   WiprMotDiagcWiprMotOvldDetd   :  2,        0x0,                  WMM, CEM;
   WiprMotErrSafe                :  2,        0x0,                  WMM, CEM;
   WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe:  2,        0x1,                  CEM, WMM;
   WiprMotFrntLvrCmdSafeLvrInIntlPosn:  1,        0x0,                  CEM, WMM;
   WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe:  2,        0x1,                  CEM, WMM;
   WiprMotFrntLvrCmdSafeLvrInSnglStrokePos:  1,        0x0,                  CEM, WMM;
   WiprMotFrntOffsAg             :  4,        0x0,                  CEM, WMM;
   WiprMotIntlCmd                :  3,        0x0,                  CEM, RLSM,WMM;
   WiprPosnForSrvReq             :  1,        0x0,                  CEM, WMM;
   WMMPartNo10CmplEndSgn1        :  8,        0x0,                  WMM, CEM;
   WMMPartNo10CmplEndSgn2        :  8,        0x0,                  WMM, CEM;
   WMMPartNo10CmplEndSgn3        :  8,        0x0,                  WMM, CEM;
   WMMPartNo10CmplNr1            :  8,        0x0,                  WMM, CEM;
   WMMPartNo10CmplNr2            :  8,        0x0,                  WMM, CEM;
   WMMPartNo10CmplNr3            :  8,        0x0,                  WMM, CEM;
   WMMPartNo10CmplNr4            :  8,        0x0,                  WMM, CEM;
   WMMPartNo10CmplNr5            :  8,        0x0,                  WMM, CEM;
   WMMPartNoCmplEndSgn1          :  8,        0x0,                  WMM, CEM;
   WMMPartNoCmplEndSgn2          :  8,        0x0,                  WMM, CEM;
   WMMPartNoCmplEndSgn3          :  8,        0x0,                  WMM, CEM;
   WMMPartNoCmplNr1              :  8,        0x0,                  WMM, CEM;
   WMMPartNoCmplNr2              :  8,        0x0,                  WMM, CEM;
   WMMPartNoCmplNr3              :  8,        0x0,                  WMM, CEM;
   WMMPartNoCmplNr4              :  8,        0x0,                  WMM, CEM;
   WMMSerNoNr1                   :  8,        0x0,                  WMM, CEM;
   WMMSerNoNr2                   :  8,        0x0,                  WMM, CEM;
   WMMSerNoNr3                   :  8,        0x0,                  WMM, CEM;
   WMMSerNoNr4                   :  8,        0x0,                  WMM, CEM;
   WshngCycActv                  :  1,        0x0,                  CEM, RLSM;
   WshngCycActvFromWMM           :  1,        0x0,                  WMM, CEM;
   WshrLvrPosnSafe               :  2,        0x1,                  CEM, WMM;
   ErrRespRLSM                   :  1,        0x0,                 RLSM, CEM;
   ErrRespRMD                    :  1,        0x0,                  RMD, CEM;
   ErrRespWMM                    :  1,        0x0,                  WMM, CEM;
}
/* ----------DIAGNOSTIC SIGNAL DEFINITIONS---------- */
Diagnostic_signals {
   /* MasterReq Reserved Signals */
   MasterReqB0         :    8,   0;
   MasterReqB1         :    8,   0;
   MasterReqB2         :    8,   0;
   MasterReqB3         :    8,   0;
   MasterReqB4         :    8,   0;
   MasterReqB5         :    8,   0;
   MasterReqB6         :    8,   0;
   MasterReqB7         :    8,   0;
   /* SlaveResp Reserved Signals */
   SlaveRespB0         :    8,   0;
   SlaveRespB1         :    8,   0;
   SlaveRespB2         :    8,   0;
   SlaveRespB3         :    8,   0;
   SlaveRespB4         :    8,   0;
   SlaveRespB5         :    8,   0;
   SlaveRespB6         :    8,   0;
   SlaveRespB7         :    8,   0;
}
/* ----------UNCONDITIONAL FRAME DEFINITIONS---------- */
Frames {
   RLSMZCLLin5Frame02  :  0x2, RLSM                ,    8 {
      SolarSnsrErr        , 0;
      RainSnsrDiagcRainSnsrHiVoltDetd, 3;
      RainSnsrDiagcRainSnsrHiTDetd, 4;
      LiOprnMod           , 5;
      RainDetection       , 7;
      RainfallAmnt        , 8;
      SolarSnsrLeValue    , 16;
      SolarSnsrRiValue    , 24;
   }
   RLSMZCLLin5Frame03  : 0x15, RLSM                ,    8 {
      AutWinWipgCmd       , 0;
      RainSnsrErrCalErrActv, 8;
      RainSnsrErrCalErr   , 9;
      RainSnsrErrRainDetnErr, 10;
      RainSnsrErrRainDetnErrActv, 11;
      WipgAutFrntMod      , 12;
      ErrRespRLSM         , 15;
      TwliBriRawQf        , 24;
      TwliBriRaw1         , 26;
      OutdBriChks8        , 40;
      OutdBriCntr4        , 48;
      OutdBriDataID4      , 52;
      OutdBriSts          , 56;
   }
   RLSMZCLLin5Frame08  : 0x2c, RLSM                ,    8 {
      CmptFrntWindDewT    , 0;
      RelHumSnsrErr       , 42;
      CmptFrntWindT       , 45;
      RelHumSnsrRelHum    , 56;
   }
   RLSMZCLLin5Frame09  : 0x23, RLSM                ,    8 {
      AmbIllmnFwdStsChks8 , 0;
      AmbIllmnFwdStsCntr4 , 8;
      AmbIllmnFwdStsDataID4, 12;
      AmbIllmnFwdStsAmblillmn1, 16;
      AmbIllmnFwdStsAmblillmn2, 32;
      HudSnsrErrChks8     , 40;
      HudSnsrErrCntr4     , 48;
      HudSnsrErrDataID4   , 52;
      HudSnsrErrParChk    , 56;
      HudSnsrErrSnsrErr   , 57;
   }
   RLSMZCLLin5Frame10  : 0x24, RLSM                ,    8 {
      AMBStsChks8         , 0;
      AMBStsCntr4         , 8;
      AMBStsDataID4       , 12;
      AMBStsAmbLiIllmn    , 16;
      FWStsChks8          , 32;
      FWStsCntr4          , 40;
      FWStsDataID4        , 44;
      FWStsAmbLiIllmn     , 48;
   }
   RLSMZCLLin5PartNrFr01: 0x20, RLSM                ,    7 {
      RLSMPartNoCmplNr1   , 0;
      RLSMPartNoCmplNr2   , 8;
      RLSMPartNoCmplNr3   , 16;
      RLSMPartNoCmplNr4   , 24;
      RLSMPartNoCmplEndSgn1, 32;
      RLSMPartNoCmplEndSgn2, 40;
      RLSMPartNoCmplEndSgn3, 48;
   }
   RLSMZCLLin5PartNrFr02: 0x18, RLSM                ,    8 {
      RLSMPartNo10CmplNr1 , 0;
      RLSMPartNo10CmplNr2 , 8;
      RLSMPartNo10CmplNr3 , 16;
      RLSMPartNo10CmplNr4 , 24;
      RLSMPartNo10CmplNr5 , 32;
      RLSMPartNo10CmplEndSgn1, 40;
      RLSMPartNo10CmplEndSgn2, 48;
      RLSMPartNo10CmplEndSgn3, 56;
   }
   RLSMZCLLin5SerNrFr01: 0x22, RLSM                ,    4 {
      RLSMSerNoNr1        , 0;
      RLSMSerNoNr2        , 8;
      RLSMSerNoNr3        , 16;
      RLSMSerNoNr4        , 24;
   }
   RMDZCLLin5Frame01   : 0x2d, RMD                 ,    8 {
      StreamingMirrBri    , 0;
      StreamingMirrEnable , 4;
      StreamingMirrImg    , 5;
      StreamingMirrModeSts, 16;
      StreamingMirrPosn   , 9;
      StreamingMirrReWrnSwtSts, 13;
      ErrRespRMD          , 63;
   }
   WMMZCLLin5Frame03   : 0x25, WMM                 ,    4 {
      WiprActvFromWMM     , 8;
      WiprInPrkgPosnLoFromWMM, 10;
      WiprInWipgArFromWMM , 11;
      WiprMotErrSafe      , 13;
      ErrRespWMM          , 15;
      WiprMotDiagcWiprMotHiVoltDetd, 16;
      WiprMotDiagcWiprMotLoVoltDetd, 18;
      WiprMotDiagcWiprMotOvldDetd, 20;
      WshngCycActvFromWMM , 22;
      WiprMotCrkAg        , 24;
   }
   WMMZCLLin5PartNrFr01:  0x8, WMM                 ,    7 {
      WMMPartNoCmplNr1    , 0;
      WMMPartNoCmplNr2    , 8;
      WMMPartNoCmplNr3    , 16;
      WMMPartNoCmplNr4    , 24;
      WMMPartNoCmplEndSgn1, 32;
      WMMPartNoCmplEndSgn2, 40;
      WMMPartNoCmplEndSgn3, 48;
   }
   WMMZCLLin5PartNrFr02: 0x19, WMM                 ,    8 {
      WMMPartNo10CmplNr1  , 0;
      WMMPartNo10CmplNr2  , 8;
      WMMPartNo10CmplNr3  , 16;
      WMMPartNo10CmplNr4  , 24;
      WMMPartNo10CmplNr5  , 32;
      WMMPartNo10CmplEndSgn1, 40;
      WMMPartNo10CmplEndSgn2, 48;
      WMMPartNo10CmplEndSgn3, 56;
   }
   WMMZCLLin5SerNrFr01 : 0x28, WMM                 ,    7 {
      WMMSerNoNr1         , 0;
      WMMSerNoNr2         , 8;
      WMMSerNoNr3         , 16;
      WMMSerNoNr4         , 24;
   }
   ZCLZCLLin5Frame02   :  0x5, CEM                 ,    6 {
      CbnGroupPwrReqforRMD, 22;
      StreamingMirrModeSwt, 32;
      VehSpdForWipg       , 0;
      WipgPwrActvnSafeWipgPwrAcsyModSafe, 8;
      WipgPwrActvnSafeWipgPwrDrvgModSafe, 10;
      RainSensActvn       , 12;
      WshrLvrPosnSafe     , 13;
      StreamingMirrReWrnSwt, 15;
      WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe, 16;
      WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe, 18;
      WiprMotFrntLvrCmdSafeLvrInSnglStrokePos, 20;
      WiprMotFrntLvrCmdSafeLvrInIntlPosn, 21;
      WiprMotIntlCmd      , 24;
      WiprPosnForSrvReq   , 27;
      WiprMotFrntOffsAg   , 40;
      StreamingMirrReWrnDis, 44;
   }
   ZCLZCLLin5Frame03   :  0x7, CEM                 ,    7 {
      AmbTForVisy         , 0;
      VehModMngtGlbSafeChks8, 8;
      VehModMngtGlbSafeCntr4, 16;
      VehModMngtGlbSafeDataID4, 20;
      VehModMngtGlbSafeEgyLvlElecMai, 24;
      VehModMngtGlbSafeEgyLvlElecSubtyp, 28;
      VehModMngtGlbSafePwrLvlElecMai, 32;
      VehModMngtGlbSafePwrLvlElecSubtyp, 36;
      VehModMngtGlbSafePwrModSts, 40;
      VehModMngtGlbSafeCarModSts, 44;
      VehModMngtGlbSafeFltEgyCnsWdSts, 47;
      VehModMngtGlbSafeCarModSubtypWdCarModSubtyp, 48;
   }
   ZCLZCLLin5Frame04   :  0x9, CEM                 ,    7 {
      RainSnsrLiThd       , 44;
   }
   ZCLZCLLin5Frame05   :  0xa, CEM                 ,    7 {
      EnaOfflineMonitor   , 17;
   }
   ZCLZCLLin5Frame06   : 0x17, CEM                 ,    8 {
      RainSnsrSnvtyForUsrSnvty0, 0;
      RainSnsrSnvtyForUsrSnvty1, 4;
      RainSnsrSnvtyForUsrSnvty2, 8;
      RainSnsrSnvtyForUsrSnvty3, 12;
      RainSnsrSnvtyForUsrSnvty4, 16;
      RainSnsrSnvtyForUsrSnvty5, 20;
      RainSnsrSnvtyForUsrSnvty6, 24;
      WindCorrnValAmb     , 32;
      WindCorrnValFrnt    , 40;
      WindCorrnValHud     , 48;
      ReAdaptReq          , 60;
   }
   ZCLZCLLin5Frame07   : 0x27, CEM                 ,    1 {
      WiprActv            , 4;
      WiprInPrkgPosnLo    , 5;
      WiprInWipgAr        , 6;
      WshngCycActv        , 7;
   }
   ZCLZCLLin5Frame08   : 0x2a, CEM                 ,    8 {
      IntrMirrDimCmdDrvrSide, 2;
      IntrMirrDimCmdIntrMirrAsyFanCmpMag, 3;
      IntrMirrDimCmdIntrMirrDiagcRst, 4;
      IntrMirrDimCmdIntrMirrDimSnvty, 0;
      IntrMirrDimCmdIntrMirrEna, 5;
      IntrMirrDimCmdIntrMirrInhbDim, 6;
      IntrMirrDimCmdIntrMirrWindHeatrCmpMag, 7;
   }
   ZCLZCLLin5Frame09   : 0x2b, CEM                 ,    8 {
      StreamingMirrBriAdjmt, 0;
      StreamingMirrImgAdjmt, 4;
      StreamingMirrPosnAdjmt, 8;
      StreamingMirrSwt    , 12;
   }
}
/* ----------DIAGNOSTIC FRAME DEFINITIONS---------- */
Diagnostic_frames {
   MasterReq           : 0x3c {
      MasterReqB0         , 0;
      MasterReqB1         , 8;
      MasterReqB2         , 16;
      MasterReqB3         , 24;
      MasterReqB4         , 32;
      MasterReqB5         , 40;
      MasterReqB6         , 48;
      MasterReqB7         , 56;
   }
   SlaveResp           : 0x3d {
      SlaveRespB0         , 0;
      SlaveRespB1         , 8;
      SlaveRespB2         , 16;
      SlaveRespB3         , 24;
      SlaveRespB4         , 32;
      SlaveRespB5         , 40;
      SlaveRespB6         , 48;
      SlaveRespB7         , 56;
   }
}
/* ----------NODE ATTRIBUTE DEFINITIONS---------- */
Node_attributes {
   RLSM {
      LIN_protocol               = "2.1";                    /* Node protocol version */
      configured_NAD             = 0x7b;                     /* configured NAD of node (1-125) */
      initial_NAD                = 0x7b;                     /* initial NAD of node (1-125) */
      product_id                 =    0x0,    0x0,    0x0;   /* Product id */
      response_error             = ErrRespRLSM;              /* Response error signal */
      configurable_frames {
         RLSMZCLLin5Frame02;
         RLSMZCLLin5Frame03;
         RLSMZCLLin5Frame08;
         RLSMZCLLin5Frame09;
         RLSMZCLLin5Frame10;
         RLSMZCLLin5PartNrFr01;
         RLSMZCLLin5PartNrFr02;
         RLSMZCLLin5SerNrFr01;
         ZCLZCLLin5Frame02;
         ZCLZCLLin5Frame03;
         ZCLZCLLin5Frame04;
         ZCLZCLLin5Frame05;
         ZCLZCLLin5Frame06;
         ZCLZCLLin5Frame07;
         ZCLZCLLin5Frame08;
      }
   }
   RMD {
      LIN_protocol               = "2.1";                    /* Node protocol version */
      configured_NAD             = 0x7c;                     /* configured NAD of node (1-125) */
      initial_NAD                = 0x7c;                     /* initial NAD of node (1-125) */
      product_id                 =    0x0,    0x0,    0x0;   /* Product id */
      response_error             = ErrRespRMD;               /* Response error signal */
      configurable_frames {
         RMDZCLLin5Frame01;
         ZCLZCLLin5Frame02;
         ZCLZCLLin5Frame09;
         ZCLZCLLin5Frame03;
         ZCLZCLLin5Frame08;
      }
   }
   WMM {
      LIN_protocol               = "2.1";                    /* Node protocol version */
      configured_NAD             = 0x12;                     /* configured NAD of node (1-125) */
      initial_NAD                = 0x12;                     /* initial NAD of node (1-125) */
      product_id                 =    0x0,    0x0,    0x0;   /* Product id */
      response_error             = ErrRespWMM;               /* Response error signal */
      configurable_frames {
         RLSMZCLLin5Frame03;
         WMMZCLLin5Frame03;
         WMMZCLLin5PartNrFr01;
         WMMZCLLin5PartNrFr02;
         WMMZCLLin5SerNrFr01;
         ZCLZCLLin5Frame02;
      }
   }
}
/* ----------SCHEDULE TABLE DEFINITIONS---------- */
Schedule_tables {
   ZCLLin5ScheduleSerNrPartNr {
      WMMZCLLin5PartNrFr01                delay 10.0 ms;
      WMMZCLLin5PartNrFr02                delay 15.0 ms;
      WMMZCLLin5SerNrFr01                 delay 10.0 ms;
      RLSMZCLLin5PartNrFr01               delay 10.0 ms;
      RLSMZCLLin5PartNrFr02               delay 15.0 ms;
      RLSMZCLLin5SerNrFr01                delay 10.0 ms;
   }
   ZCLLin5ScheduleTable01 {
      ZCLZCLLin5Frame09                   delay 15.0 ms;
      RMDZCLLin5Frame01                   delay 15.0 ms;
      ZCLZCLLin5Frame08                   delay 15.0 ms;
      RLSMZCLLin5Frame09                  delay 15.0 ms;
      RLSMZCLLin5Frame10                  delay 15.0 ms;
      RLSMZCLLin5Frame02                  delay 15.0 ms;
      RLSMZCLLin5Frame03                  delay 15.0 ms;
      WMMZCLLin5Frame03                   delay 10.0 ms;
      ZCLZCLLin5Frame02                   delay 10.0 ms;
      ZCLZCLLin5Frame03                   delay 15.0 ms;
      ZCLZCLLin5Frame04                   delay 15.0 ms;
      ZCLZCLLin5Frame05                   delay 15.0 ms;
      ZCLZCLLin5Frame06                   delay 15.0 ms;
      ZCLZCLLin5Frame07                   delay 15.0 ms;
      RLSMZCLLin5Frame08                  delay 15.0 ms;
   }
}
/* ----------SIGNAL ENCODDING DEFINITIONS---------- */
Signal_encoding_types {
   AmntSnsr {
      logical_value,0,"AmntSnsr_Amnt0";
      logical_value,1,"AmntSnsr_Amnt1";
      logical_value,2,"AmntSnsr_Amnt2";
      logical_value,3,"AmntSnsr_Amnt3";
      logical_value,4,"AmntSnsr_Amnt4";
      logical_value,5,"AmntSnsr_Amnt5";
      logical_value,6,"AmntSnsr_Amnt6";
      logical_value,7,"AmntSnsr_Amnt7";
      logical_value,8,"AmntSnsr_Amnt8";
      logical_value,9,"AmntSnsr_Amnt9";
      logical_value,10,"AmntSnsr_Amnt10";
      logical_value,11,"AmntSnsr_Amnt11";
      logical_value,12,"AmntSnsr_Amnt12";
      logical_value,13,"AmntSnsr_Amnt13";
      logical_value,14,"AmntSnsr_InitValue";
      logical_value,15,"AmntSnsr_Error";
   }
   Boolean {
      logical_value,0,"Boolean_FALSE";
      logical_value,1,"Boolean_TRUE";
   }
   CarModSts1 {
      logical_value,0,"CarModSts1_CarModNorm";
      logical_value,1,"CarModSts1_CarModTrnsp";
      logical_value,2,"CarModSts1_CarModFcy";
      logical_value,3,"CarModSts1_CarModCrash";
      logical_value,5,"CarModSts1_CarModDyno";
   }
   Err1 {
      logical_value,0,"Err1_NoErr";
      logical_value,1,"Err1_Err";
   }
   Flg1 {
      logical_value,0,"Flg1_Rst";
      logical_value,1,"Flg1_Set";
   }
   FltEgyCns1 {
      logical_value,0,"FltEgyCns1_NoFlt";
      logical_value,1,"FltEgyCns1_Flt";
   }
   FltStsSlaveBasc {
      logical_value,0,"FltStsSlaveBasc_FltStsTestPassd";
      logical_value,1,"FltStsSlaveBasc_FltStsTestFaild";
   }
   GenQf1 {
      logical_value,0,"GenQf1_UndefindDataAccur";
      logical_value,1,"GenQf1_TmpUndefdData";
      logical_value,2,"GenQf1_DataAccurNotWithinSpcn";
      logical_value,3,"GenQf1_AccurData";
   }
   IntrMirrDimSnvtyTyp {
      logical_value,0,"IntrMirrDimSnvtyTyp_Normal";
      logical_value,1,"IntrMirrDimSnvtyTyp_Dark";
      logical_value,2,"IntrMirrDimSnvtyTyp_Light";
      logical_value,3,"IntrMirrDimSnvtyTyp_Inhibit";
   }
   LiIllmn {
      physical_value,0,65535,1.0,0.0,"Unitless";
   }
   Liner32 {
      physical_value,0,511,1.0,0.0,"Unitless";
   }
   LiOperMod {
      logical_value,0,"LiOperMod_Night";
      logical_value,1,"LiOperMod_Day";
      logical_value,2,"LiOperMod_Twli";
      logical_value,3,"LiOperMod_Tnl";
   }
   MvBattSwVersAct {
      physical_value,0,255,1.0,0.0,"Unitless";
   }
   Nr5 {
      physical_value,0,7,1.0,0.0,"Unitless";
   }
   OnOff1 {
      logical_value,0,"OnOff1_Off";
      logical_value,1,"OnOff1_On";
   }
   OnOffCrit1 {
      logical_value,0,"OnOffCrit1_NotVld1";
      logical_value,1,"OnOffCrit1_Off";
      logical_value,2,"OnOffCrit1_On";
      logical_value,3,"OnOffCrit1_NotVld2";
   }
   OnOffNoReq {
      logical_value,0,"OnOffNoReq_NoReq";
      logical_value,1,"OnOffNoReq_On";
      logical_value,2,"OnOffNoReq_Off";
   }
   OutdBriSts {
      logical_value,0,"OutdBriSts_Ukwn";
      logical_value,1,"OutdBriSts_Night";
      logical_value,2,"OutdBriSts_Day";
      logical_value,3,"OutdBriSts_Invld";
   }
   ParChks1 {
      logical_value,0,"ParChks1_Unevennrof1";
      logical_value,1,"ParChks1_Evennrof1";
   }
   Perc2 {
      physical_value,0,200,0.5,0.0,"%";
   }
   PowerModeState {
      logical_value,2,"PowerModeState_IDLE";
      logical_value,4,"PowerModeState_ACTIVE";
      logical_value,8,"PowerModeState_DRIVING";
   }
   RainSnsrThd {
      physical_value,0,15,5.0,-40.0,"%";
   }
   RiLeTyp {
      logical_value,0,"RiLeTyp_Right";
      logical_value,1,"RiLeTyp_Left";
   }
   Sunload2D {
      physical_value,0,255,5.0,0.0,"W/m2";
   }
   TByCmptmtSnsrRelHum {
      physical_value,0,2047,0.1,-40.0,"degC";
   }
   TwliBriRaw1 {
      physical_value,0,16383,1.0,0.0,"Unitless";
   }
   UInt4 {
      physical_value,0,15,1.0,0.0,"Unitless";
   }
   UInt8 {
      physical_value,0,255,1.0,0.0,"Unitless";
   }
   WipAg {
      physical_value,0,255,1.0,0.0,"Unitless";
   }
   WipAgOffs {
      physical_value,0,15,1.0,0.0,"Deg";
   }
   WipgAutFrntMod {
      logical_value,0,"WipgAutFrntMod_Off";
      logical_value,1,"WipgAutFrntMod_ImdtMod";
      logical_value,2,"WipgAutFrntMod_IntlMod";
      logical_value,3,"WipgAutFrntMod_ContnsMod";
   }
   WipgSpd2 {
      logical_value,0,"WipgSpd2_WipgSpd0Rpm";
      logical_value,1,"WipgSpd2_WipgSpd40Rpm";
      logical_value,2,"WipgSpd2_WipgSpd43Rpm";
      logical_value,3,"WipgSpd2_WipgSpd46Rpm";
      logical_value,4,"WipgSpd2_WipgSpd50Rpm";
      logical_value,5,"WipgSpd2_WipgSpd54Rpm";
      logical_value,6,"WipgSpd2_WipgSpd57Rpm";
      logical_value,7,"WipgSpd2_WipgSpd60Rpm";
   }
   WipgSpdIntlFromHmi {
      logical_value,0,"WipgSpdIntlFromHmi_Posn0";
      logical_value,1,"WipgSpdIntlFromHmi_Posn1";
      logical_value,2,"WipgSpdIntlFromHmi_Posn2";
      logical_value,3,"WipgSpdIntlFromHmi_Posn3";
      logical_value,4,"WipgSpdIntlFromHmi_Posn4";
      logical_value,5,"WipgSpdIntlFromHmi_Posn5";
      logical_value,6,"WipgSpdIntlFromHmi_Posn6";
      logical_value,7,"WipgSpdIntlFromHmi_Posn7";
   }
}
/* ----------SIGNAL REPRESENTATION DEFINITIONS---------- */
Signal_representation {
   AmntSnsr            :RainfallAmnt;
   Boolean             :EnaOfflineMonitor, RainDetection;
   CarModSts1          :VehModMngtGlbSafeCarModSts;
   Err1                :RelHumSnsrErr, SolarSnsrErr;
   Flg1                :IntrMirrDimCmdIntrMirrDiagcRst;
   FltEgyCns1          :VehModMngtGlbSafeFltEgyCnsWdSts;
   FltStsSlaveBasc     :HudSnsrErrSnsrErr;
   GenQf1              :TwliBriRawQf;
   IntrMirrDimSnvtyTyp :IntrMirrDimCmdIntrMirrDimSnvty;
   LiIllmn             :AMBStsAmbLiIllmn, FWStsAmbLiIllmn;
   Liner32             :AmbIllmnFwdStsAmblillmn1;
   LiOperMod           :LiOprnMod;
   MvBattSwVersAct     :AmbIllmnFwdStsAmblillmn2;
   Nr5                 :VehModMngtGlbSafeCarModSubtypWdCarModSubtyp;
	OnOff1 : IntrMirrDimCmdIntrMirrAsyFanCmpMag, IntrMirrDimCmdIntrMirrEna,
		IntrMirrDimCmdIntrMirrInhbDim, IntrMirrDimCmdIntrMirrWindHeatrCmpMag,
		RainSensActvn, RainSnsrDiagcRainSnsrHiTDetd, RainSnsrDiagcRainSnsrHiVoltDetd,
		RainSnsrErrCalErr, RainSnsrErrCalErrActv, RainSnsrErrRainDetnErr,
		RainSnsrErrRainDetnErrActv, ReAdaptReq, StreamingMirrEnable,
		StreamingMirrReWrnSwt, StreamingMirrReWrnSwtSts, WiprActv,
		WiprActvFromWMM, WiprInPrkgPosnLo, WiprInPrkgPosnLoFromWMM, WiprInWipgAr,
		WiprInWipgArFromWMM, WiprMotFrntLvrCmdSafeLvrInIntlPosn,
		WiprMotFrntLvrCmdSafeLvrInSnglStrokePos, WiprPosnForSrvReq, WshngCycActv,
		WshngCycActvFromWMM;
	OnOffCrit1 : WipgPwrActvnSafeWipgPwrAcsyModSafe,
		WipgPwrActvnSafeWipgPwrDrvgModSafe, WiprMotDiagcWiprMotHiVoltDetd,
		WiprMotDiagcWiprMotLoVoltDetd, WiprMotDiagcWiprMotOvldDetd, WiprMotErrSafe,
		WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe, WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe,
		WshrLvrPosnSafe;
	OnOffNoReq : CbnGroupPwrReqforRMD, StreamingMirrSwt;
   OutdBriSts          :OutdBriSts;
   ParChks1            :HudSnsrErrParChk;
   Perc2               :RelHumSnsrRelHum;
   PowerModeState      :VehModMngtGlbSafePwrModSts;
   RainSnsrThd         :RainSnsrLiThd;
   RiLeTyp             :IntrMirrDimCmdDrvrSide;
   Sunload2D           :SolarSnsrLeValue, SolarSnsrRiValue;
   TByCmptmtSnsrRelHum :CmptFrntWindDewT, CmptFrntWindT;
   TwliBriRaw1         :TwliBriRaw1;
	UInt4 : AmbIllmnFwdStsCntr4, AmbIllmnFwdStsDataID4, AMBStsCntr4,
		AMBStsDataID4, FWStsCntr4, FWStsDataID4, HudSnsrErrCntr4,
		HudSnsrErrDataID4, OutdBriCntr4, OutdBriDataID4,
		RainSnsrSnvtyForUsrSnvty0, RainSnsrSnvtyForUsrSnvty1, RainSnsrSnvtyForUsrSnvty2,
		RainSnsrSnvtyForUsrSnvty3, RainSnsrSnvtyForUsrSnvty4, RainSnsrSnvtyForUsrSnvty5,
		RainSnsrSnvtyForUsrSnvty6, StreamingMirrBri, StreamingMirrBriAdjmt,
		StreamingMirrImg, StreamingMirrImgAdjmt, StreamingMirrModeSts,
		StreamingMirrModeSwt, StreamingMirrPosn, StreamingMirrPosnAdjmt,
		StreamingMirrReWrnDis, VehModMngtGlbSafeCntr4, VehModMngtGlbSafeDataID4,
		VehModMngtGlbSafeEgyLvlElecMai, VehModMngtGlbSafeEgyLvlElecSubtyp,
		VehModMngtGlbSafePwrLvlElecMai, VehModMngtGlbSafePwrLvlElecSubtyp;
	UInt8 : AmbIllmnFwdStsChks8, AMBStsChks8, AmbTForVisy,
		FWStsChks8, HudSnsrErrChks8, OutdBriChks8, RLSMPartNo10CmplEndSgn1,
		RLSMPartNo10CmplEndSgn2, RLSMPartNo10CmplEndSgn3, RLSMPartNo10CmplNr1,
		RLSMPartNo10CmplNr2, RLSMPartNo10CmplNr3, RLSMPartNo10CmplNr4,
		RLSMPartNo10CmplNr5, RLSMPartNoCmplEndSgn1, RLSMPartNoCmplEndSgn2,
		RLSMPartNoCmplEndSgn3, RLSMPartNoCmplNr1, RLSMPartNoCmplNr2,
		RLSMPartNoCmplNr3, RLSMPartNoCmplNr4, RLSMSerNoNr1, RLSMSerNoNr2,
		RLSMSerNoNr3, RLSMSerNoNr4, VehModMngtGlbSafeChks8, VehSpdForWipg,
		WindCorrnValAmb, WindCorrnValFrnt, WindCorrnValHud,
		WMMPartNo10CmplEndSgn1, WMMPartNo10CmplEndSgn2, WMMPartNo10CmplEndSgn3,
		WMMPartNo10CmplNr1, WMMPartNo10CmplNr2, WMMPartNo10CmplNr3,
		WMMPartNo10CmplNr4, WMMPartNo10CmplNr5, WMMPartNoCmplEndSgn1,
		WMMPartNoCmplEndSgn2, WMMPartNoCmplEndSgn3, WMMPartNoCmplNr1,
		WMMPartNoCmplNr2, WMMPartNoCmplNr3, WMMPartNoCmplNr4, WMMSerNoNr1,
		WMMSerNoNr2, WMMSerNoNr3, WMMSerNoNr4;
	WipAg : WiprMotCrkAg;
	WipAgOffs : WiprMotFrntOffsAg;
   WipgAutFrntMod      :WipgAutFrntMod;
   WipgSpd2            :AutWinWipgCmd;
   WipgSpdIntlFromHmi  :WiprMotIntlCmd;
}

