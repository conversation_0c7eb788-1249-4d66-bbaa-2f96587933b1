// MCU Script  File
#include "tp6823.h"
#include "load_tbl.h"
#include "mi2c.h"


unsigned char
sBE_vd_i2c_init[] = {
	// $BE,B1,00,01,C0,10,10,10,10,47,10,00,0D,00,00,00,38
		0xf3, 0x89,
};
#define sBE_vd_i2c_init_size	(sizeof(sBE_vd_i2c_init))
#define load_sBE_vd_i2c_init_tbl	load_tbl_p3(sBE_vd_i2c_init, sBE_vd_i2c_init_size)
// # Main Path Digital Video Input
// $B8,00,00		# VXi_Access_Sel = Main Path
//$B8,01,0C,D0,32,E0,01,11,14,00,50,30,00,00,A0,00,70,00,00,00,00,D0,02,E0,01,10,33,00,20,00,00,00
unsigned char
sB8_input_l601_init[] = {
	//  $B8,00,00
	0x00,0x00,
	//$B8,01,11,80,07,80,01,04,0C,00,30,A0,10,83,80,13,00,00,00,00,00,80,07,80,01,10,43,00,20,C0,00,C0
	0x01,0x0C,
		0x02,0x80,
		0x03,0x07,  //1920*384
		0x04,0x80,
		0x05,0x01,
		0x06,0x04,
		0x07,0x08,
		0x08,0x00,
		0x09,0x0a,
		0x0a,0x60,
		0x0b,0x8d,
		0x0c,0x00,//0x80,
		0x0d,0x80,
		0x0e,0x13,
		0x0f,0x00,
		0x10,0x00,
		0x11,0x00,
		0x12,0x00,
		0x13,0x00,
		0x14,0x80,
		0x15,0x07,
		0x16,0x80,
		0x17,0x01,
		0x18,0x10,
		0x19,0x72,
		//	0x1a,0xa1,
			0x1a,0x20,//0xa5,
			0x1b,0x20,
			0x1c,0xc0,
			0x1d,0x00,
			0x1e,0xc0,
};
#define sB8_input_l601_init_size	(sizeof(sB8_input_l601_init))
#define load_sB8_input_l601_init_tbl	load_tbl_p0(sB8_input_l601_init, sB8_input_l601_init_size)

unsigned char
sBB_input_mipi_init[] = {
	0x20,0x00,
};
#define sBB_input_mipi_init_size	(sizeof(sBB_input_mipi_init))
#define load_sBB_input_mipi_init_tbl	load_tbl_p3(sBB_input_mipi_init, sBB_input_mipi_init_size)



// # LVDS Rx
// Scaler Page3 Register Table
unsigned char
sBE_rx_video_init[] = {
	//$88,02,80
	//$88,07,40
	//$88,0B,40,43,50
	////$88,13,00,00,13,15,00,19,D0,25
	//$88,1C,06,72
	////$88,20,30,86,38,3C
	//$88,25,FF,05,2D,00
	//$88,2B,4A,0A,30,70
	//$88,30,48,BA,2E,90
	//$88,35,05,DC
	//$88,38,40,0E,32,25
		0x02,0x80,
		0x07,0x40,

		0x0B,0x40,
		0x0C,0x43,
		0x0D,0x50,

		0x13,0x00,
		0x14,0x00,
		0x15,0x13,
		0x16,0x15,
		0x17,0x00,
		0x18,0x19,
		0x19,0xd0,
		0x1a,0x25,

		0x1c,0x06,
		0x1d,0x72,

		0x20,0x30,
		0x21,0x86,
		0x22,0x38,
		0x23,0x3C,

		0x25,0xff,
		0x26,0x05,
		0x27,0x2d,
		0x28,0x00,

		0x2B,0x4A,
		0x2C,0x0a,
		0x2D,0x30,
		0x2E,0x70,

		0x30,0x48,
		0x31,0xba,
		0x32,0x2e,
		0x33,0x90,

		0x35,0x05,
		0x36,0xdc,

		0x38,0x40,
		0x39,0x0e,
		0x3A,0x32,
		0x3B,0x25,

};
#define sBE_rx_video_init_size	(sizeof(sBE_rx_video_init))
#define load_sBE_rx_video_init_tbl	load_tbl_vd(sBE_rx_video_init, sBE_rx_video_init_size)


void load_l601_8_init(void)
{
	//load_sBE_vd_i2c_init_tbl;
	//load_sB8_input_l601_init_tbl;
	//load_sBB_input_mipi_init_tbl;
	//load_sBE_rx_video_init_tbl;
}
