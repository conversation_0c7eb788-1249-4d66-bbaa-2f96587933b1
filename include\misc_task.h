/*
 * @file      misc_task.h
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:50 created
 * $Id: misc_task.h 1.1 $
 */

#ifndef MISC_TASK_H_
#define MISC_TASK_H_

#include "stdint.h"
#define NVALUE 50

typedef enum
{
	ADC_12V = 0,
	ADC_LCM ,
	ADC_FRONT,
	ADC_TEMP_BOARD ,
	ADC_CAM_PWR ,
	ADC_MAX
};

#define  ADC_brightest  250       //最亮时候的AD值
#define  ADC_darkest    40           //最暗时候的AD值

#define TEMP_ADC_45_DEGREE    1020
#define TEMP_ADC_55_DEGREE    620
#define TEMP_ADC_75_DEGREE    525
#define TEMP_ADC_85_DEGREE    375

uint8_t getOverHeatFg(void);
void pwm_set_ajust(void);
void pwm_set_init(void);
uint8_t averageFilter();
uint16_t GET_ADC_LCM_Average();
void PWM_set(uint16_t time);


extern void misc_init(void);
extern void misc_task(void);
#endif  /* MISC_TASK_H_ */
