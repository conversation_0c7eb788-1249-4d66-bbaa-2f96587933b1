#include "tp6806.h"
#include "osd_func.h"
#include "test12_res.h"
#include "config.h"

#if (LIN_TP_VERSION_QUERY == 1)
extern unsigned char  tp_version[12];
#endif
void get_test12_res_version(void)
{
	unsigned char cnt = 0;
	unsigned char buf[TEST12_VERSION_CNT];

	if (spi_dma(buf, TEST12_VERSION_CODE_OFFSET, TEST12_VERSION_CNT)
		!= EXIT_SUCCESS) {
		printf("DMA failed, can't get Resource Header!\n");
	}
	if (*((unsigned long *)(buf + TEST12_HEADER_START_IDX))
		!= TEST12_MAGIC_START) {
		printf("Header first magic number mismatch!\n");
	}
	if (*((unsigned long *)(buf + TEST12_HEADER_END_IDX))
		!= TEST12_MAGIC_END) {
		printf("Header last magic number mismatch!\n");
	}
	/* You can get version number here,
	* probably you want to do something like checking the version. */
	printf("TEST12 Resource ID:%02X%02X Version:v%d.%d Size:%d\n",
		buf[TEST12_RES_VER_IDX + 0],
		buf[TEST12_RES_VER_IDX + 1],
		buf[TEST12_RES_VER_IDX + 2],
		buf[TEST12_RES_VER_IDX + 3],
		*((unsigned long *)(buf + TEST12_RES_SIZE_IDX)));

	{
#if (LIN_TP_VERSION_QUERY == 1)
		unsigned char temp[2]={0};
		tp_version[6]='0' + buf[TEST12_RES_VER_IDX + 2];
		sprintf(temp, "%d", buf[TEST12_RES_VER_IDX + 3]);
		tp_version[8]=temp[0];
		tp_version[9]=temp[1];
#endif
	}
}






void show_page_0_page (void)
{
	osd_init();

	spi_load_rls(page_0_com_17_rlsatt_ptr, page_0_com_17_rlsatt_addr,
			page_0_com_17_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_16_rlsatt_ptr, page_0_com_16_rlsatt_addr,
			page_0_com_16_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_15_rlsatt_ptr, page_0_com_15_rlsatt_addr,
			page_0_com_15_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_14_rlsatt_ptr, page_0_com_14_rlsatt_addr,
			page_0_com_14_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_13_rlsatt_ptr, page_0_com_13_rlsatt_addr,
			page_0_com_13_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_12_rlsatt_ptr, page_0_com_12_rlsatt_addr,
			page_0_com_12_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_11_rlsatt_ptr, page_0_com_11_rlsatt_addr,
			page_0_com_11_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_10_rlsatt_ptr, page_0_com_10_rlsatt_addr,
			page_0_com_10_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0__dummy1__rlsatt_ptr, page_0__dummy1__rlsatt_addr,
			page_0__dummy1__rlsatt_size, PAGE_0_CODE_OFFSET); // dummy


	spi_load_rls(page_0_com_9_rlsatt_ptr, page_0_com_9_rlsatt_addr,
			page_0_com_9_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_8_rlsatt_ptr, page_0_com_8_rlsatt_addr,
			page_0_com_8_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_7_rlsatt_ptr, page_0_com_7_rlsatt_addr,
			page_0_com_7_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_6_rlsatt_ptr, page_0_com_6_rlsatt_addr,
			page_0_com_6_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_5_rlsatt_ptr, page_0_com_5_rlsatt_addr,
			page_0_com_5_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_4_rlsatt_ptr, page_0_com_4_rlsatt_addr,
			page_0_com_4_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_3_rlsatt_ptr, page_0_com_3_rlsatt_addr,
			page_0_com_3_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_2_rlsatt_ptr, page_0_com_2_rlsatt_addr,
			page_0_com_2_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0__dummy2__rlsatt_ptr, page_0__dummy2__rlsatt_addr,
			page_0__dummy2__rlsatt_size, PAGE_0_CODE_OFFSET); // dummy


	spi_load_rls(page_0_com_0_rlsatt_ptr, page_0_com_0_rlsatt_addr,
			page_0_com_0_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0_com_1_rlsatt_ptr, page_0_com_1_rlsatt_addr,
			page_0_com_1_rlsatt_size, PAGE_0_CODE_OFFSET);

	spi_load_rls(page_0__dummy3__rlsatt_ptr, page_0__dummy3__rlsatt_addr,
			page_0__dummy3__rlsatt_size, PAGE_0_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	rls_B_config(page_0_RLs_Thread_B_regaddr);
	rls_C_config(page_0_RLs_Thread_C_regaddr);
	rls_D_config(page_0_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN | RLS_C_EN | RLS_B_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLS_EN);
}






void show_page_1_page (void)
{
	osd_init();

	spi_load_rls(page_1_com_6_rlsatt_ptr, page_1_com_6_rlsatt_addr,
			page_1_com_6_rlsatt_size, PAGE_1_CODE_OFFSET);

	spi_load_rls(page_1_com_5_rlsatt_ptr, page_1_com_5_rlsatt_addr,
			page_1_com_5_rlsatt_size, PAGE_1_CODE_OFFSET);

	spi_load_rls(page_1_com_4_rlsatt_ptr, page_1_com_4_rlsatt_addr,
			page_1_com_4_rlsatt_size, PAGE_1_CODE_OFFSET);

	spi_load_rls(page_1_com_3_rlsatt_ptr, page_1_com_3_rlsatt_addr,
			page_1_com_3_rlsatt_size, PAGE_1_CODE_OFFSET);

	spi_load_rls(page_1_com_2_rlsatt_ptr, page_1_com_2_rlsatt_addr,
			page_1_com_2_rlsatt_size, PAGE_1_CODE_OFFSET);

	spi_load_rls(page_1_com_1_rlsatt_ptr, page_1_com_1_rlsatt_addr,
			page_1_com_1_rlsatt_size, PAGE_1_CODE_OFFSET);

	spi_load_rls(page_1__dummy2__rlsatt_ptr, page_1__dummy2__rlsatt_addr,
			page_1__dummy2__rlsatt_size, PAGE_1_CODE_OFFSET); // dummy


	spi_load_rls(page_1_com_0_rlsatt_ptr, page_1_com_0_rlsatt_addr,
			page_1_com_0_rlsatt_size, PAGE_1_CODE_OFFSET);

	spi_load_rls(page_1__dummy3__rlsatt_ptr, page_1__dummy3__rlsatt_addr,
			page_1__dummy3__rlsatt_size, PAGE_1_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	rls_C_config(page_1_RLs_Thread_C_regaddr);
	rls_D_config(page_1_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN | RLS_C_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLS_EN);
}





#if (USE_LCM_FLIP == 1)
void show_page_2_page (void)
{
	osd_init();

	spi_load_rls(page_2_com_7_rlsatt_ptr, page_2_com_7_rlsatt_addr,
			page_2_com_7_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_6_rlsatt_ptr, page_2_com_6_rlsatt_addr,
			page_2_com_6_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_5_rlsatt_ptr, page_2_com_5_rlsatt_addr,
			page_2_com_5_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_4_rlsatt_ptr, page_2_com_4_rlsatt_addr,
			page_2_com_4_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_3_rlsatt_ptr, page_2_com_3_rlsatt_addr,
			page_2_com_3_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_2_rlsatt_ptr, page_2_com_2_rlsatt_addr,
			page_2_com_2_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_1_rlsatt_ptr, page_2_com_1_rlsatt_addr,
			page_2_com_1_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2__dummy2__rlsatt_ptr, page_2__dummy2__rlsatt_addr,
			page_2__dummy2__rlsatt_size, PAGE_2_CODE_OFFSET); // dummy


	spi_load_rls(page_2_com_0_rlsatt_ptr, page_2_com_0_rlsatt_addr,
			page_2_com_0_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2__dummy3__rlsatt_ptr, page_2__dummy3__rlsatt_addr,
			page_2__dummy3__rlsatt_size, PAGE_2_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	rls_C_config(page_2_RLs_Thread_C_regaddr);
	rls_D_config(page_2_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN | RLS_C_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLS_EN);
}
#else
void show_page_2_page (void)
{
	osd_init();

	spi_load_rls(page_2_com_1_rlsatt_ptr, page_2_com_1_rlsatt_addr,
			page_2_com_1_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_2_rlsatt_ptr, page_2_com_2_rlsatt_addr,
			page_2_com_2_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_3_rlsatt_ptr, page_2_com_3_rlsatt_addr,
			page_2_com_3_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_4_rlsatt_ptr, page_2_com_4_rlsatt_addr,
			page_2_com_4_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_5_rlsatt_ptr, page_2_com_5_rlsatt_addr,
			page_2_com_5_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_6_rlsatt_ptr, page_2_com_6_rlsatt_addr,
			page_2_com_6_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2_com_7_rlsatt_ptr, page_2_com_7_rlsatt_addr,
			page_2_com_7_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2__dummy2__rlsatt_ptr, page_2__dummy2__rlsatt_addr,
			page_2__dummy2__rlsatt_size, PAGE_2_CODE_OFFSET); // dummy


	spi_load_rls(page_2_com_0_rlsatt_ptr, page_2_com_0_rlsatt_addr,
			page_2_com_0_rlsatt_size, PAGE_2_CODE_OFFSET);

	spi_load_rls(page_2__dummy3__rlsatt_ptr, page_2__dummy3__rlsatt_addr,
			page_2__dummy3__rlsatt_size, PAGE_2_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	rls_C_config(page_2_RLs_Thread_C_regaddr);
	rls_D_config(page_2_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN | RLS_C_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLS_EN);
}

#endif





void show_page_3_page (void)
{
	osd_init();

	spi_load_oram(page_3_com_0_rlgatt_ptr, page_3_com_0_rlgatt_addr,
			page_3_com_0_rlgatt_size);
	spi_load_oram(page_3_com_0_rlgoram_ptr, page_3_com_0_rlgcode_addr,
			page_3_com_0_rlgcode_size);

	spi_load_oram(page_3_com_1_rlgatt_ptr, page_3_com_1_rlgatt_addr,
			page_3_com_1_rlgatt_size);
	spi_load_oram(page_3_com_1_rlgoram_ptr, page_3_com_1_rlgcode_addr,
			page_3_com_1_rlgcode_size);

	spi_load_oram(page_3_com_2_rlgatt_ptr, page_3_com_2_rlgatt_addr,
			page_3_com_2_rlgatt_size);
	spi_load_oram(page_3_com_2_rlgoram_ptr, page_3_com_2_rlgcode_addr,
			page_3_com_2_rlgcode_size);

	spi_load_rls(page_3_com_0_rlsatt_ptr, page_3_com_0_rlsatt_addr,
			page_3_com_0_rlsatt_size, PAGE_3_CODE_OFFSET);

	spi_load_rls(page_3__dummy0__rlsatt_ptr, page_3__dummy0__rlsatt_addr,
			page_3__dummy0__rlsatt_size, PAGE_3_CODE_OFFSET); // dummy


	spi_load_rls(page_3_com_3_rlsatt_ptr, page_3_com_3_rlsatt_addr,
			page_3_com_3_rlsatt_size, PAGE_3_CODE_OFFSET);

	spi_load_rls(page_3__dummy1__rlsatt_ptr, page_3__dummy1__rlsatt_addr,
			page_3__dummy1__rlsatt_size, PAGE_3_CODE_OFFSET); // dummy


	spi_load_rls(page_3_com_1_rlsatt_ptr, page_3_com_1_rlsatt_addr,
			page_3_com_1_rlsatt_size, PAGE_3_CODE_OFFSET);

	spi_load_rls(page_3__dummy2__rlsatt_ptr, page_3__dummy2__rlsatt_addr,
			page_3__dummy2__rlsatt_size, PAGE_3_CODE_OFFSET); // dummy


	spi_load_rls(page_3_com_2_rlsatt_ptr, page_3_com_2_rlsatt_addr,
			page_3_com_2_rlsatt_size, PAGE_3_CODE_OFFSET);

	spi_load_rls(page_3__dummy3__rlsatt_ptr, page_3__dummy3__rlsatt_addr,
			page_3__dummy3__rlsatt_size, PAGE_3_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_config(page_3_RLg_Thread_B_regaddr);
	rlg_C_config(page_3_RLg_Thread_C_regaddr);
	rlg_D_config(page_3_RLg_Thread_D_regaddr);
	rls_A_config(page_3_RLs_Thread_A_regaddr);
	rls_B_config(page_3_RLs_Thread_B_regaddr);
	rls_C_config(page_3_RLs_Thread_C_regaddr);
	rls_D_config(page_3_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN | RLS_C_EN | RLS_B_EN | RLS_A_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLG_EN | OSD_RLS_EN);
}






void show_page_4_page (void)
{
	osd_init();

	spi_load_oram(page_4_com_0_rlgatt_ptr, page_4_com_0_rlgatt_addr,
			page_4_com_0_rlgatt_size);
	spi_load_oram(page_4_com_0_rlgoram_ptr, page_4_com_0_rlgcode_addr,
			page_4_com_0_rlgcode_size);

	spi_load_rls(page_4_com_4_rlsatt_ptr, page_4_com_4_rlsatt_addr,
			page_4_com_4_rlsatt_size, PAGE_4_CODE_OFFSET);

	spi_load_rls(page_4__dummy0__rlsatt_ptr, page_4__dummy0__rlsatt_addr,
			page_4__dummy0__rlsatt_size, PAGE_4_CODE_OFFSET); // dummy


	spi_load_rls(page_4_com_3_rlsatt_ptr, page_4_com_3_rlsatt_addr,
			page_4_com_3_rlsatt_size, PAGE_4_CODE_OFFSET);

	spi_load_rls(page_4__dummy1__rlsatt_ptr, page_4__dummy1__rlsatt_addr,
			page_4__dummy1__rlsatt_size, PAGE_4_CODE_OFFSET); // dummy


	spi_load_rls(page_4_com_2_rlsatt_ptr, page_4_com_2_rlsatt_addr,
			page_4_com_2_rlsatt_size, PAGE_4_CODE_OFFSET);

	spi_load_rls(page_4__dummy2__rlsatt_ptr, page_4__dummy2__rlsatt_addr,
			page_4__dummy2__rlsatt_size, PAGE_4_CODE_OFFSET); // dummy


	spi_load_rls(page_4_com_1_rlsatt_ptr, page_4_com_1_rlsatt_addr,
			page_4_com_1_rlsatt_size, PAGE_4_CODE_OFFSET);

	spi_load_rls(page_4__dummy3__rlsatt_ptr, page_4__dummy3__rlsatt_addr,
			page_4__dummy3__rlsatt_size, PAGE_4_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_config(page_4_RLg_Thread_D_regaddr);
	rls_A_config(page_4_RLs_Thread_A_regaddr);
	rls_B_config(page_4_RLs_Thread_B_regaddr);
	rls_C_config(page_4_RLs_Thread_C_regaddr);
	rls_D_config(page_4_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN | RLS_C_EN | RLS_B_EN | RLS_A_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLG_EN | OSD_RLS_EN);
}


#if (LIN_TP_VERSION_QUERY == 1)
uint8_t lin_tp_version = 0x0;

uint8_t TP_VERSION_ARR[][12]={
        "EF1E1v0.70T",
        "EF1E1v0.80T",
        "EF1E1v0.81T",
        "EF1E1v0.82T",
        "EF1E1v0.83T",
        "EF1E1v0.84T",
        "EF1E1v0.85T",
        "EF1E1v0.86T",
        "EF1E1v0.87T",
        "EF1E1v0.88T",
        "EF1E1v0.89T"
};
uint8_t LIN_VERSION_TP_ARR[]={
        0x00,
        0x01,
        0x02,
        0x03,
        0x04,
        0x05,
        0x06,
        0x07,
        0x08,
        0x09,
        0x0A
};

uint8_t query_lin_version_tp_index(uint8_t str[])
{
	uint8_t i;
	uint8_t index = 0;
	uint8_t number = sizeof(LIN_VERSION_TP_ARR) / sizeof(LIN_VERSION_TP_ARR[0]);
	printf("[%s] tpversion=%s, number=%d\n", __FUNCTION__,str,number);
	for(i=0; i<number; i++){
		if(strcmp(str,&TP_VERSION_ARR[i][0]) == 0){
			index = i;
			//printf("[%s] index=%d\n", __FUNCTION__,index);
			break;
		}
	}
	printf("[%s] index=%d\n", __FUNCTION__,index);
	return index;
}

uint8_t get_lin_version_tp(uint8_t tpver[])
{
	uint8_t ver,index;
	index = query_lin_version_tp_index(tpver);
	ver = LIN_VERSION_TP_ARR[index];
	printf("[%s] tp lin version=%#x\r\n", __FUNCTION__,ver);
	return ver;
}
#endif
#if (EF1E_TEST_MODE == 4)
void show_page_5_page (void)
{
	osd_init();

	spi_load_oram(page_5_com_0_rlgatt_ptr, page_5_com_0_rlgatt_addr,
			page_5_com_0_rlgatt_size);
	spi_load_oram(page_5_com_0_rlgoram_ptr, page_5_com_0_rlgcode_addr,
			page_5_com_0_rlgcode_size);



	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_config(page_5_RLg_Thread_A_regaddr);
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_en(OSD_CTRL_EN | OSD_RLG_EN);
}






void show_page_6_page (void)
{
	osd_init();

	spi_load_oram(page_6_com_0_rlgatt_ptr, page_6_com_0_rlgatt_addr,
			page_6_com_0_rlgatt_size);
	spi_load_oram(page_6_com_0_rlgoram_ptr, page_6_com_0_rlgcode_addr,
			page_6_com_0_rlgcode_size);



	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_config(page_6_RLg_Thread_A_regaddr);
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_en(OSD_CTRL_EN | OSD_RLG_EN);
}






void show_page_7_page (void)
{
	osd_init();

	spi_load_rls(page_7_com_3_rlsatt_ptr, page_7_com_3_rlsatt_addr,
			page_7_com_3_rlsatt_size, PAGE_7_CODE_OFFSET);

	spi_load_rls(page_7__dummy0__rlsatt_ptr, page_7__dummy0__rlsatt_addr,
			page_7__dummy0__rlsatt_size, PAGE_7_CODE_OFFSET); // dummy


	spi_load_rls(page_7_com_2_rlsatt_ptr, page_7_com_2_rlsatt_addr,
			page_7_com_2_rlsatt_size, PAGE_7_CODE_OFFSET);

	spi_load_rls(page_7__dummy1__rlsatt_ptr, page_7__dummy1__rlsatt_addr,
			page_7__dummy1__rlsatt_size, PAGE_7_CODE_OFFSET); // dummy


	spi_load_rls(page_7_com_1_rlsatt_ptr, page_7_com_1_rlsatt_addr,
			page_7_com_1_rlsatt_size, PAGE_7_CODE_OFFSET);

	spi_load_rls(page_7__dummy2__rlsatt_ptr, page_7__dummy2__rlsatt_addr,
			page_7__dummy2__rlsatt_size, PAGE_7_CODE_OFFSET); // dummy


	spi_load_rls(page_7_com_0_rlsatt_ptr, page_7_com_0_rlsatt_addr,
			page_7_com_0_rlsatt_size, PAGE_7_CODE_OFFSET);

	spi_load_rls(page_7__dummy3__rlsatt_ptr, page_7__dummy3__rlsatt_addr,
			page_7__dummy3__rlsatt_size, PAGE_7_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	rls_A_config(page_7_RLs_Thread_A_regaddr);
	rls_B_config(page_7_RLs_Thread_B_regaddr);
	rls_C_config(page_7_RLs_Thread_C_regaddr);
	rls_D_config(page_7_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN | RLS_C_EN | RLS_B_EN | RLS_A_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLS_EN);
}






void show_page_8_page (void)
{
	osd_init();

	spi_load_rls(page_8_com_1_rlsatt_ptr, page_8_com_1_rlsatt_addr,
			page_8_com_1_rlsatt_size, PAGE_8_CODE_OFFSET);

	spi_load_rls(page_8__dummy2__rlsatt_ptr, page_8__dummy2__rlsatt_addr,
			page_8__dummy2__rlsatt_size, PAGE_8_CODE_OFFSET); // dummy


	spi_load_rls(page_8_com_0_rlsatt_ptr, page_8_com_0_rlsatt_addr,
			page_8_com_0_rlsatt_size, PAGE_8_CODE_OFFSET);

	spi_load_rls(page_8__dummy3__rlsatt_ptr, page_8__dummy3__rlsatt_addr,
			page_8__dummy3__rlsatt_size, PAGE_8_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	rls_C_config(page_8_RLs_Thread_C_regaddr);
	rls_D_config(page_8_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN | RLS_C_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLS_EN);
}
#else
void show_page_5_page (void)
{
	osd_init();

	spi_load_rls(page_5_com_0_rlsatt_ptr, page_5_com_0_rlsatt_addr,
			page_5_com_0_rlsatt_size, PAGE_5_CODE_OFFSET);

	spi_load_rls(page_5__dummy3__rlsatt_ptr, page_5__dummy3__rlsatt_addr,
			page_5__dummy3__rlsatt_size, PAGE_5_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	rls_D_config(page_5_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLS_EN);
}






void show_page_6_page (void)
{
	osd_init();

	spi_load_rls(page_6_com_0_rlsatt_ptr, page_6_com_0_rlsatt_addr,
			page_6_com_0_rlsatt_size, PAGE_6_CODE_OFFSET);

	spi_load_rls(page_6__dummy3__rlsatt_ptr, page_6__dummy3__rlsatt_addr,
			page_6__dummy3__rlsatt_size, PAGE_6_CODE_OFFSET); // dummy




	row_A_disable();
	row_B_disable();
	row_C_disable();
	row_D_disable();
	rlg_A_disable();
	rlg_B_disable();
	rlg_C_disable();
	rlg_D_disable();
	rls_D_config(page_6_RLs_Thread_D_regaddr);
	win_A_disable();
	win_B_disable();
	win_C_disable();
	win_D_disable();
	mat_A_disable();
	mat_B_disable();
	mat_C_disable();
	mat_D_disable();


	osd_wr_rls_en(RLS_D_EN);
	osd_wr_en(OSD_CTRL_EN | OSD_RLS_EN);
}


#endif
