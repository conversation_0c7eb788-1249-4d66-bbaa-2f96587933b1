/*************************************************************************************/

//                                                                                     

// Description: LIN Description file created using VDE						            

// Created:     11/12/2024 11:27:39

// Author:      VDE

//                                                                                     

/*************************************************************************************/

LIN_description_file;
LIN_protocol_version = "2.1";
LIN_language_version = "2.1";
LIN_speed = 19.2 kbps;
Channel_name = "ZCL_LIN5";

Nodes {
  Master: ZCL, 5.0 ms, 1.0 ms ;
  Slaves: RLSM, RMD, WMM ;
}

Signals {
  IntrMirrDimRespIntrMirrDimPerc: 8, 0, <PERSON><PERSON>, <PERSON><PERSON>;//Dec 0 = 0% (no dimming). Dec 255 = 100% (fully dimming)
  IntrMirrDimRespIntrMirrIntFailr: 1, 0, RMD, ZCL;//failure status
  IntrMirrDimRespResdBoolean: 1, 0, RMD, ZCL;//reserved
  IntrMirrDimRespResdUInt6: 6, 0, RMD, ZCL;//reserved
  IRMMRMDPartNoCmplEndSgn1: 8, 0, RMD, ZCL;//First letter of suffix
  IRMMRMDPartNoCmplEndSgn2: 8, 0, RMD, ZCL;//Second letter of suffix
  IRMMRMDPartNoCmplEndSgn3: 8, 0, RMD, ZCL;//Third letter of suffix
  IRMMRMDPartNoCmplNr1: 8, 0, RMD, ZCL;//First BCD two numbers
  IRMMRMDPartNoCmplNr2: 8, 0, RMD, ZCL;//Second BCD two numbers
  IRMMRMDPartNoCmplNr3: 8, 0, RMD, ZCL;//Third BCD two numbers
  IRMMRMDPartNoCmplNr4: 8, 0, RMD, ZCL;//Fourth BCD two numbers
  IRMMRMDPartNo10CmplEndSgn1: 8, 0, RMD, ZCL;//First letter of suffix
  IRMMRMDPartNo10CmplEndSgn2: 8, 0, RMD, ZCL;//Second letter of suffix
  IRMMRMDPartNo10CmplEndSgn3: 8, 0, RMD, ZCL;//Third letter of suffix
  IRMMRMDPartNo10CmplNr1: 8, 0, RMD, ZCL;//First BCD two numbers
  IRMMRMDPartNo10CmplNr2: 8, 0, RMD, ZCL;//Second BCD two numbers
  IRMMRMDPartNo10CmplNr3: 8, 0, RMD, ZCL;//Third BCD two numbers
  IRMMRMDPartNo10CmplNr4: 8, 0, RMD, ZCL;//Fourth BCD two numbers
  IRMMRMDPartNo10CmplNr5: 8, 0, RMD, ZCL;//Fifth BCD two numbers
  IRMMRMDSerNoNr1: 8, 0, RMD, ZCL;//Byte1 of the serial number
  IRMMRMDSerNoNr2: 8, 0, RMD, ZCL;//Byte2 of the serial number
  IRMMRMDSerNoNr3: 8, 0, RMD, ZCL;//Byte3 of the serial number
  IRMMRMDSerNoNr4: 8, 0, RMD, ZCL;//Byte4 of the serial number
  LiOprnMod: 2, 0, RLSM, ZCL;//information about day,night,twilight and tunnel detection. 
  RainDetection: 1, 0, RLSM, ZCL;//rain detected
  RainfallAmnt: 4, 14, RLSM, ZCL;//The signal describe the amount of rain falling
  RainSnsrDiagcRainSnsrHiTDetd: 1, 0, RLSM, ZCL;// High temperature detected
  RainSnsrDiagcRainSnsrHiVoltDetd: 1, 0, RLSM, ZCL;// High voltage detected
  SolarSnsrErr: 1, 0, RLSM, ZCL;//sun sensor error status.
  SolarSnsrLeValue: 8, 0, RLSM, ZCL;//Left side sun sensor value.
  SolarSnsrRiValue: 8, 0, RLSM, ZCL;//Right side sun sensor value.
  AutWinWipgCmd: 3, 0, RLSM, WMM, ZCL;//Wiping speed command.
  ErrRespRLSM: 1, 0, RLSM, ZCL;
  OutdBriChks8: 8, 0, RLSM, ZCL;//Checksum
  OutdBriCntr4: 4, 0, RLSM, ZCL;//Counter
  OutdBriDataID4: 4, 0, RLSM, ZCL;//Data ID
  OutdBriSts: 2, 0, RLSM, ZCL;//Input for controlling lights on and off automatically. Status information.
  RainSnsrErrCalErr: 1, 0, RLSM, ZCL;//Rain Sensor Error.
  RainSnsrErrCalErrActv: 1, 0, RLSM, ZCL;//Rain Sensor Error.
  RainSnsrErrRainDetnErr: 1, 0, RLSM, ZCL;//Rain Sensor Error.
  RainSnsrErrRainDetnErrActv: 1, 0, RLSM, ZCL;//Rain Sensor Error.
  TwliBriRaw1: 14, 0, RLSM, ZCL;//Outside ambient light measured by SUS and sent to CSC as lux value. Raw value
  TwliBriRawQf: 2, 1, RLSM, ZCL;//Outside ambient light measured by SUS and sent to CSC as lux value. Quality Factor
  WipgAutFrntMod: 2, 0, RLSM, WMM, ZCL;//Information regarding rain sensor operational mode.
  CmptFrntWindDewT: 11, 400, RLSM, ZCL;//Compartment front windscreen dew temperature.
  CmptFrntWindT: 11, 400, RLSM, ZCL;//Compartment front windsreeen temperature signal.
  RelHumSnsrErr: 1, 0, RLSM, ZCL;//Compartment relative humidity sensor error information.
  RelHumSnsrRelHum: 8, 0, RLSM, ZCL;//Compartment relative humidity signal.
  AmbIllmnFwdStsAmblillmn1: 9, 0, RLSM, ZCL;//8 bit ambient ullumination level.
  AmbIllmnFwdStsAmblillmn2: 8, 0, RLSM, ZCL;//9 bit ambient ullumination level.
  AmbIllmnFwdStsChks8: 8, 0, RLSM, ZCL;//checksum
  AmbIllmnFwdStsCntr4: 4, 0, RLSM, ZCL;//counter
  AmbIllmnFwdStsDataID4: 4, 0, RLSM, ZCL;//DataID4
  HudSnsrErrChks8: 8, 0, RLSM, ZCL;//checksum
  HudSnsrErrCntr4: 4, 0, RLSM, ZCL;//counter
  HudSnsrErrDataID4: 4, 0, RLSM, ZCL;//DataID4
  HudSnsrErrParChk: 1, 0, RLSM, ZCL;//Signal indicates whether a fault has currently been detected in the forward facing HUD sensor of the RLSM or not. To ensure signal integrity, the signal also includes a parity bit ParChk. Odd parity is used. 
  HudSnsrErrSnsrErr: 1, 0, RLSM, ZCL;//Signal indicates whether a fault has currently been detected in the forward facing HUD sensor of the RLSM or not. 
  AMBStsAmbLiIllmn: 16, 0, RLSM, ZCL;//16 bit ambient ullumination level.
  AMBStsChks8: 8, 0, RLSM, ZCL;//checksum
  AMBStsCntr4: 4, 0, RLSM, ZCL;//counter
  AMBStsDataID4: 4, 0, RLSM, ZCL;//DataID4
  FWStsAmbLiIllmn: 16, 0, RLSM, ZCL;//16 bit ambient ullumination level.
  FWStsChks8: 8, 0, RLSM, ZCL;//checksum
  FWStsCntr4: 4, 0, RLSM, ZCL;//counter
  FWStsDataID4: 4, 0, RLSM, ZCL;//DataID4
  RLSMPartNoCmplEndSgn1: 8, 0, RLSM, ZCL;//First letter of suffix
  RLSMPartNoCmplEndSgn2: 8, 0, RLSM, ZCL;//Second letter of suffix
  RLSMPartNoCmplEndSgn3: 8, 0, RLSM, ZCL;//Third letter of suffix
  RLSMPartNoCmplNr1: 8, 0, RLSM, ZCL;//First BCD two numbers
  RLSMPartNoCmplNr2: 8, 0, RLSM, ZCL;//Second BCD two numbers
  RLSMPartNoCmplNr3: 8, 0, RLSM, ZCL;//Third BCD two numbers
  RLSMPartNoCmplNr4: 8, 0, RLSM, ZCL;//Fourth  BCD two numbers
  RLSMPartNo10CmplEndSgn1: 8, 0, RLSM, ZCL;//First letter of suffix
  RLSMPartNo10CmplEndSgn2: 8, 0, RLSM, ZCL;//Second letter of suffix
  RLSMPartNo10CmplEndSgn3: 8, 0, RLSM, ZCL;//Third letter of suffix
  RLSMPartNo10CmplNr1: 8, 0, RLSM, ZCL;//First BCD two numbers
  RLSMPartNo10CmplNr2: 8, 0, RLSM, ZCL;//Second BCD two numbers
  RLSMPartNo10CmplNr3: 8, 0, RLSM, ZCL;//Third BCD two numbers
  RLSMPartNo10CmplNr4: 8, 0, RLSM, ZCL;//Fourth  BCD two numbers
  RLSMPartNo10CmplNr5: 8, 0, RLSM, ZCL;//Fifth BCD two numbers
  RLSMSerNoNr1: 8, 0, RLSM, ZCL;//Byte1 of the serial number
  RLSMSerNoNr2: 8, 0, RLSM, ZCL;//Byte2 of the serial number
  RLSMSerNoNr3: 8, 0, RLSM, ZCL;//Byte3 of the serial number
  RLSMSerNoNr4: 8, 0, RLSM, ZCL;//Byte4 of the serial number
  ErrRespRMD: 1, 0, RMD, ZCL;
  StreamingMirrBri: 4, 10, RMD, ZCL;//Feedback of manually adjust the brightness of the streaming media rearview mirror.
  StreamingMirrEnable: 1, 1, RMD, ZCL;//Feedback on or off the streaming media rearview mirror.
  StreamingMirrImg: 4, 0, RMD, ZCL;//Feedback of rearview mirror streaming image zoom.
  StreamingMirrModeSts: 4, 0, RMD, ZCL;//mode switch status to ZC
  StreamingMirrPosn: 4, 3, RMD, ZCL;//Feedback of streaming media rearview mirror position adjustment
  StreamingMirrReWrnSwtSts: 1, 0, RMD, ZCL;//Feedback the rear collision warning switch status of mirror streaming
  ErrRespWMM: 1, 0, WMM, ZCL;
  WiprActvFromWMM: 1, 0, WMM, ZCL;//Information regarding wiper activation
  WiprInPrkgPosnLoFromWMM: 1, 0, WMM, ZCL;//Information regarding wiper position on the windscreen
  WiprInWipgArFromWMM: 1, 0, WMM, ZCL;//Information regarding wiper position on the windscreen.
  WiprMotCrkAg: 8, 0, WMM, ZCL;//Wiper motor crank angle
  WiprMotDiagcWiprMotHiVoltDetd: 2, 0, WMM, ZCL;//Information regarding overload, high voltage and low voltage on the wiper motor module.  Overload detected (2-bit coded)
  WiprMotDiagcWiprMotLoVoltDetd: 2, 0, WMM, ZCL;//Information regarding overload, high voltage and low voltage on the wiper motor module.  Overload detected (2-bit coded)
  WiprMotDiagcWiprMotOvldDetd: 2, 0, WMM, ZCL;//Information regarding overload, high voltage and low voltage on the wiper motor module.  Overload detected (2-bit coded)
  WiprMotErrSafe: 2, 0, WMM, ZCL;//Wiper Motor Error (2-bit coded, system safety relevant)
  WshngCycActvFromWMM: 1, 0, WMM, ZCL;//Information regarding front washing cycle active or not.
  WMMPartNoCmplEndSgn1: 8, 0, WMM, ZCL;//First letter of suffix
  WMMPartNoCmplEndSgn2: 8, 0, WMM, ZCL;//Second letter of suffix
  WMMPartNoCmplEndSgn3: 8, 0, WMM, ZCL;//Third letter of suffix
  WMMPartNoCmplNr1: 8, 0, WMM, ZCL;//First BCD two numbers
  WMMPartNoCmplNr2: 8, 0, WMM, ZCL;//Second BCD two numbers
  WMMPartNoCmplNr3: 8, 0, WMM, ZCL;//Third BCD two numbers
  WMMPartNoCmplNr4: 8, 0, WMM, ZCL;//Fourth BCD two numbers
  WMMPartNo10CmplEndSgn1: 8, 0, WMM, ZCL;//First letter of suffix
  WMMPartNo10CmplEndSgn2: 8, 0, WMM, ZCL;//Second letter of suffix
  WMMPartNo10CmplEndSgn3: 8, 0, WMM, ZCL;//Third letter of suffix
  WMMPartNo10CmplNr1: 8, 0, WMM, ZCL;//First BCD two numbers
  WMMPartNo10CmplNr2: 8, 0, WMM, ZCL;//Second BCD two numbers
  WMMPartNo10CmplNr3: 8, 0, WMM, ZCL;//Third BCD two numbers
  WMMPartNo10CmplNr4: 8, 0, WMM, ZCL;//Fourth BCD two numbers
  WMMPartNo10CmplNr5: 8, 0, WMM, ZCL;//Fifth BCD two numbers
  WMMSerNoNr1: 8, 0, WMM, ZCL;//Byte1 of the serial number
  WMMSerNoNr2: 8, 0, WMM, ZCL;//Byte2 of the serial number
  WMMSerNoNr3: 8, 0, WMM, ZCL;//Byte3 of the serial number
  WMMSerNoNr4: 8, 0, WMM, ZCL;//Byte4 of the serial number
  CbnGroupPwrReqforRMD: 2, 0, ZCL, RMD;//Power mode
  RainSensActvn: 1, 0, ZCL, RLSM, WMM;//Rain sensor function activation command to the rain sensor and the wiper motor.
  StreamingMirrModeSwt: 4, 0, ZCL, RMD;//mode switch to RMD
  StreamingMirrReWrnDis: 4, 0, ZCL, RMD;//Rearview mirror streaming  rear collision warning display
  StreamingMirrReWrnSwt: 1, 0, ZCL, RMD;//Rearview mirror streaming rear collision warning switch
  VehSpdForWipg: 8, 0, ZCL, RLSM, WMM;//Vehicle speed (0-255 Kilometres per hour).
  WipgPwrActvnSafeWipgPwrAcsyModSafe: 2, 1, ZCL, WMM;//Signals controlling operational mode of the wiper motor. Convenience mode or above (former Terminal X)
  WipgPwrActvnSafeWipgPwrDrvgModSafe: 2, 1, ZCL, WMM;//Signals controlling operational mode of the wiper motor. Driving mode (former Terminal 15)
  WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe: 2, 1, ZCL, WMM;//Signals requesting different wiping modes depending on the positions of the wiper stalk.  Wiper lever in high speed position (2-bit coded)
  WiprMotFrntLvrCmdSafeLvrInIntlPosn: 1, 0, ZCL, WMM;//Signals requesting different wiping modes depending on the positions of the wiper stalk.  Wiper lever in interval position
  WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe: 2, 1, ZCL, WMM;//Signals requesting different wiping modes depending on the positions of the wiper stalk.  Wiper lever in low speed position (2-bit coded)
  WiprMotFrntLvrCmdSafeLvrInSnglStrokePos: 1, 0, ZCL, WMM;//Signals requesting different wiping modes depending on the positions of the wiper stalk.  Wiper lever in single stroke position
  WiprMotFrntOffsAg: 4, 0, ZCL, WMM;//Information used to calibrate the upper reversal position of the wiper arm 
  WiprMotIntlCmd: 3, 0, ZCL, RLSM, WMM;//Rain sensor rensitivity ring setting on the wiper stalk as selected by the driver.
  WiprPosnForSrvReq: 1, 0, ZCL, WMM;//Request to the wiper motor to raise the wiper arm to a service position
  WshrLvrPosnSafe: 2, 1, ZCL, WMM;//Washer Lever Position.
  AmbTForVisy: 8, 0, ZCL, RLSM,RMD;//The temperature shall be calculated from the signal with the following operation
  VehModMngtGlbSafeCarModSts: 3, 0, ZCL, RLSM,RMD;//CarMode Status
  VehModMngtGlbSafeCarModSubtypWdCarModSubtyp: 3, 0, ZCL, RLSM,RMD;//CarMode substate status
  VehModMngtGlbSafeChks8: 8, 0, ZCL, RLSM,RMD;//Checksum
  VehModMngtGlbSafeCntr4: 4, 0, ZCL, RLSM,RMD;//Counter
  VehModMngtGlbSafeDataID4: 4, 0, ZCL, RLSM,RMD;//DataID
  VehModMngtGlbSafeEgyLvlElecMai: 4, 0, ZCL, RLSM,RMD;//Main energy level in the vehicle.
  VehModMngtGlbSafeEgyLvlElecSubtyp: 4, 0, ZCL, RLSM,RMD;//Substate of the energy level in the vehicle.
  VehModMngtGlbSafeFltEgyCnsWdSts: 1, 0, ZCL, RLSM,RMD;//reserved
  VehModMngtGlbSafePwrLvlElecMai: 4, 0, ZCL, RLSM,RMD;//Main Power level in the vehicle.
  VehModMngtGlbSafePwrLvlElecSubtyp: 4, 0, ZCL, RLSM,RMD;//Substate of the power level in the vehicle.
  VehModMngtGlbSafePwrModSts: 4, 2, ZCL, RLSM,RMD;//PowerMode Status
  RainSnsrLiThd: 4, 8, ZCL, RLSM;//Rain sensor light threshold settings can be changed through this signal.
  EnaOfflineMonitor: 1, 0, ZCL, RLSM;//Rain monitoring if car is Off.
  RainSnsrSnvtyForUsrSnvty0: 4, 0, ZCL, RLSM;//Signals used to map the different sensitivity settings that can be selected by the driver to an internal sensitivity setting in the rain sensor. This is used to trim the sensitivity of the different rain sensor settings that the driver can select using the thumbwheel.
  RainSnsrSnvtyForUsrSnvty1: 4, 0, ZCL, RLSM;//Signals used to map the different sensitivity settings that can be selected by the driver to an internal sensitivity setting in the rain sensor. This is used to trim the sensitivity of the different rain sensor settings that the driver can select using the thumbwheel.
  RainSnsrSnvtyForUsrSnvty2: 4, 0, ZCL, RLSM;//Signals used to map the different sensitivity settings that can be selected by the driver to an internal sensitivity setting in the rain sensor. This is used to trim the sensitivity of the different rain sensor settings that the driver can select using the thumbwheel.
  RainSnsrSnvtyForUsrSnvty3: 4, 0, ZCL, RLSM;//Signals used to map the different sensitivity settings that can be selected by the driver to an internal sensitivity setting in the rain sensor. This is used to trim the sensitivity of the different rain sensor settings that the driver can select using the thumbwheel.
  RainSnsrSnvtyForUsrSnvty4: 4, 0, ZCL, RLSM;//Signals used to map the different sensitivity settings that can be selected by the driver to an internal sensitivity setting in the rain sensor. This is used to trim the sensitivity of the different rain sensor settings that the driver can select using the thumbwheel.
  RainSnsrSnvtyForUsrSnvty5: 4, 0, ZCL, RLSM;//Signals used to map the different sensitivity settings that can be selected by the driver to an internal sensitivity setting in the rain sensor. This is used to trim the sensitivity of the different rain sensor settings that the driver can select using the thumbwheel.
  RainSnsrSnvtyForUsrSnvty6: 4, 0, ZCL, RLSM;//Signals used to map the different sensitivity settings that can be selected by the driver to an internal sensitivity setting in the rain sensor. This is used to trim the sensitivity of the different rain sensor settings that the driver can select using the thumbwheel.
  ReAdaptReq: 1, 0, ZCL, RLSM;//Re-adaption request signal
  WindCorrnValAmb: 8, 0, ZCL, RLSM;//Three different windscreen correction values used to calibrate the light sensors in the rain sensor for different windscreen types. Windscreen correction value ambient light sensor
  WindCorrnValFrnt: 8, 0, ZCL, RLSM;//Three different windscreen correction values used to calibrate the light sensors in the rain sensor for different windscreen types. Windscreen correction value front light sensor
  WindCorrnValHud: 8, 0, ZCL, RLSM;//Three different windscreen correction values used to calibrate the light sensors in the rain sensor for different windscreen types. Windscreen correction value HUD sensor
  WiprActv: 1, 0, ZCL, RLSM;//Information regarding if the wiper is active or not.
  WiprInPrkgPosnLo: 1, 0, ZCL, RLSM;//Wiper position on the windscreen. Wiper lever in, below or above extended parking position
  WiprInWipgAr: 1, 0, ZCL, RLSM;//Information regarding wiper position on the windscreen.
  WshngCycActv: 1, 0, ZCL, RLSM;//Off, No front washing cycle active. On, Front washing active (pre-washing, washing or afterwiping)
  IntrMirrDimCmdDrvrSide: 1, 0, ZCL, RMD;//na
  IntrMirrDimCmdIntrMirrAsyFanCmpMag: 1, 0, ZCL, RMD;//na
  IntrMirrDimCmdIntrMirrDiagcRst: 1, 1, ZCL, RMD;//na
  IntrMirrDimCmdIntrMirrDimSnvty: 2, 0, ZCL, RMD;//na
  IntrMirrDimCmdIntrMirrEna: 1, 0, ZCL, RMD;//na
  IntrMirrDimCmdIntrMirrInhbDim: 1, 0, ZCL, RMD;//interior mirror dimming inhibition command
  IntrMirrDimCmdIntrMirrWindHeatrCmpMag: 1, 0, ZCL, RMD;//na
  StreamingMirrBriAdjmt: 4, 10, ZCL, RMD;//Manually adjust the brightness of the streaming media rearview mirror
  StreamingMirrImgAdjmt: 4, 0, ZCL, RMD;//Rearview mirror streaming image zoom
  StreamingMirrPosnAdjmt: 4, 3, ZCL, RMD;//Streaming media rearview mirror position adjustment
  StreamingMirrSwt: 2, 1, ZCL, RMD;//Turn on or off the streaming media rearview mirror.
}

Diagnostic_signals {
  MasterReqB0: 8, 0 ;
  MasterReqB1: 8, 0 ;
  MasterReqB2: 8, 0 ;
  MasterReqB3: 8, 0 ;
  MasterReqB4: 8, 0 ;
  MasterReqB5: 8, 0 ;
  MasterReqB6: 8, 0 ;
  MasterReqB7: 8, 0 ;
  SlaveRespB0: 8, 0 ;
  SlaveRespB1: 8, 0 ;
  SlaveRespB2: 8, 0 ;
  SlaveRespB3: 8, 0 ;
  SlaveRespB4: 8, 0 ;
  SlaveRespB5: 8, 0 ;
  SlaveRespB6: 8, 0 ;
  SlaveRespB7: 8, 0 ;
}
Frames {
  IRMMZCLLin5Frame01: 0x29, RMD, 8 {
    IntrMirrDimRespIntrMirrDimPerc, 0 ;
    IntrMirrDimRespIntrMirrIntFailr, 8 ;
    IntrMirrDimRespResdBoolean, 9 ;
    IntrMirrDimRespResdUInt6, 10 ;
  }
  IRMMZCLLin5PartNrFr01: 0xB, RMD, 8 {
    IRMMRMDPartNoCmplEndSgn1, 0 ;
    IRMMRMDPartNoCmplEndSgn2, 8 ;
    IRMMRMDPartNoCmplEndSgn3, 16 ;
    IRMMRMDPartNoCmplNr1, 24 ;
    IRMMRMDPartNoCmplNr2, 32 ;
    IRMMRMDPartNoCmplNr3, 40 ;
    IRMMRMDPartNoCmplNr4, 48 ;
  }
  IRMMZCLLin5PartNrFr02: 0xC, RMD, 8 {
    IRMMRMDPartNo10CmplEndSgn1, 0 ;
    IRMMRMDPartNo10CmplEndSgn2, 8 ;
    IRMMRMDPartNo10CmplEndSgn3, 16 ;
    IRMMRMDPartNo10CmplNr1, 24 ;
    IRMMRMDPartNo10CmplNr2, 32 ;
    IRMMRMDPartNo10CmplNr3, 40 ;
    IRMMRMDPartNo10CmplNr4, 48 ;
    IRMMRMDPartNo10CmplNr5, 56 ;
  }
  IRMMZCLLin5SerNrFr01: 0xD, RMD, 8 {
    IRMMRMDSerNoNr1, 0 ;
    IRMMRMDSerNoNr2, 8 ;
    IRMMRMDSerNoNr3, 16 ;
    IRMMRMDSerNoNr4, 24 ;
  }
  RLSMZCLLin5Frame02: 0x2, RLSM, 8 {
    LiOprnMod, 5 ;
    RainDetection, 7 ;
    RainfallAmnt, 8 ;
    RainSnsrDiagcRainSnsrHiTDetd, 4 ;
    RainSnsrDiagcRainSnsrHiVoltDetd, 3 ;
    SolarSnsrErr, 0 ;
    SolarSnsrLeValue, 16 ;
    SolarSnsrRiValue, 24 ;
  }
  RLSMZCLLin5Frame03: 0x15, RLSM, 8 {
    AutWinWipgCmd, 0 ;
    ErrRespRLSM, 15 ;
    OutdBriChks8, 40 ;
    OutdBriCntr4, 48 ;
    OutdBriDataID4, 52 ;
    OutdBriSts, 56 ;
    RainSnsrErrCalErr, 9 ;
    RainSnsrErrCalErrActv, 8 ;
    RainSnsrErrRainDetnErr, 10 ;
    RainSnsrErrRainDetnErrActv, 11 ;
    TwliBriRaw1, 26 ;
    TwliBriRawQf, 24 ;
    WipgAutFrntMod, 12 ;
  }
  RLSMZCLLin5Frame08: 0x2C, RLSM, 8 {
    CmptFrntWindDewT, 0 ;
    CmptFrntWindT, 45 ;
    RelHumSnsrErr, 42 ;
    RelHumSnsrRelHum, 56 ;
  }
  RLSMZCLLin5Frame09: 0x23, RLSM, 8 {
    AmbIllmnFwdStsAmblillmn1, 16 ;
    AmbIllmnFwdStsAmblillmn2, 32 ;
    AmbIllmnFwdStsChks8, 0 ;
    AmbIllmnFwdStsCntr4, 8 ;
    AmbIllmnFwdStsDataID4, 12 ;
    HudSnsrErrChks8, 40 ;
    HudSnsrErrCntr4, 48 ;
    HudSnsrErrDataID4, 52 ;
    HudSnsrErrParChk, 56 ;
    HudSnsrErrSnsrErr, 57 ;
  }
  RLSMZCLLin5Frame10: 0x24, RLSM, 8 {
    AMBStsAmbLiIllmn, 16 ;
    AMBStsChks8, 0 ;
    AMBStsCntr4, 8 ;
    AMBStsDataID4, 12 ;
    FWStsAmbLiIllmn, 48 ;
    FWStsChks8, 32 ;
    FWStsCntr4, 40 ;
    FWStsDataID4, 44 ;
  }
  RLSMZCLLin5PartNrFr01: 0x20, RLSM, 7 {
    RLSMPartNoCmplEndSgn1, 32 ;
    RLSMPartNoCmplEndSgn2, 40 ;
    RLSMPartNoCmplEndSgn3, 48 ;
    RLSMPartNoCmplNr1, 0 ;
    RLSMPartNoCmplNr2, 8 ;
    RLSMPartNoCmplNr3, 16 ;
    RLSMPartNoCmplNr4, 24 ;
  }
  RLSMZCLLin5PartNrFr02: 0x18, RLSM, 8 {
    RLSMPartNo10CmplEndSgn1, 40 ;
    RLSMPartNo10CmplEndSgn2, 48 ;
    RLSMPartNo10CmplEndSgn3, 56 ;
    RLSMPartNo10CmplNr1, 0 ;
    RLSMPartNo10CmplNr2, 8 ;
    RLSMPartNo10CmplNr3, 16 ;
    RLSMPartNo10CmplNr4, 24 ;
    RLSMPartNo10CmplNr5, 32 ;
  }
  RLSMZCLLin5SerNrFr01: 0x22, RLSM, 4 {
    RLSMSerNoNr1, 0 ;
    RLSMSerNoNr2, 8 ;
    RLSMSerNoNr3, 16 ;
    RLSMSerNoNr4, 24 ;
  }
  RMDZCLLin5Frame01: 0x2D, RMD, 8 {
    ErrRespRMD, 63 ;
    StreamingMirrBri, 0 ;
    StreamingMirrEnable, 4 ;
    StreamingMirrImg, 5 ;
    StreamingMirrModeSts, 16 ;
    StreamingMirrPosn, 9 ;
    StreamingMirrReWrnSwtSts, 13 ;
  }
  WMMZCLLin5Frame03: 0x25, WMM, 4 {
    ErrRespWMM, 15 ;
    WiprActvFromWMM, 8 ;
    WiprInPrkgPosnLoFromWMM, 10 ;
    WiprInWipgArFromWMM, 11 ;
    WiprMotCrkAg, 24 ;
    WiprMotDiagcWiprMotHiVoltDetd, 16 ;
    WiprMotDiagcWiprMotLoVoltDetd, 18 ;
    WiprMotDiagcWiprMotOvldDetd, 20 ;
    WiprMotErrSafe, 13 ;
    WshngCycActvFromWMM, 22 ;
  }
  WMMZCLLin5PartNrFr01: 0x8, WMM, 7 {
    WMMPartNoCmplEndSgn1, 32 ;
    WMMPartNoCmplEndSgn2, 40 ;
    WMMPartNoCmplEndSgn3, 48 ;
    WMMPartNoCmplNr1, 0 ;
    WMMPartNoCmplNr2, 8 ;
    WMMPartNoCmplNr3, 16 ;
    WMMPartNoCmplNr4, 24 ;
  }
  WMMZCLLin5PartNrFr02: 0x19, WMM, 8 {
    WMMPartNo10CmplEndSgn1, 40 ;
    WMMPartNo10CmplEndSgn2, 48 ;
    WMMPartNo10CmplEndSgn3, 56 ;
    WMMPartNo10CmplNr1, 0 ;
    WMMPartNo10CmplNr2, 8 ;
    WMMPartNo10CmplNr3, 16 ;
    WMMPartNo10CmplNr4, 24 ;
    WMMPartNo10CmplNr5, 32 ;
  }
  WMMZCLLin5SerNrFr01: 0x28, WMM, 7 {
    WMMSerNoNr1, 0 ;
    WMMSerNoNr2, 8 ;
    WMMSerNoNr3, 16 ;
    WMMSerNoNr4, 24 ;
  }
  ZCLZCLLin5Frame02: 0x5, ZCL, 6 {
    CbnGroupPwrReqforRMD, 22 ;
    RainSensActvn, 12 ;
    StreamingMirrModeSwt, 32 ;
    StreamingMirrReWrnDis, 44 ;
    StreamingMirrReWrnSwt, 15 ;
    VehSpdForWipg, 0 ;
    WipgPwrActvnSafeWipgPwrAcsyModSafe, 8 ;
    WipgPwrActvnSafeWipgPwrDrvgModSafe, 10 ;
    WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe, 16 ;
    WiprMotFrntLvrCmdSafeLvrInIntlPosn, 21 ;
    WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe, 18 ;
    WiprMotFrntLvrCmdSafeLvrInSnglStrokePos, 20 ;
    WiprMotFrntOffsAg, 40 ;
    WiprMotIntlCmd, 24 ;
    WiprPosnForSrvReq, 27 ;
    WshrLvrPosnSafe, 13 ;
  }
  ZCLZCLLin5Frame03: 0x7, ZCL, 7 {
    AmbTForVisy, 0 ;
    VehModMngtGlbSafeCarModSts, 44 ;
    VehModMngtGlbSafeCarModSubtypWdCarModSubtyp, 48 ;
    VehModMngtGlbSafeChks8, 8 ;
    VehModMngtGlbSafeCntr4, 16 ;
    VehModMngtGlbSafeDataID4, 20 ;
    VehModMngtGlbSafeEgyLvlElecMai, 24 ;
    VehModMngtGlbSafeEgyLvlElecSubtyp, 28 ;
    VehModMngtGlbSafeFltEgyCnsWdSts, 47 ;
    VehModMngtGlbSafePwrLvlElecMai, 32 ;
    VehModMngtGlbSafePwrLvlElecSubtyp, 36 ;
    VehModMngtGlbSafePwrModSts, 40 ;
  }
  ZCLZCLLin5Frame04: 0x9, ZCL, 7 {
    RainSnsrLiThd, 44 ;
  }
  ZCLZCLLin5Frame05: 0xA, ZCL, 7 {
    EnaOfflineMonitor, 17 ;
  }
  ZCLZCLLin5Frame06: 0x17, ZCL, 8 {
    RainSnsrSnvtyForUsrSnvty0, 0 ;
    RainSnsrSnvtyForUsrSnvty1, 4 ;
    RainSnsrSnvtyForUsrSnvty2, 8 ;
    RainSnsrSnvtyForUsrSnvty3, 12 ;
    RainSnsrSnvtyForUsrSnvty4, 16 ;
    RainSnsrSnvtyForUsrSnvty5, 20 ;
    RainSnsrSnvtyForUsrSnvty6, 24 ;
    ReAdaptReq, 60 ;
    WindCorrnValAmb, 32 ;
    WindCorrnValFrnt, 40 ;
    WindCorrnValHud, 48 ;
  }
  ZCLZCLLin5Frame07: 0x27, ZCL, 1 {
    WiprActv, 4 ;
    WiprInPrkgPosnLo, 5 ;
    WiprInWipgAr, 6 ;
    WshngCycActv, 7 ;
  }
  ZCLZCLLin5Frame08: 0x2A, ZCL, 8 {
    IntrMirrDimCmdDrvrSide, 2 ;
    IntrMirrDimCmdIntrMirrAsyFanCmpMag, 3 ;
    IntrMirrDimCmdIntrMirrDiagcRst, 4 ;
    IntrMirrDimCmdIntrMirrDimSnvty, 0 ;
    IntrMirrDimCmdIntrMirrEna, 5 ;
    IntrMirrDimCmdIntrMirrInhbDim, 6 ;
    IntrMirrDimCmdIntrMirrWindHeatrCmpMag, 7 ;
  }
  ZCLZCLLin5Frame09: 0x2B, ZCL, 8 {
    StreamingMirrBriAdjmt, 0 ;
    StreamingMirrImgAdjmt, 4 ;
    StreamingMirrPosnAdjmt, 8 ;
    StreamingMirrSwt, 12 ;
  }
}

Diagnostic_frames {
  MasterReq: 60 {
    MasterReqB0, 0 ;
    MasterReqB1, 8 ;
    MasterReqB2, 16 ;
    MasterReqB3, 24 ;
    MasterReqB4, 32 ;
    MasterReqB5, 40 ;
    MasterReqB6, 48 ;
    MasterReqB7, 56 ;
  }
  SlaveResp: 61 {
    SlaveRespB0, 0 ;
    SlaveRespB1, 8 ;
    SlaveRespB2, 16 ;
    SlaveRespB3, 24 ;
    SlaveRespB4, 32 ;
    SlaveRespB5, 40 ;
    SlaveRespB6, 48 ;
    SlaveRespB7, 56 ;
  }
}
Node_attributes {
  RLSM{
    LIN_protocol = "2.1" ;
    configured_NAD = 0x7B ;
    initial_NAD = 0x7B ;
    product_id = 0x0, 0x0,  0 ;
    response_error = ErrRespRLSM ;
	  configurable_frames {
      RLSMZCLLin5Frame02;
      RLSMZCLLin5Frame03;
      RLSMZCLLin5Frame08;
      RLSMZCLLin5Frame09;
      RLSMZCLLin5Frame10;
      RLSMZCLLin5PartNrFr01;
      RLSMZCLLin5PartNrFr02;
      RLSMZCLLin5SerNrFr01;
      ZCLZCLLin5Frame02;
      ZCLZCLLin5Frame03;
      ZCLZCLLin5Frame05;
      ZCLZCLLin5Frame04;
      ZCLZCLLin5Frame06;
      ZCLZCLLin5Frame07;
    }    
  }
  RMD{
    LIN_protocol = "2.1" ;
    configured_NAD = 0x7C ;
    initial_NAD = 0x7C ;
    product_id = 0x0, 0x0,  0 ;
    response_error = ErrRespRMD ;
    configurable_frames {
      RMDZCLLin5Frame01;
      ZCLZCLLin5Frame02;
      ZCLZCLLin5Frame03;
      ZCLZCLLin5Frame08;
      ZCLZCLLin5Frame09;       
      IRMMZCLLin5Frame01; 
      IRMMZCLLin5PartNrFr01;
      IRMMZCLLin5PartNrFr02;
      IRMMZCLLin5SerNrFr01;               
    }    
  }
  WMM{
    LIN_protocol = "2.1" ;
    configured_NAD = 0x12 ;
    initial_NAD = 0x12 ;
    product_id = 0x0, 0x0,  0 ;
    response_error = ErrRespWMM ;
    configurable_frames {      
      WMMZCLLin5Frame03;
      WMMZCLLin5PartNrFr01;
      WMMZCLLin5PartNrFr02;
      WMMZCLLin5SerNrFr01;
      RLSMZCLLin5Frame03;
      ZCLZCLLin5Frame02;
    }     
  }
}

Schedule_tables {
 ZCLLin5ScheduleSerNrPartNr {
    WMMZCLLin5PartNrFr01 delay 10.0 ms ;
    WMMZCLLin5PartNrFr02 delay 15.0 ms ;
    WMMZCLLin5SerNrFr01 delay 10.0 ms ;
    RLSMZCLLin5PartNrFr01 delay 10.0 ms ;
    RLSMZCLLin5PartNrFr02 delay 15.0 ms ;
    RLSMZCLLin5SerNrFr01 delay 10.0 ms ;
    IRMMZCLLin5PartNrFr01 delay 15.000 ms ;
    IRMMZCLLin5PartNrFr02 delay 15.0 ms ;
    IRMMZCLLin5SerNrFr01 delay 15.000 ms ;
  }
 ZCLLin5ScheduleTable01 {
    WMMZCLLin5Frame03 delay 10.0 ms ;
    ZCLZCLLin5Frame07 delay 15.000 ms ;
    RLSMZCLLin5Frame10 delay 15.000 ms ;
    ZCLZCLLin5Frame09 delay 15.0 ms ;
    RMDZCLLin5Frame01 delay 15.0 ms ;
    IRMMZCLLin5Frame01 delay 15.0 ms ;
    ZCLZCLLin5Frame08 delay 15.0 ms ;
    RLSMZCLLin5Frame09 delay 15.0 ms ;
    RLSMZCLLin5Frame02 delay 15.0 ms ;
    RLSMZCLLin5Frame03 delay 15.0 ms ;
    WMMZCLLin5Frame03 delay 10.0 ms ;
    ZCLZCLLin5Frame07 delay 15.0 ms ;
    RLSMZCLLin5Frame10 delay 15.0 ms ;
    ZCLZCLLin5Frame02 delay 10.0 ms ;
    ZCLZCLLin5Frame03 delay 15.0 ms ;
    ZCLZCLLin5Frame04 delay 15.0 ms ;
    ZCLZCLLin5Frame05 delay 15.0 ms ;
    ZCLZCLLin5Frame06 delay 15.0 ms ;
    RLSMZCLLin5Frame08 delay 15.0 ms ;
  }
}

Signal_encoding_types {
  Perc5 {
    physical_value, 0, 255, 0.39215686274509803, 0.0, "%" ;
  }
  FailrNoFailr1 {
    logical_value, 0, "FailrNoFailr1_NoFailr" ;
    logical_value, 1, "FailrNoFailr1_Failr" ;
  }
  Boolean {
    logical_value, 0, "Boolean_FALSE" ;
    logical_value, 1, "Boolean_TRUE" ;
  }
  UInt6 {
    physical_value, 0, 63, 1, 0, "Unitless" ;
  }
  UInt8 {
    physical_value, 0, 255, 1, 0, "Unitless" ;
  }
  LiOperMod {
    logical_value, 0, "LiOperMod_Night" ;
    logical_value, 1, "LiOperMod_Day" ;
    logical_value, 2, "LiOperMod_Twli" ;
    logical_value, 3, "LiOperMod_Tnl" ;
  }
  AmntSnsr {
    logical_value, 0, "AmntSnsr_Amnt0" ;
    logical_value, 1, "AmntSnsr_Amnt1" ;
    logical_value, 2, "AmntSnsr_Amnt2" ;
    logical_value, 3, "AmntSnsr_Amnt3" ;
    logical_value, 4, "AmntSnsr_Amnt4" ;
    logical_value, 5, "AmntSnsr_Amnt5" ;
    logical_value, 6, "AmntSnsr_Amnt6" ;
    logical_value, 7, "AmntSnsr_Amnt7" ;
    logical_value, 8, "AmntSnsr_Amnt8" ;
    logical_value, 9, "AmntSnsr_Amnt9" ;
    logical_value, 10, "AmntSnsr_Amnt10" ;
    logical_value, 11, "AmntSnsr_Amnt11" ;
    logical_value, 12, "AmntSnsr_Amnt12" ;
    logical_value, 13, "AmntSnsr_Amnt13" ;
    logical_value, 14, "AmntSnsr_InitValue" ;
    logical_value, 15, "AmntSnsr_Error" ;
  }
  OnOff1 {
    logical_value, 0, "OnOff1_Off" ;
    logical_value, 1, "OnOff1_On" ;
  }
  Err1 {
    logical_value, 0, "Err1_NoErr" ;
    logical_value, 1, "Err1_Err" ;
  }
  Sunload2D {
    physical_value, 0, 255, 5.0, 0.0, "W/m2" ;
  }
  WipgSpd2 {
    logical_value, 0, "WipgSpd2_WipgSpd0Rpm" ;
    logical_value, 1, "WipgSpd2_WipgSpd40Rpm" ;
    logical_value, 2, "WipgSpd2_WipgSpd43Rpm" ;
    logical_value, 3, "WipgSpd2_WipgSpd46Rpm" ;
    logical_value, 4, "WipgSpd2_WipgSpd50Rpm" ;
    logical_value, 5, "WipgSpd2_WipgSpd54Rpm" ;
    logical_value, 6, "WipgSpd2_WipgSpd57Rpm" ;
    logical_value, 7, "WipgSpd2_WipgSpd60Rpm" ;
  }
  UInt4 {
    physical_value, 0, 15, 1, 0, "Unitless" ;
  }
  OutdBriSts {
    logical_value, 0, "OutdBriSts_Ukwn" ;
    logical_value, 1, "OutdBriSts_Night" ;
    logical_value, 2, "OutdBriSts_Day" ;
    logical_value, 3, "OutdBriSts_Invld" ;
  }
  TwliBriRaw1 {
    physical_value, 0, 10000, 1, 0, "Unitless" ;
  }
  GenQf1 {
    logical_value, 0, "GenQf1_UndefindDataAccur" ;
    logical_value, 1, "GenQf1_TmpUndefdData" ;
    logical_value, 2, "GenQf1_DataAccurNotWithinSpcn" ;
    logical_value, 3, "GenQf1_AccurData" ;
  }
  WipgAutFrntMod {
    logical_value, 0, "WipgAutFrntMod_Off" ;
    logical_value, 1, "WipgAutFrntMod_ImdtMod" ;
    logical_value, 2, "WipgAutFrntMod_IntlMod" ;
    logical_value, 3, "WipgAutFrntMod_ContnsMod" ;
  }
  TByCmptmtSnsrRelHum {
    physical_value, 0, 2047, 0.1, -40.0, "degC" ;
  }
  Perc2 {
    physical_value, 0, 200, 0.5, 0.0, "%" ;
  }
  Liner32 {
    physical_value, 0, 511, 1.0, 0.0, "Unitless" ;
  }
  MvBattSwVersAct {
    physical_value, 0, 255, 1.0, 0.0, "Unitless" ;
  }
  ParChks1 {
    logical_value, 0, "ParChks1_Unevennrof1" ;
    logical_value, 1, "ParChks1_Evennrof1" ;
  }
  FltStsSlaveBasc {
    logical_value, 0, "FltStsSlaveBasc_FltStsTestPassd" ;
    logical_value, 1, "FltStsSlaveBasc_FltStsTestFaild" ;
  }
  LiIllmn {
    physical_value, 0, 65534, 1, 0, "Unitless" ;
  }
  WipAg {
    physical_value, 0, 255, 1, 0, "Unitless" ;
  }
  OnOffCrit1 {
    logical_value, 0, "OnOffCrit1_NotVld1" ;
    logical_value, 1, "OnOffCrit1_Off" ;
    logical_value, 2, "OnOffCrit1_On" ;
    logical_value, 3, "OnOffCrit1_NotVld2" ;
  }
  OnOffNoReq {
    logical_value, 0, "OnOffNoReq_NoReq" ;
    logical_value, 1, "OnOffNoReq_On" ;
    logical_value, 2, "OnOffNoReq_Off" ;
  }
  WipAgOffs {
    physical_value, 0, 15, 1, 0, "Deg" ;
  }
  WipgSpdIntlFromHmi {
    logical_value, 0, "WipgSpdIntlFromHmi_Posn0" ;
    logical_value, 1, "WipgSpdIntlFromHmi_Posn1" ;
    logical_value, 2, "WipgSpdIntlFromHmi_Posn2" ;
    logical_value, 3, "WipgSpdIntlFromHmi_Posn3" ;
    logical_value, 4, "WipgSpdIntlFromHmi_Posn4" ;
    logical_value, 5, "WipgSpdIntlFromHmi_Posn5" ;
    logical_value, 6, "WipgSpdIntlFromHmi_Posn6" ;
    logical_value, 7, "WipgSpdIntlFromHmi_Posn7" ;
  }
  CarModSts1 {
    logical_value, 0, "CarModSts1_CarModNorm" ;
    logical_value, 1, "CarModSts1_CarModTrnsp" ;
    logical_value, 2, "CarModSts1_CarModFcy" ;
    logical_value, 3, "CarModSts1_CarModCrash" ;
    logical_value, 5, "CarModSts1_CarModDyno" ;
  }
  Nr5 {
    physical_value, 0, 7, 1, 0, "Unitless" ;
  }
  FltEgyCns1 {
    logical_value, 0, "FltEgyCns1_NoFlt" ;
    logical_value, 1, "FltEgyCns1_Flt" ;
  }
  PowerModeState {
    logical_value, 2, "PowerModeState_IDLE" ;
    logical_value, 4, "PowerModeState_ACTIVE" ;
    logical_value, 8, "PowerModeState_DRIVING" ;
  }
  RainSnsrThd {
    physical_value, 0, 15, 5.0, -40.0, "%" ;
  }
  RiLeTyp {
    logical_value, 0, "RiLeTyp_Right" ;
    logical_value, 1, "RiLeTyp_Left" ;
  }
  Flg1 {
    logical_value, 0, "Flg1_Rst" ;
    logical_value, 1, "Flg1_Set" ;
  }
  IntrMirrDimSnvtyTyp {
    logical_value, 0, "IntrMirrDimSnvtyTyp_Normal" ;
    logical_value, 1, "IntrMirrDimSnvtyTyp_Dark" ;
    logical_value, 2, "IntrMirrDimSnvtyTyp_Light" ;
    logical_value, 3, "IntrMirrDimSnvtyTyp_Inhibit" ;
  }
}

Signal_representation {
  RiLeTyp: IntrMirrDimCmdDrvrSide ;
  WipAgOffs: WiprMotFrntOffsAg ;
  WipgSpd2: AutWinWipgCmd ;
  Err1: SolarSnsrErr ,RelHumSnsrErr ;
  Nr5: VehModMngtGlbSafeCarModSubtypWdCarModSubtyp ;
  UInt8: IRMMRMDPartNoCmplEndSgn1 ,IRMMRMDPartNoCmplEndSgn2 ,IRMMRMDPartNoCmplEndSgn3 ,IRMMRMDPartNoCmplNr1 ,IRMMRMDPartNoCmplNr2 ,IRMMRMDPartNoCmplNr3 ,IRMMRMDPartNoCmplNr4 ,IRMMRMDPartNo10CmplEndSgn1 ,IRMMRMDPartNo10CmplEndSgn2 ,IRMMRMDPartNo10CmplEndSgn3 ,IRMMRMDPartNo10CmplNr1 ,IRMMRMDPartNo10CmplNr2 ,IRMMRMDPartNo10CmplNr3 ,IRMMRMDPartNo10CmplNr4 ,IRMMRMDPartNo10CmplNr5 ,IRMMRMDSerNoNr1 ,IRMMRMDSerNoNr2 ,IRMMRMDSerNoNr3 ,IRMMRMDSerNoNr4 ,OutdBriChks8 ,AmbIllmnFwdStsChks8 ,HudSnsrErrChks8 ,AMBStsChks8 ,FWStsChks8 ,RLSMPartNoCmplEndSgn1 ,RLSMPartNoCmplEndSgn2 ,RLSMPartNoCmplEndSgn3 ,RLSMPartNoCmplNr1 ,RLSMPartNoCmplNr2 ,RLSMPartNoCmplNr3 ,RLSMPartNoCmplNr4 ,RLSMPartNo10CmplEndSgn1 ,RLSMPartNo10CmplEndSgn2 ,RLSMPartNo10CmplEndSgn3 ,RLSMPartNo10CmplNr1 ,RLSMPartNo10CmplNr2 ,RLSMPartNo10CmplNr3 ,RLSMPartNo10CmplNr4 ,RLSMPartNo10CmplNr5 ,RLSMSerNoNr1 ,RLSMSerNoNr2 ,RLSMSerNoNr3 ,RLSMSerNoNr4 ,WMMPartNoCmplEndSgn1 ,WMMPartNoCmplEndSgn2 ,WMMPartNoCmplEndSgn3 ,WMMPartNoCmplNr1 ,WMMPartNoCmplNr2 ,WMMPartNoCmplNr3 ,WMMPartNoCmplNr4 ,WMMPartNo10CmplEndSgn1 ,WMMPartNo10CmplEndSgn2 ,WMMPartNo10CmplEndSgn3 ,WMMPartNo10CmplNr1 ,WMMPartNo10CmplNr2 ,WMMPartNo10CmplNr3 ,WMMPartNo10CmplNr4 ,WMMPartNo10CmplNr5 ,WMMSerNoNr1 ,WMMSerNoNr2 ,WMMSerNoNr3 ,WMMSerNoNr4 ,VehSpdForWipg ,AmbTForVisy ,VehModMngtGlbSafeChks8 ,WindCorrnValAmb ,WindCorrnValFrnt ,WindCorrnValHud ;
  Liner32: AmbIllmnFwdStsAmblillmn1 ;
  FailrNoFailr1: IntrMirrDimRespIntrMirrIntFailr ;
  Perc5: IntrMirrDimRespIntrMirrDimPerc ;
  OnOffCrit1: WiprMotDiagcWiprMotHiVoltDetd ,WiprMotDiagcWiprMotLoVoltDetd ,WiprMotDiagcWiprMotOvldDetd ,WiprMotErrSafe ,WipgPwrActvnSafeWipgPwrAcsyModSafe ,WipgPwrActvnSafeWipgPwrDrvgModSafe ,WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe ,WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe ,WshrLvrPosnSafe ;
  LiOperMod: LiOprnMod ;
  Perc2: RelHumSnsrRelHum ;
  Flg1: IntrMirrDimCmdIntrMirrDiagcRst ;
  OnOff1: RainSnsrDiagcRainSnsrHiTDetd ,RainSnsrDiagcRainSnsrHiVoltDetd ,RainSnsrErrCalErr ,RainSnsrErrCalErrActv ,RainSnsrErrRainDetnErr ,RainSnsrErrRainDetnErrActv ,StreamingMirrEnable ,StreamingMirrReWrnSwtSts ,WiprActvFromWMM ,WiprInPrkgPosnLoFromWMM ,WiprInWipgArFromWMM ,WshngCycActvFromWMM ,RainSensActvn ,StreamingMirrReWrnSwt ,WiprMotFrntLvrCmdSafeLvrInIntlPosn ,WiprMotFrntLvrCmdSafeLvrInSnglStrokePos ,WiprPosnForSrvReq ,ReAdaptReq ,WiprActv ,WiprInPrkgPosnLo ,WiprInWipgAr ,WshngCycActv ,IntrMirrDimCmdIntrMirrAsyFanCmpMag ,IntrMirrDimCmdIntrMirrEna ,IntrMirrDimCmdIntrMirrInhbDim ,IntrMirrDimCmdIntrMirrWindHeatrCmpMag ;
  OnOffNoReq: CbnGroupPwrReqforRMD ,StreamingMirrSwt ;
  TByCmptmtSnsrRelHum: CmptFrntWindDewT ,CmptFrntWindT ;
  ParChks1: HudSnsrErrParChk ;
  CarModSts1: VehModMngtGlbSafeCarModSts ;
  LiIllmn: AMBStsAmbLiIllmn ,FWStsAmbLiIllmn ;
  PowerModeState: VehModMngtGlbSafePwrModSts ;
  UInt4: OutdBriCntr4 ,OutdBriDataID4 ,AmbIllmnFwdStsCntr4 ,AmbIllmnFwdStsDataID4 ,HudSnsrErrCntr4 ,HudSnsrErrDataID4 ,AMBStsCntr4 ,AMBStsDataID4 ,FWStsCntr4 ,FWStsDataID4 ,StreamingMirrBri ,StreamingMirrImg ,StreamingMirrModeSts ,StreamingMirrPosn ,StreamingMirrModeSwt ,StreamingMirrReWrnDis ,VehModMngtGlbSafeCntr4 ,VehModMngtGlbSafeDataID4 ,VehModMngtGlbSafeEgyLvlElecMai ,VehModMngtGlbSafeEgyLvlElecSubtyp ,VehModMngtGlbSafePwrLvlElecMai ,VehModMngtGlbSafePwrLvlElecSubtyp ,RainSnsrSnvtyForUsrSnvty0 ,RainSnsrSnvtyForUsrSnvty1 ,RainSnsrSnvtyForUsrSnvty2 ,RainSnsrSnvtyForUsrSnvty3 ,RainSnsrSnvtyForUsrSnvty4 ,RainSnsrSnvtyForUsrSnvty5 ,RainSnsrSnvtyForUsrSnvty6 ,StreamingMirrBriAdjmt ,StreamingMirrImgAdjmt ,StreamingMirrPosnAdjmt ;
  WipgSpdIntlFromHmi: WiprMotIntlCmd ;
  WipAg: WiprMotCrkAg ;
  Boolean: IntrMirrDimRespResdBoolean ,RainDetection ,EnaOfflineMonitor ;
  OutdBriSts: OutdBriSts ;
  TwliBriRaw1: TwliBriRaw1 ;
  AmntSnsr: RainfallAmnt ;
  UInt6: IntrMirrDimRespResdUInt6 ;
  RainSnsrThd: RainSnsrLiThd ;
  WipgAutFrntMod: WipgAutFrntMod ;
  Sunload2D: SolarSnsrLeValue ,SolarSnsrRiValue ;
  GenQf1: TwliBriRawQf ;
  MvBattSwVersAct: AmbIllmnFwdStsAmblillmn2 ;
  FltEgyCns1: VehModMngtGlbSafeFltEgyCnsWdSts ;
  FltStsSlaveBasc: HudSnsrErrSnsrErr ;
  IntrMirrDimSnvtyTyp: IntrMirrDimCmdIntrMirrDimSnvty ;
}

