/*
 * @file      load_tbl.h
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:50 created
 * $Id: load_tbl.h 1.1 $
 */

#ifndef LOAD_TBL_H_
#define LOAD_TBL_H_

void load_tbl_p0(unsigned char  *tbl, unsigned int size);
void load_tbl_p1(unsigned char  *tbl, unsigned int size);
void load_tbl_p2(unsigned char  *tbl, unsigned int size);
void load_tbl_p3(unsigned char  *tbl, unsigned int size);
void load_tbl_vd(unsigned char  *tbl, unsigned int size);
void load_tbl_cq(unsigned char  *tbl, unsigned int size);
void load_tbl_cq_ptr(unsigned char ptr, unsigned char  *tbl, unsigned int size);

#endif /* LOAD_TBL_H_ */
