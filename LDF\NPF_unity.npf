/*** GENERAL DEFINITION ***/
LIN_node_config_file;

/*** MCU DEFINITION ***/
mcu {
   mcu_name                      = SKEAZ1284;
   bus_clock                     = 24000000;                 /* Frequency bus of system Hz*/
   xgate_support                 = no;                       /* Support XGATE Co-Processor */
}

/*** <PERSON><PERSON> HARDWARE DEFINITION ***/
/* Uart config */
uart {
   k_uart{
      uart_channel               = 2;
   }
}

/*** NETWORK DEFINITION ***/
network {
   idle_timeout                  = 5 s;
   time_period                   = 500 us;
   diagnostic_class              = 1;
   resynchronization_support     = no;
   autobaud_support              = no;
   max_message_length            = 6;
   LI0 {
      node                       = RMD;                      /* Name of node described in LDF (must check consistence with L<PERSON>) */
      file                       = "ZSDB225100_EX1H_High_ZCL_LIN5_V5.ldf"; /* Name of LDF file */
      device                     = k_uart;
      support_sid {
         READ_BY_IDENTIFIER      = 0xb2;
         ASSIGN_FRAME_ID_RANGE   = 0xb7;
         ASSIGN_NAD              = 0xb0;
         CONDITIONAL_CHANGE_NAD  = 0xb3;
         SAVE_CONFIGURATION      = 0xb6;
      }
   }
}

