/**
* @file		<FILE>
* <AUTHOR>
* @date		<COMMITTERDATE>
* @version	<BRANCH>
*/

#ifndef SPI_GENERAL_FLASH_H_
#define SPI_GENERAL_FLASH_H_

#include "tp6823.h"

#define SNOR_QER_BY_DETECT_ID

/* enabling Quad I/O */
#define SPIIF_FIX_MFID	    0   // support all known compatible SPI Flash
//#define SPIIF_FIX_MFID	    MXIC_MFID
//#define SPIIF_FIX_MFID	    ISSI_MFID
//#define SPIIF_FIX_MFID      WINBOND_MFID
//#define SPIIF_FIX_MFID	    GD_MFID
//#define SPIIF_FIX_MFID	    EON_MFID

/* to achieve max SPI clock specified by SPI Flash */
#define SPIIF_FIX_DEVID     0   // support all known compatible SPI Flash
//#define SPIIF_FIX_DEVID     MX25L3233F_ID
//#define SPIIF_FIX_DEVID     MX25L12845G_ID
//#define SPIIF_FIX_DEVID     IS25LP256D_ID
// ...
//#define SPIIF_FIX_DEVID     GD25Q32C_ID
//#define SPIIF_FIX_DEVID     S25FL064L_ID
//#define SPIIF_FIX_DEVID     EN25QH64A_ID

/*
 *  */

/* Enable
 * Performance Enhance Mode (MXIC)/Continuous Read Mode (Winbond)/AX Read Mode (ISSI) */
#define SPIIF_EN_XIP_MODE

/* Enable
 * QPI Mode */
//#define SPIIF_EN_QPI_MODE

/* Enable
 * 4Byte Address Mode */
//#define SPIIF_EN_4B_ADDR

/* Enable
 * Soft Start Mode for some Flash require longer periods of CS# Setup Time */
//#define SPIIF_EN_SOFTSTART

/* Enable
 * DTR Mode */
//#define SPIIF_EN_DTR_MODE

// Configures for SPI Delays & Eye_Det width
// Page0: 0x86, 0x8B, 0x8C,
#if 0
unsigned char spi_delays_table[] = {
	0x12,	// SPI_i_Bias= 1 | SPI_i_Delay= 1
	0xE5,	// Eye_Result | Eye_Det_En | Eye_Auto
	0x10	// EyeX_Width_Sel= 0 | SPIDX_Delay= 10h
};
#define SPI_DELAYS_TABLE_SIZE   sizeof(spi_delays_table)
// Configures for SPI Fetch
// Page0: 0x8D, 0x8E, 0x8F,
unsigned char spi_fetch_table[] = {
	0xEB,	// Fetch Instruction
    0x22,   // 6 Dummy Cycle | XIP mode
	0x32,	// SPI+4i4o
};
#define SPI_FETCH_TABLE_SIZE   sizeof(spi_fetch_table)
// Configures for SPI Fetch
// Page3: 0x98, 0x99, 0x9A, 0x9B, 0x9C,
unsigned char code spll_dividers_table[] = {
	0x26, 0x01, 0x01, 0x88, 0x48,	// 120.00MHz
};
#endif
#endif  /* SPI_GENERAL_FLASH_H_ */
