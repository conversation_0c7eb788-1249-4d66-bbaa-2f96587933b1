<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<!-- Copyright 2019 NXP -->
<sdks>
	<variables>
		<variable name="TEMPLATE_PATH" value="tpl.keax.1_0_0.xml"/>
		<variable name="BASE_PATH" value="${KEAx_v1.0.0}"/>
		<variable name="SDK_VERSION" value="1.0.0"/>
	</variables>
	<sdk>
		<variables>
			<variable name="DEVICES" value="SKEAZ128::,SKEAZ64::"/>
			<variable name="NAME" value="KEAZ128"/>
		</variables>
		<template id="t1" name="TEMPLATE" path="$[TEMPLATE_PATH]"/>
	</sdk>
	<sdk>
		<variables>
			<variable name="DEVICES" value="SKEAZN64::,SKEAZN32::,SKEAZN16::"/>
			<variable name="NAME" value="KEAZN64"/>
		</variables>
		<template id="t1" name="TEMPLATE" path="$[TEMPLATE_PATH]"/>
	</sdk>
</sdks>
