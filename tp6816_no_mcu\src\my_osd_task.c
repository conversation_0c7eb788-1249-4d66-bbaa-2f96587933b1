

/*
 * @file	  my_osd_task.c
 * @brief
 * <AUTHOR>
 * @date	  2020-06-12 12:59:55 created
 * $Id: my_osd_task.c 1.1 $
 */
#include "config.h"
#include "tp6806.h"
#include "osd_func.h"
#include "cmd_queue.h"
#include "my_osd_task.h"
#include "mi2c.h"
#include "test12_res.h"
#include "state.h"
#include "gpio.h"
#include "tick.h"
#include "app_key_task.h"
#include "para.h"
#include "timing_det.h"
#include "type.h"

#include "power.h"
#include "misc_task.h"  // 添加misc_task.h头文件以使用摄像头连接异常显示相关函数

static uint16_t osd_task_tick, video_check_tick;
static uint16_t osd_polling_time_tick;

uint8_t  osd_event_flag = 0; //osd�¼�
uint16_t  osd_err_mask = 0;

i2c_send_data  I2c_send_data;
i2c_cmd I2c_cmd;
static uint8_t err_timer = 0;
static uint8_t err_steps = 0;
static uint16_t  osd_polling_time = 0;

extern int  video_detection_time; //��ʱ�����Ƶʱ��
//extern uint8_t  re_buffer[MAX_RE_BUF_SIZE]; //���ͻ���
uint8_t i2c_send_data_flag;
extern int  osd_time;

typedef uint8_t(*osd_fun) (void);

#define MAX_DISP_ITEM			5



extern uint8_t getOverHeatFg(void);
extern void  mpq3367_disable(void);
extern void  mpq3367_enable(void);

void show_osd_menu(void)
{
	//show_page_0_page();
	//show_page_1_page();
	//show_page_2_page();
	//show_page_3_page();
	//show_page_4_page();
	//show_main_page(); 		 //��ʾ��ҳ��
	// reset_radar_alpha();
	//	auto_run_gline(1);
	//	  osd_menu_enable();
}


void demo_osd_init(void)
{
#if defined(SEL_LVDS_1024x600_PANEL)
	osd_original_x(0x60);
	osd_original_y(0x30);
#elif defined(SEL_LVDS_1600x480_PANEL)
	osd_original_x(400);
#elif defined(SEL_DLVDS_1920x720_PANEL)
	osd_original_x(0x230);
	osd_original_y(0x78);
#elif defined(SEL_LVDS_1280x800_PANEL)
	osd_original_x(0xF0);
	osd_original_y(0xA0);
#endif

	//get_demo_res_version();
	get_test12_res_version();

	//show_osd_logo();	 // ������ʾloge
	//osd_strength(0x3E); 							// 98% strength

	//osd_strength(0x1E);	// 98% strength
	show_osd_menu();								//��ʾ��ҳ��

	//osd_auto_run_en(1);

#if (LIN_TP_VERSION_QUERY == 1)
	extern uint8_t lin_tp_version;
	extern unsigned char  tp_version[12];
	lin_tp_version = get_lin_version_tp(tp_version);
#endif

}

#if (USE_API_CODE_MIRROR_REWRN == 1)
static uint16_t osd_warning_time_tick;
uint8_t get_StreamingMirrReWrnDis(void);
uint8_t get_StreamingMirrReWnSwt(void);
static uint8_t g_disp_closed_warning_flag = 0;
static uint8_t g_disp_closed_warning_osd_flag = 0;
static uint8_t g_streamingmirrrewnswt = 0;
static uint8_t g_streamingmirrrewrndis = 0;
static uint8_t g_streamingmirrrewrndis_pre = 0;
uint8_t disp_closed_warning(void)
{
	if (get_StreamingMirrReWnSwt() == 1)
	{
		if (g_streamingmirrrewnswt == 0)
		{
			//warning ��һ�����ã���ʼ��
			g_disp_closed_warning_flag = 1;
			g_streamingmirrrewrndis = 0;
			g_streamingmirrrewrndis_pre = 0;
		}
		g_streamingmirrrewnswt = 1;
		g_streamingmirrrewrndis = get_StreamingMirrReWrnDis();
		if (g_streamingmirrrewrndis !=g_streamingmirrrewrndis_pre)
		{
			//�ж�warning��־
			DEBUG_LOGI("disp_closed_dis: %bd\r\n", g_streamingmirrrewrndis);
			if(g_streamingmirrrewrndis == 1)
				show_page_6_page();
			else if (g_streamingmirrrewrndis == 2)
				show_page_5_page();
			g_disp_closed_warning_flag = 1;
			g_streamingmirrrewrndis_pre = g_streamingmirrrewrndis;
		}
		else
		{
#if USE_API_CODE_MIRROR_REWRN_FLICKING
			g_disp_closed_warning_flag = g_disp_closed_warning_flag ==0 ? 1:0;
#endif
		}
		if (g_disp_closed_warning_flag ==1)
		{
			if (g_streamingmirrrewrndis == 0)
			{
				mark_a(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
				mark_b(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
				mark_c(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
				mark_d(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			}
			else
			{
			mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0xa1);
			mark_b(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0xa1);
			mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0xa1);
			mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0xa1);
			}
		}
		else
		{
			mark_a(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_b(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_c(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_d(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
		}
	}
	else
	{
		if (g_streamingmirrrewnswt == 1)
		{
			mark_a(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_b(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_c(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_d(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			g_streamingmirrrewnswt = 0;
		}
	}
	return 1;
}
#endif
#if (USE_LCM_FLIP == 1)
uint8_t disp_bright()
{
	DEBUG_LOGI("disp_bright: %bd\r\n", MirrPara.vedio_para.StreamingMirrBriAdjmt);
	show_page_0_page();

	// reset_radar_alpha();

	switch (MirrPara.vedio_para.StreamingMirrBriAdjmt)
	{
	case 1:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x18, 0xa1);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x18, 0xa1);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 2:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x18, 0xa1);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xFA, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 3:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xFA, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xFA, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 4:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xFA, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xDC, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 5:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xDC, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xDC, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 6:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xDC, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xBE, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 7:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xBE, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xBE, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 8:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xBE, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xA0, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 9:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xA0, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xA0, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 10:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0xA0, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x82, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 11:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x82, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x82, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 12:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x82, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x64, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 13:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x64, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x64, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 14:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x64, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x46, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 15:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x46, 0xa0);
		mark_c(0xf0, 0x0, 0x00, 0x0, 0x7f, 0x7, 0x46, 0xa0);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	default:
		return 1;
	}

	return 1;
}

uint8_t disp_view()
{
	DEBUG_LOGI("disp_view\r\n");
	I2c_send_data.view = I2c_cmd.view;
	i2c_send_data_flag = 1;
	show_page_2_page();
	switch (MirrPara.vedio_para.StreamingMirrPosnAdjmt)
	{
	case 0:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x80, 0x7, 0x0E, 0xa1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 1:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x80, 0x7, 0xF0, 0xa0);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 2:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x80, 0x7, 0xD2, 0xa0);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 3:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x80, 0x7, 0xB4, 0xa0);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 4:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x80, 0x7, 0x96, 0xa0);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 5:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x80, 0x7, 0x78, 0xa0);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 6:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x80, 0x7, 0x5A, 0xa0);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	default:
		return 1;
	}

	return 1;
}


uint8_t disp_angle()
{
	DEBUG_LOGI("disp_angle\r\n");
	I2c_send_data.visual_angle = I2c_cmd.visual_angle;
	i2c_send_data_flag = 1;
	show_page_1_page();

	switch (MirrPara.vedio_para.StreamingMirrImgAdjmt)
	{
	case 0:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x80, 0x7, 0xf0, 0xa0);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 1:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0xf0, 0x0, 0x00, 0x0, 0x80, 0x7, 0xb4, 0xa0);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 2:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	default:
		break;
	}

	return 1;
}
#else
uint8_t disp_bright()
{
	DEBUG_LOGI("disp_bright: %bd\r\n", MirrPara.vedio_para.StreamingMirrBriAdjmt);
	show_page_0_page();

	// reset_radar_alpha();

	switch (MirrPara.vedio_para.StreamingMirrBriAdjmt)
	{
	case 1:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0x7f, 0x5, 0x5f, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
		mark_c(0x7f, 0x5, 0x5f, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 2:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0x7f, 0x5, 0x5f, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
		mark_c(0x7f, 0x5, 0x83, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 3:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0x7f, 0x5, 0x83, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
		mark_c(0x7f, 0x5, 0x83, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 4:
		mark_a(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		mark_b(0x7f, 0x5, 0x83, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
		mark_c(0x7f, 0x5, 0x8f, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
		mark_d(0x7f, 0x7, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
		break;

	case 5:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0x90, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0x90, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 6:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0x90, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0xc0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 7:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xc0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0xc0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 8:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xc0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0xd0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 9:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xd0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0xd0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 10:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xd0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0xf0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 11:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xf0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0xf0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 12:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xf0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0x10, 0x1, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 13:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0x10, 0x1, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0x10, 0x1, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 14:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0x10, 0x1, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0x80, 0x1, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 15:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0x80, 0x1, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x5, 0x80, 0x1, 0x80, 0x7, 0x80, 0xa1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	default:
		return 1;
	}

	return 1;
}


uint8_t disp_view()
{
	DEBUG_LOGI("disp_view\r\n");
	I2c_send_data.view = I2c_cmd.view;
	i2c_send_data_flag = 1;
	show_page_2_page();
	switch (MirrPara.vedio_para.StreamingMirrPosnAdjmt)
	{
	case 0:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0x80, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 1:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0x9c, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 2:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xbc, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 3:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xdc, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 4:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xfc, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 5:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0x10, 0x1, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 6:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	default:
		return 1;
	}

	return 1;
}


uint8_t disp_angle()
{
	DEBUG_LOGI("disp_angle\r\n");
	I2c_send_data.visual_angle = I2c_cmd.visual_angle;
	i2c_send_data_flag = 1;
	show_page_1_page();

	switch (MirrPara.vedio_para.StreamingMirrImgAdjmt)
	{
	case 0:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xb0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 1:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x5, 0xf0, 0x0, 0x80, 0x7, 0x80, 0xa1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	case 2:
		mark_a(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_b(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_c(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		mark_d(0x80, 0x7, 0x80, 0x1, 0x80, 0x7, 0x80, 0x1);
		break;

	default:
		break;
	}

	return 1;
}
#endif

uint8_t disp_on_off(uint8_t _on_off)
{

	switch (_on_off)
	{
	case 0:
		mpq3367_disable();
		break;

	case 1:
		mpq3367_enable();
		break;

	default:
		break;
	}

	return 1;
}

void need_disp_on(void)
{
	extern uint8_t mpq3367_enable_state;
	if (mpq3367_enable_state == 0 /*I2c_cmd.on_off == 0*/)
	{
		mpq3367_enable();
	}
}
#if (USE_LCM_FLIP==1)
//ERR_GW5210��Ƶ�����ݴ������
//����ͷ�����쳣
#define DISP_ERR_VI()			;	{									\
		show_page_4_page(); 											\
		mark_a(0x14,0x5,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_d(0x0,0x0,0x7f,0x1,0x7f,0x7,0x7f,0x1); 					\
		}

//�¶ȹ�����ʾ����ʱ3��
#define DISP_ERR_TEMP(T)			;	{								\
		show_page_3_page(); 											\
		if(T == 0){ 													\
		mark_a(0x7f,0x7,0x7f,0x1,0x7f,0x7,0x7f,0x1);					\
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_d(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		}																\
		else if(T == 1){												\
		mark_a(0x08,0x7,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_b(0x00,0x0,0x0,0x0,0x08,0x7,0x7f,0xa1);					\
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_d(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		}																\
		else if(T == 2){												\
		mark_a(0x08,0x07,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_c(0x00,0x0,0x0,0x0,0x08,0x7,0x7f,0xa1);					\
		mark_d(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		}																\
		else if(T == 3){												\
		mark_a(0x08,0x7,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_d(0x00,0x0,0x0,0x0,0x08,0x7,0x7f,0xa1);					\
		}																\
		}

//��ý���������ѹ�쳣
#define DISP_ERR_VOLTS()			;	{									  \
		show_page_4_page(); 											\
		mark_a(0x14,0x5,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_b(0x0,0x0,0x7f,0x1,0x7f,0x7,0x7f,0x1); 					\
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_d(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		}
#else

//ERR_GW5210��Ƶ�����ݴ������
//����ͷ�����쳣
#define DISP_ERR_VI()			;	{									\
		show_page_4_page(); 											\
		mark_a(0x0,0x0,0x0,0x0,0x70,0x2,0x7f,0xa1); 					\
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_d(0x0,0x0,0x7f,0x1,0x7f,0x7,0x7f,0x1); 					\
		}

//�¶ȹ�����ʾ����ʱ3��
#define DISP_ERR_TEMP(T)			;	{								\
		show_page_3_page(); 											\
		if(T == 0){ 													\
		mark_a(0x7f,0x7,0x7f,0x1,0x7f,0x7,0x7f,0x1);					\
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_d(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		}																\
		else if(T == 1){												\
		mark_a(0x0,0x0,0x0,0x0,0x7f,0x0,0x7f,0xa1); 					\
		mark_b(0x7f,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);					\
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_d(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		}																\
		else if(T == 2){												\
		mark_a(0x0,0x0,0x0,0x0,0x7f,0x0,0x7f,0xa1); 					\
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_c(0x7f,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);					\
		mark_d(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		}																\
		else if(T == 3){												\
		mark_a(0x0,0x0,0x0,0x0,0x7f,0x0,0x7f,0xa1); 					\
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1); 					\
		mark_d(0x7f,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);					\
		}																\
		}

// ��ý���������ѹ�쳣
#define DISP_ERR_VOLTS()                                   \
	;                                                      \
	{                                                      \
		show_page_4_page();                                \
		mark_a(0x0, 0x0, 0x0, 0x0, 0x70, 0x2, 0x7f, 0xa1); \
		mark_b(0x0, 0x0, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1); \
		mark_c(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1); \
		mark_d(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1); \
	}

#endif

#if (EF1E_TEST_MODE == 4)
uint8_t ef1e_test_error_mode = 0;
uint8_t ef1e_test_error_view_flag = 0;
void display_test_screen(uint8_t _on_off, uint8_t _r, uint8_t _g, uint8_t _b);
#define EF1E_TEST_ERROR_MAX_COUNT 20

void display_test_error_mode(uint8_t _mode, uint8_t _on_off)
{
	if (_on_off == 1)
	{
		if (_mode == 6)
		{
			ef1e_test_error_mode = _mode;
			return;
		}

		if (_mode == 5 || _mode == 4)
			display_blue_screen();

		ef1e_test_error_mode = _mode;
		osd_err_mask |= ERR_TEST_ERROR;
		osd_event_flag |= OSD_EVENT_ERR;
	}
	else
	{
		disable_blue_screen(); // ����Ƶ����ʱ�ر���ʾ����
		osd_err_mask &= ~ERR_TEST_ERROR;
		ef1e_test_error_mode = 0;
	}
}
#endif

uint8_t disp_one_err(uint16_t err)
{
	switch (err)
	{
#if (HW_VERSION_CHECK == 1)
	case ERR_HW_VER:
		need_disp_on();
		show_page_3_page();
	#if (USE_LCM_FLIP==1)
		mark_a(0x0A,0x5,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_d(0x0A,0x5,0x0,0x0,0x7f,0x7,0x7f,0xa1);
	#else
		mark_a(0x0,0x0,0x0,0x0,0x6c,0x2,0x7f,0xa1);
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_d(0x0,0x0,0x0,0x0,0x7f,0x0,0x7f,0xa1);
	#endif
		break;
#endif
	case ERR_GW5210:
	case ERR_MAX96716A:
	case ERR_MAX96752:
		if(lin_get_sleep_flag() == 0){
			need_disp_on();
		}
		DISP_ERR_VI();
		break;

	case ERR_KEAZN64_IGN:
	case ERR_KEAZN64_12V:
	case ERR_KEAZN64_MAX20088:
	case ERR_KEAZN64_MAX20040:
	case ERR_KEAZN64_PG3:
	case ERR_KEAZN64_PG2:
	case ERR_KEAZN64_PG1:
		if (/*SLEEP_MODE*/0x0A == lin_lld_get_state() && MirrPara.StreamingMirrEnable == OnOff1_Off){
			DEBUG_LOGI("lin SLEEP_MODE && StreamingMirrEnable==0, need_disp_on() skip\r\n");
		}else{
			need_disp_on();
		}
		DISP_ERR_VOLTS();
		break;
#if (EF1E_TEST_MODE == 4)
	case ERR_TEST_ERROR:
		printf("ef1e_test_error_mode = %d\r\n", ef1e_test_error_mode);
		switch (ef1e_test_error_mode)
		{
		case 1://��������
		show_page_7_page();
			mark_a(0x0, 0x0, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
			mark_b(0x0, 0x0, 0x0, 0x0, 0x70, 0x7, 0x7f, 0xa1);
			mark_c(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_d(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);

			break;
		case 2://ͼ���ӳ�
			show_page_7_page();
			mark_a(0x0, 0x0, 0x0, 0x0, 0x70, 0x7, 0x7f, 0xa1);
			mark_b(0x0, 0x0, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
			mark_c(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_d(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			break;
		case 3://ͼ�񶳽�
			show_page_7_page();
			mark_a(0x0, 0x0, 0x0, 0x0, 0x70, 0x7, 0x7f, 0xa1);
			mark_b(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_c(0x0, 0x0, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
			mark_d(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			break;
		case 4://cemʧЧ
			show_page_7_page();
			mark_a(0x0, 0x0, 0x0, 0x0, 0x70, 0x7, 0x7f, 0xa1);
			mark_b(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_c(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_d(0x0, 0x0, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
			break;
		case 5://CEM,LCMͨѶ�쳣
			show_page_8_page();
			mark_a(0x0, 0x0, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
			mark_b(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_c(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_d(0x0, 0x0, 0x0, 0x0, 0x70, 0x7, 0x7f, 0xa1);
			break;
		case 6://����
			show_page_4_page();
			mark_a(0x0, 0x0, 0x0, 0x0, 0x70, 0x7, 0x7f, 0xa1);
			mark_b(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_c(0x0, 0x0, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
			mark_d(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);

			break;
		case 7:
			show_page_8_page();//��ѧʧ��
			mark_a(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_b(0x0, 0x0, 0x7f, 0x1, 0x7f, 0x7, 0x7f, 0x1);
			mark_c(0x0, 0x0, 0x0, 0x0, 0x7f, 0x7, 0x7f, 0xa1);
			mark_d(0x0, 0x0, 0x0, 0x0, 0x70, 0x2, 0x7f, 0xa1);
			break;
			
		default:
			break;
		}
		break;
#endif
	case ERR_KEAZN64_TEMP:
	case ERR_NTC:
		need_disp_on();
		//show_page_3_page();
		show_page_3_page();
#if (USE_LCM_FLIP==1)
		mark_a(0x08,0x7,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_d(0x00,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
#else
		mark_a(0x0,0x0,0x0,0x0,0x7f,0x0,0x7f,0xa1);
		mark_b(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_c(0x0,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
		mark_d(0x7f,0x0,0x0,0x0,0x7f,0x7,0x7f,0xa1);
#endif
		break;

	default:
		return 1;
	}
	return 0;
}


uint8_t disp_err_type()
{
	static uint8_t  type = 0;
	while (type < 16)
	{
		if (osd_err_mask & (1 << type))
		{
			if (disp_one_err(osd_err_mask & (1 << type)) == 1)
			{
				osd_err_mask &= ~(1 << type++);
			}
			else
			{
				break;
			}
		}
		else
		{
			type++;
		}
	}

	if (type >= 16)
	{
		type = 0;
		return 1;
	}
	return 0;
}

uint8_t disp_tp_err_type()
{
	DEBUG_LOGI("disp_tp_err_type\r\n");
	DISP_ERR_VI();
	return 1;
}


static uint8_t  disp_item = 0;
static uint8_t  disp_en = 0;
static uint8_t  steps = 0;

#define DISP_TIME				3500
static void disp_init()
{
	disp_item = 0;

	//��ʼ��NXP������ʾ
	err_timer = 0;
	err_steps = 0;
}

uint8_t tp6815_reset_mipiSinal(void);
void osd_task_init()
{
	osd_event_flag &= ~(OSD_EVENT_BRIGHT | OSD_EVENT_VIEW | OSD_EVENT_ANGLE|OSD_EVENT_ERR); //������������õ���ʾ
	disp_en = 0;
	steps = 0;
	disp_init();
	//DEBUG_LOGI("osd_event_flag = %02bx\r\n", osd_event_flag);
}


#if (HW_VERSION_CHECK == 1)
extern uint8_t  gHWVersionErr;
#endif
void my_osd_task(void)
{
	uint8_t tEvBit = 0;
	uint8_t tOsdEv = 0;
	uint8_t rtn = 0;
    if (SysGetLapseTick(osd_task_tick) < 10)
    {
    	return;
    }
    SysSetCurrentTick(&osd_task_tick);


	//DEBUG_LOGI("DBGLOG[%ums]: my_osd_task!\n", getSysTick();
	//ͼ��ֱ��ʼ��
	//DEBUG_LOGI("p0_02: %02bx\r\n", read_p0(0x02));
	//DEBUG_LOGI("p0_03: %02bx\r\n", read_p0(0x03));
	//0x0780 = 1920
#if 0
	if (read_p0(0x02) != 0x80)
	{
		DEBUG_LOGI("P0_02 err ->%02x\r\n", read_p0(0x02));
		write_p0(0x02, 0x80);
	}

	if (read_p0(0x03) != 0x07)
	{
		DEBUG_LOGI("P0_03 err ->%02x\r\n", read_p0(0x03));
		write_p0(0x03, 0x07);
	}

	if (read_p0(0x04) != 0x80)
	{
		DEBUG_LOGI("P0_04 err ->%02x\r\n", read_p0(0x04));
		write_p0(0x04, 0x80);
	}

	if (read_p0(0x05) != 0x01)
	{
		DEBUG_LOGI("P0_05 err ->%02x\r\n", read_p0(0x05));
		write_p0(0x05, 0x01);
	}
#endif
	tOsdEv = osd_event_flag & 0x3F;
	if (tOsdEv) //new osd even
	{
		disp_item = 0;
		//get first event bit
		for (tEvBit = 1; tEvBit < OSD_EVENT_MAX; tEvBit <<= 1)
		{
			if (tOsdEv & tEvBit)
			{
				disp_item = tEvBit;
				//DBGMSG ( ( "disp_item = %02bx\r\n", disp_item ) );
				break;
			}
		}
		//osd_disable ( OSD_CTRL_EN );
		switch (disp_item)
		{
		case OSD_EVENT_BRIGHT:
#if (OSD_EVENT_BRIGHT_ANGLE_VIEW_SKIP == 1)
			rtn = 1;
#else
			rtn = disp_bright();
#endif
			break;

		case OSD_EVENT_VIEW:
#if (OSD_EVENT_BRIGHT_ANGLE_VIEW_SKIP == 1)
			rtn = 1;
#else
			rtn = disp_view();
#endif
			break;

		case OSD_EVENT_ANGLE:
#if (OSD_EVENT_BRIGHT_ANGLE_VIEW_SKIP == 1)
			rtn = 1;
#else
			rtn = disp_angle();
#endif
			break;

		case OSD_EVENT_ONOFF:
			break;

		case OSD_EVENT_ERR:
			disp_err_type();
			SysSetCurrentTick(&osd_polling_time_tick);
			break;
		default:
			rtn = 1;
			break;
		}
		osd_event_flag &= ~disp_item;	// clear current event bit
		//osd_polling_time = getSysTick();	// update time stamp
	}
#if (USE_API_CODE_MIRROR_REWRN == 1)
	if (SysGetLapseTick(osd_warning_time_tick) > 500)
	{
	    SysSetCurrentTick(&osd_warning_time_tick);
	    if (!disp_item)
	    	//���û������OSD�¼�����ʾ
	    	disp_closed_warning();
	    else
	    	//���������OSD�¼����ʼ��
	    	g_streamingmirrrewnswt = 0;

	}
#endif

	//if ((getSysTick() - osd_polling_time) > DISP_TIME)//��ʾN���ر�
	if (SysGetLapseTick(osd_polling_time_tick) > DISP_TIME)
	{
		if (disp_item)
		{

#if (USE_API_CODE_MIRROR_REWRN == 1 && HW_VERSION_CHECK == 1)
			if (g_disp_closed_warning_osd_flag == 0 || gHWVersionErr == 0)
#elif (HW_VERSION_CHECK == 1)
			if (gHWVersionErr == 0)
#elif (USE_API_CODE_MIRROR_REWRN == 1)
			if (g_disp_closed_warning_osd_flag == 0)
#endif
				osd_disable(OSD_CTRL_EN);
			if (getOverHeatFg())
			{
				DEBUG_LOGI("getoverheat\r\n");
				mpq3367_disable();
			}else{
				if(get_power_state()!=POWER_OFF_2MIN && get_power_state()!=POWER_OFF_7MIN)
				{
					disp_on_off(MirrPara.vedio_para.StreamingMirrSwt);
				}
				else
				{
					disp_on_off(0);
				}
			}

#if (EF1E_TEST_MODE == 4)
			if (ef1e_test_error_mode > 0)
			{
				display_test_screen(1, 0, 0, 0);
				// if (ef1e_test_error_mode == 3 || ef1e_test_error_mode == 4)
				// 	write_p1(0xA9, read_p1(0xA9) | 0x08);
			}
#endif
			disp_item = 0; // make sure runing once
			DEBUG_LOGI("osd off\r\n");
		}
		//osd_polling_time = getSysTick();
	}
#if (USE_API_CODE_MIRROR_REWRN == 1)
	if (g_streamingmirrrewnswt == 0 && g_disp_closed_warning_osd_flag == 1)
	{
		//��Ҫ�ر�osd
		osd_disable(OSD_CTRL_EN);
		g_disp_closed_warning_osd_flag = 0;
		DEBUG_LOGI("warning osd off\r\n");
	}
#endif
	//Add Error Sub-process
}


/***************************************
��Ƶ�����⺯��

time ��ʱʱ�䣬��λms ȡֵ��ΧС��10000

**************************************/
int  video_flag = video_normal; //0;
#if 0
void init_wait_video_input(void)
{
	while ((read_p0(0x2f) & 0x20) == 0) //no video input
	{
		wdt_keepalive(10);
		display_blue_screen();
		write_p0(0x2f, 0x20);					//д1����
		TP_Delay(50);
	}

	disable_blue_screen();
	DEBUG_LOGI("init_wait_video_input: video is OK!\r\n");
}
#endif



uint8_t getVideoFlag(void)
{
	return video_flag;
}
extern void max96706_init(void);
extern void l601_8_init(void);
extern void load_panel_if_init(void);
static uint16_t video_check_loss_count = 0;
static uint16_t video_check_in_count = 0;

uint8_t regP3_38Val = 0xff;
uint8_t regP3_39Val = 0xff;
extern uint8_t check_ser_des_lock(void);
void reset_mipi_csi(void)
{
	write_p3(0x20,0x02);
	KEA_Delay(10);
	write_p3(0x20,0x40);
}
uint8_t tp6815_check_mipiSinal(void)
{
	uint8_t ret = 0;
	regP3_38Val = read_p3(0x38);
	KEA_Delay(1);
	regP3_39Val = read_p3(0x39);
	//printf("tp6815 check mipi, P3reg38: %x, P3reg39: %x\r\n", regP3_38Val, regP3_39Val);
	if((regP3_38Val == 0) &&(regP3_39Val == 0))
	{
		ret = 1;
	}
	//ret &= check_ser_des_lock();
	return ret;
}
uint8_t tp6815_reset_mipiSinal(void)
{
	uint8_t ok = 0;
	write_p3(0x20,0x02);
	ok = write_p3(0x10, 0x31);
	if(regP3_38Val != 0)
	{
		ok = write_p3(0x38, 0x11);//regP3_38Val);
	}
	if(regP3_39Val != 0)
	{
		ok = write_p3(0x39, 0x11);//regP3_39Val);
	}
	write_p3(0x20,0x40);
	ok = write_p3(0x10, 0x30);
	return ok;
}
extern void reset_tp6815_rx(void);
#if (USE_TD_TIMING_DETECT==1)
uint8_t vp_checkMipiRxStatus(void)//APP ���ô˺���
{
	uint8_t ok = 0;
	uint8_t lock_init_flag = 0;
	uint8_t cnt = 0;
	uint8_t cnt_max = 5;
	if(get_repower_status() != 1){
		cnt_max = 5;
	}else{
		cnt_max = 2;
	}
	//cnt_max = 2;
	while(cnt < cnt_max)
	{
		cnt ++;
		ok = tp6815_check_mipiSinal();
		if (ok ==1)
		{
			break;
		}
		ok = tp6815_reset_mipiSinal();
		KEA_Delay(10);
		WDOG_Feed();
		DEBUG_LOGI("vp_checkMipiRxStatus cnt: %d[%d][%d]\r\n", cnt,cnt_max,ok);
	}
	//printf("ok[%x]\r\n",ok);
	return ok;
}
uint8_t vp_checkMipiRxStatus_1(void)//APP ���ô˺���
{
	uint8_t ok = 0;
	uint8_t cnt = 0;
	uint8_t cnt_max = 5;
	if(get_repower_status() != 1){
		cnt_max = 5;
	}else{
		cnt_max = 2;
	}
	//cnt_max = 2;
	while(cnt < cnt_max)
	{
		cnt ++;
		ok = check_ser_des_lock();//tp6815_check_mipiSinal();
		if(ok == 1)
		{
			ok = tp6815_check_mipiSinal();
			if (ok ==1)
			{
				//rest_tp6815_rx_lock();
				break;
			}
			else
			{
				DEBUG_LOGI("tp6815_check_mipiSinal error\r\n");
			}
		}
		else
		{
			DEBUG_LOGI("ser des lock error\r\n");
			ok = 2;
		}
		if(ok == 0|| ok ==2)
		{
			if (ok ==2)
			{
				max_des_ser_init();
				KEA_Delay(20);
			}
			ok = check_ser_des_lock();
			if(ok ==1)
			{
				//after lock
				//check_ser_des_error();
				ok = tp6815_check_mipiSinal();
				if (ok == 0)
				{	DEBUG_LOGI("reset mipiSinal\r\n");
					reset_mipi_csi();
					KEA_Delay(10);
					tp6815_reset_mipiSinal();
					KEA_Delay(10);
				}
				rest_tp6815_rx_lock();
#if 0
				else
				{
					regP1_A9val = read_p1(0xA9);
					if ((regP1_A9val&0x08) !=0x08)
					{
						DEBUG_LOGI("reset mipiSinal_A9\r\n");
						reset_mipi_csi();
						KEA_Delay(10);
						tp6815_reset_mipiSinal();
						KEA_Delay(10);
					}
				}
#endif
				KEA_Delay(20);
			}
		}
		WDOG_Feed();
		DEBUG_LOGI("vp_checkMipiRxStatus cnt: %d[%d][%d]\r\n", cnt,cnt_max,ok);
	}
	//printf("ok[%x]\r\n",ok);
	return ok;
}
#else
uint8_t vp_checkMipiRxStatus(void)//APP ���ô˺���
{
	uint8_t ok = 0;
	uint8_t cnt = 0;
	uint8_t cnt_max = 5;
	if(get_repower_status() != 1){
		cnt_max = 5;
	}else{
		cnt_max = 2;
	}
	while(cnt < cnt_max)
	{
		cnt ++;
		ok = check_ser_des_lock();//tp6815_check_mipiSinal();
		if(ok == 1)
		{
			ok = tp6815_check_mipiSinal();
			if (ok ==1)
				break;
			else
			{
				DEBUG_LOGI("tp6815_check_mipiSinal error\r\n");
			}
		}
		else
		{
			DEBUG_LOGI("ser des lock error\r\n");
			ok = 2;
		}
		if(ok == 0|| ok ==2)
		{
			if (ok ==2)
			{
				max_des_ser_init();
				KEA_Delay(20);
			}
			ok = check_ser_des_lock();
			if(ok ==1)
			{
				//after lock
				check_ser_des_error();
				ok = tp6815_check_mipiSinal();
				if (ok == 0)
				{	DEBUG_LOGI("reset mipiSinal\r\n");
					reset_mipi_csi();
					KEA_Delay(10);
					ok = tp6815_reset_mipiSinal();
					KEA_Delay(10);
				}
				rest_tp6815_rx_lock();
#if 0
				else
				{
					regP1_A9val = read_p1(0xA9);
					if ((regP1_A9val&0x08) !=0x08)
					{
						DEBUG_LOGI("reset mipiSinal_A9\r\n");
						reset_mipi_csi();
						KEA_Delay(10);
						tp6815_reset_mipiSinal();
						KEA_Delay(10);
					}
				}
#endif
				KEA_Delay(20);
			}
		}
		WDOG_Feed();
		DEBUG_LOGI("vp_checkMipiRxStatus cnt: %d[%d][%d]\r\n", cnt,cnt_max,ok);
	}
	//printf("ok[%x]\r\n",ok);
	return ok;
}
#endif
#if (USE_TD_TIMING_DETECT == 1)
uint8_t rx_retry_count = 0;
uint8_t abnormal_flag_count1 = 0;
uint8_t abnormal_flag_count2 = 0;
uint8_t abnormal_lock_flag_count = 0;
uint8_t p0_2f_retry_count = 0;
uint8_t tp6816_stop_read_flag = 0;
uint8_t f_tp6816_stop_read_flag(void)
{
	//DEBUG_LOGI("[%s]##############[tp6816_stop_read_flag] = %d\r\n", __FUNCTION__,tp6816_stop_read_flag);
	return tp6816_stop_read_flag;
}
#define USE_TD_TIME_DETECT_TYPE 1
void video_det(uint8_t osdUpdateFg)
{
	uint8_t read_flag = 0;
	uint8_t lock_flag = 1;
	uint8_t abnormal_flag = 0;
	uint8_t p0_20 = 0x00;
	uint8_t p0_2f = 0x00;
#if (USE_TD_TIME_DETECT_TYPE==1)
	if (td_det_format != TD_384I)//((td_det_format == TD_UNKNOWN_TIMING) || (td_det_format == TD_STATE_NO_INPUT))
	{
		if (check_ser_des_lock()==1)
		{
			DEBUG_LOGI("[%s]##############[stats1] = %d\r\n", __FUNCTION__,td_det_format);
			rx_retry_count ++;
			if (rx_retry_count>2)
			{
				//reset_tp6815_rx();
				read_flag = vp_checkMipiRxStatus();//check_ser_des_lock();//
				rx_retry_count = 0;
			}
			KEA_Delay(10);
			rest_tp6815_rx_lock();
			abnormal_lock_flag_count = 0;
		}
		else
		{
			DEBUG_LOGI("[%s]##############[stats2] = %d\r\n", __FUNCTION__,td_det_format);
			max_des_ser_init_only_des();//max_des_ser_init();
			//KEA_Delay(20);
			read_flag = 0;
			lock_flag = 0;
		}
	}
	else
	{
		if (check_ser_des_lock()==1)
		{
			if (tp6815_check_mipiSinal()==1)
				read_flag = 1;
			{
				rx_retry_count ++;
				if (rx_retry_count>2)
				{
					//reset_tp6815_rx();
					read_flag = vp_checkMipiRxStatus();//check_ser_des_lock();//
					rx_retry_count = 0;
				}
				KEA_Delay(10);
				rest_tp6815_rx_lock();
			}
			abnormal_lock_flag_count = 0;
		}
		else
		{
			DEBUG_LOGI("[%s]##############[stats3] = %d\r\n", __FUNCTION__,td_det_format);
			max_des_ser_init_only_des();//max_des_ser_init();
			//KEA_Delay(20);
			read_flag = 0;
			lock_flag = 0;
		}
		p0_20 = read_p0(0x20);
		p0_2f = read_p0(0x2f);
		//DEBUG_LOGI("[%s]##############[p0_2f] = %x\r\n", __FUNCTION__,p0_2f);
		if (p0_2f == 0x00)
		{
			p0_2f_retry_count++;
			if (p0_2f_retry_count >15)
			{
				read_flag = tp6815_reset_mipiSinal();//check_ser_des_lock();//
				rx_retry_count = 0;
				KEA_Delay(10);
				rest_tp6815_rx_lock();
				DEBUG_LOGI("[%s]##############[reset] = %x\r\n", __FUNCTION__,mi2c_wr_byte(0xb8,0x20,0x40));
				p0_2f_retry_count = 0;
			}
		}
		else
			p0_2f_retry_count = 0;
	}
	KEA_Delay(10);
#else
	read_flag = vp_checkMipiRxStatus();
	p0_2f = read_p0(0x2f);
	DEBUG_LOGI("[%s]##############[p0_2f] = %x\r\n", __FUNCTION__,p0_2f);
	if (p0_2f == 0x00)
	{
		p0_2f_retry_count++;
		if (p0_2f_retry_count >10)
		{
			read_flag = tp6815_reset_mipiSinal();//check_ser_des_lock();//
			rx_retry_count = 0;
			KEA_Delay(10);
			//rest_tp6815_rx_lock();
			DEBUG_LOGI("[%s]##############[reset] = %x\r\n", __FUNCTION__,mi2c_wr_byte(0xb8,0x20,0x40));
			p0_2f_retry_count = 0;
		}
	}
	else
		p0_2f_retry_count = 0;
#endif
	td_timing_detect();
	if (read_flag == 0)
	{
		//mipi  ��·��Ч
		if (video_flag == video_normal)
		{
			tp6816_stop_read_flag = 1;
#if (USE_TD_TIME_DETECT_TYPE ==1)
			abnormal_flag_count1++;
			abnormal_lock_flag_count++;
			if ((lock_flag == 1 && abnormal_flag_count1 >30)||(lock_flag == 0 && abnormal_lock_flag_count >0))
#endif
			{
				if(td_det_format != TD_384I || lock_flag == 0)
				{
					abnormal_flag = 1;
#if (USE_TD_TIME_DETECT_TYPE ==1)
				abnormal_flag_count1 = 0;
				abnormal_lock_flag_count = 0;
				DEBUG_LOGI("[%s]##############[stats7] = %d\r\n", __FUNCTION__,lock_flag);
#endif
				}
			}
		}
	}
	else
	{
		//��·��Ч
		if (video_flag == video_normal)
		{
			//ԭ��Ƶ������
			if (td_det_format != TD_384I)
			{
				//DEBUG_LOGI("[%s]##############[stats5] = %d\r\n", __FUNCTION__,td_det_format);
			}
			if (td_det_format == TD_UNKNOWN_TIMING)
			{
				tp6816_stop_read_flag = 1;
				abnormal_flag_count2++;
				if (abnormal_flag_count2 >20)
				{
					abnormal_flag = 1;
					abnormal_flag_count2 = 0;
					DEBUG_LOGI("[%s]##############[stats8] = %d\r\n", __FUNCTION__,td_det_format);
				}
			}
			else
			{
				//DEBUG_LOGI("[%s]##############[stats5] = %d\r\n", __FUNCTION__,abnormal_flag_count1);
				abnormal_flag_count2 = 0;
				abnormal_flag_count1 = 0;
				tp6816_stop_read_flag = 0;
			}
		}
		else
		{
			//ԭ��Ƶ���쳣
			//if (osdUpdateFg)
			  //td_init();
			if (td_det_format != TD_384I)
			{
				DEBUG_LOGI("[%s]##############[stats6] = %d\r\n", __FUNCTION__,td_det_format);
			}
#if (USE_TD_TIME_DETECT_TYPE==1)
			if (td_det_format == TD_384I)
#endif
			{
				abnormal_flag = 2;
				tp6816_stop_read_flag = 0;
			}
		}
	}

	if (abnormal_flag == 1)
	{
		DEBUG_LOGI("video_detection: video lossin!\r\n");
#if 0
		pattern_gen1(1, 0, 5, 5, 5);
		write_p1(0xA9, 0x04);
#else
		display_blue_screen();			//timeʱ�����ʾ����
#endif
		if (osdUpdateFg) //not osd display while init
		{
			I2c_send_data.Error_type |= ERR_MAX96752;
			i2c_send_data_flag = 1;
			//osd_event_flag |= OSD_EVENT_ERR; //��ʾͼ�����
			state_add_err(ERR_MAX96752);
			//osd_err_mask |= ERR_MAX96752;

			// 设置摄像头连接异常显示标志，确保在低功耗状态下也能显示错误信息
			set_camera_error_display_flag(1);
		}
		DEBUG_LOGI("video_detection: video lossout!\r\n");
		video_flag = video_loss;
	}
	else if (abnormal_flag == 2)
	{
		DEBUG_LOGI("video_detection: video normal in!\r\n");
#if 0
		write_p1(0xA9, 0x08);
		pattern_gen1(0, 0, 5, 5, 5);
#else
		disable_blue_screen();			//timeʱ�����ʾ����
#endif
		if (osdUpdateFg) //not osd display while init
		{
			I2c_send_data.Error_type &= ~ERR_MAX96752;
			i2c_send_data_flag = 1;
			state_clear_err(ERR_MAX96752);
			//osd_err_mask &= ~ERR_MAX96752;

			// 清除摄像头连接异常显示标志
			set_camera_error_display_flag(0);
		}
		max_des_ser_init();
		load_TP6816_MIPI_20240116();
		KEA_Delay(10);
		DEBUG_LOGI("video_detection: video normal!\r\n");
		video_flag = video_normal;
		abnormal_flag_count1 = 0;
		abnormal_flag_count2 = 0;
		abnormal_lock_flag_count = 0;
		tp6816_stop_read_flag = 0;

	}
}
#else
void video_det(uint8_t osdUpdateFg)
{
	uint8_t read_flag = 0;
	uint8_t p0_2f = 0;
#if (USE_TP_CHECK_P1_A9==1)
	uint8_t regP1_A9val =0;
#endif	
	//td_timing_detect();
	read_flag = vp_checkMipiRxStatus();//check_ser_des_lock();//
#if (USE_TD_TIMING_DETECT==1)	
	td_timing_detect();
	if (read_flag == 0 || td_det_format == TD_UNKNOWN_TIMING) //�����⵽û����Ƶ����
#else
	if(read_flag == 0) //�����⵽û����Ƶ����
#endif	
	{
		if (video_flag == video_normal) //pre state video ok
		{
			video_flag = video_loss;
#if (USE_TD_TIMING_DETECT==1)				
			pattern_gen1(1, 0, 5, 5, 5);
			write_p1(0xA9, 0x04);
#else
			display_blue_screen();			//timeʱ�����ʾ����
#endif
			if (osdUpdateFg) //not osd display while init
			{
				I2c_send_data.Error_type |= ERR_MAX96752;
				i2c_send_data_flag = 1;
				//osd_event_flag |= OSD_EVENT_ERR; //��ʾͼ�����
				state_add_err(ERR_MAX96752);
				//osd_err_mask |= ERR_MAX96752;
			}
			DEBUG_LOGI("video_detection: video loss!\r\n");
		}
		video_check_in_count = 0;
	}
#if (USE_TD_TIMING_DETECT==1)	
	else if (read_flag != 0 && td_det_format == TD_384I)
#else
	else
#endif	
	{
		if (video_flag == video_loss) //pre state video loss
		{
			video_flag = video_normal;
#if (USE_TD_TIMING_DETECT==1)	
			write_p1(0xA9, 0x08);
			pattern_gen1(0, 0, 5, 5, 5);

#else
			disable_blue_screen();			//����Ƶ����ʱ�ر���ʾ����
#endif			
			if (osdUpdateFg) //not osd display while init
			{
				I2c_send_data.Error_type &= ~ERR_MAX96752;
				i2c_send_data_flag = 1;
				state_clear_err(ERR_MAX96752);
				//osd_err_mask &= ~ERR_MAX96752;
			}
			max_des_ser_init();
			load_TP6816_MIPI_20240116();
			KEA_Delay(10);
			DEBUG_LOGI("video_detection: video normal!\r\n");

			//if(check_ser_des_lock()==1)
				//reset_TP6816_MIPI();
		}
		else
		{
#if (USE_TP_CHECK_P1_A9==1)
			regP1_A9val = read_p1(0xA9);
			if ((regP1_A9val&0x08) !=0x08)
			{
#if 0
				DEBUG_LOGI("reset mipiSinal_A9\r\n");
				reset_mipi_csi();
				KEA_Delay(10);
				tp6815_reset_mipiSinal();
				KEA_Delay(10);
#endif
				max_des_ser_init();
				load_TP6816_MIPI_20240116();
				KEA_Delay(10);
			}
#endif
		}

		video_check_loss_count = 0;
	}
}

#endif
void video_det_set_osd(void)
{
	if (video_flag == video_loss)
	{
		I2c_send_data.Error_type |= ERR_MAX96752;
		i2c_send_data_flag = 1;
		//osd_event_flag |= OSD_EVENT_ERR; //��ʾͼ�����
		state_add_err(ERR_MAX96752);
	}
}

void my_video_detection_task(void) //int time)
{
	if (SysGetLapseTick(video_check_tick) >= 20)
	{
		SysSetCurrentTick(&video_check_tick);
#if (USE_TD_TIMING_DETECT == 1)
		TD_CLEAR_TIME_CHG();
		TD2_CLEAR_TIME_CHG();
#endif
		//DEBUG_LOGI("[%s]##############video_check_tick = %d\r\n", __FUNCTION__,video_check_tick);
		video_det(1);
	}
}


/***************************************
������ʾ����

**************************************/
void display_blue_screen(void)
{
	DEBUG_LOGI("turn on BLUE screen!\r\n");
	write_p1(0x76, 0x80);
	write_p1(0x77, 0x09);
	write_p1(0x78, 0x5);							//b
	write_p1(0x79, 0x5);							//g
	write_p1(0x7a, 0x5);							//r
}


void disable_blue_screen(void)
{
	DEBUG_LOGI("turn off BLUE screen!\r\n");
	write_p1(0x76, 0x00);							// ???????
}

void pattern_gen1(unsigned char en, uint8_t mode, uint8_t Red, uint8_t Green, uint8_t blue)
{
	cq_set_trig_mode(CQ_VS_TRIG);
	CQ_CNT_START();
	if (en == 0) {
		// disable VSi_Tracking for frame lock first
		//CQ_P1_1B_(0xA9, (read_p1(0xA9) & 0xDF));
		CQ_PAUSE_FRAME(0x5); // ����������Ե���
		//CQ_P1_1B_(0xA9, read_p1(0xA9));
	}
	CQ_P1_1B_(0x76, (en == 1 ? 0x80 : 0x00));  // P1_76 = 0x80, enable build-in pattern
	if (en == 1)
	{
		if (mode == 0)
		{
			CQ_P1_1B_(0x76, 0x80);
			CQ_P1_1B_(0x77, 0x09);
			CQ_P1_1B_(0x78, Red);
			CQ_P1_1B_(0x79, Green);
			CQ_P1_1B_(0x7A, blue);
		}
		else if (mode != 0)
		{
			CQ_P1_1B_(0x76, 0x80);
			CQ_P1_1B_(0x77, 0x08);
		}

	}
	CQ_FINISH_ISSUE();

}

void mark_a(uint8_t x_begin_low, uint8_t x_begin_high, uint8_t y_begin_low, uint8_t y_begin_high, uint8_t x_end_low, uint8_t x_end_high, uint8_t y_end_low, uint8_t y_end_high)
{
	write_p2(0x20, x_begin_low);
	write_p2(0x21, x_begin_high);
	write_p2(0x22, y_begin_low);
	write_p2(0x23, y_begin_high);
	write_p2(0x24, x_end_low);
	write_p2(0x25, x_end_high);
	write_p2(0x26, y_end_low);
	write_p2(0x27, y_end_high);

}

void mark_b(uint8_t x_begin_low, uint8_t x_begin_high, uint8_t y_begin_low, uint8_t y_begin_high, uint8_t x_end_low, uint8_t x_end_high, uint8_t y_end_low, uint8_t y_end_high)
{
	write_p2(0x28, x_begin_low);
	write_p2(0x29, x_begin_high);
	write_p2(0x2a, y_begin_low);
	write_p2(0x2b, y_begin_high);
	write_p2(0x2c, x_end_low);
	write_p2(0x2d, x_end_high);
	write_p2(0x2e, y_end_low);
	write_p2(0x2f, y_end_high);

}

void mark_c(uint8_t x_begin_low, uint8_t x_begin_high, uint8_t y_begin_low, uint8_t y_begin_high, uint8_t x_end_low, uint8_t x_end_high, uint8_t y_end_low, uint8_t y_end_high)
{
	write_p2(0x30, x_begin_low);
	write_p2(0x31, x_begin_high);
	write_p2(0x32, y_begin_low);
	write_p2(0x33, y_begin_high);
	write_p2(0x34, x_end_low);
	write_p2(0x35, x_end_high);
	write_p2(0x36, y_end_low);
	write_p2(0x37, y_end_high);

}

void mark_d(uint8_t x_begin_low, uint8_t x_begin_high, uint8_t y_begin_low, uint8_t y_begin_high, uint8_t x_end_low, uint8_t x_end_high, uint8_t y_end_low, uint8_t y_end_high)
{
	write_p2(0x38, x_begin_low);
	write_p2(0x39, x_begin_high);
	write_p2(0x3a, y_begin_low);
	write_p2(0x3b, y_begin_high);
	write_p2(0x3c, x_end_low);
	write_p2(0x3d, x_end_high);
	write_p2(0x3e, y_end_low);
	write_p2(0x3f, y_end_high);

}

#if (EF1E_TEST_MODE == 1 || EF1E_TEST_MODE == 4)
void display_test_screen(uint8_t _on_off, uint8_t _r, uint8_t _g, uint8_t _b)
{
	//DEBUG_LOGI("turn on BLUE screen!\r\n");
	if (_on_off == 1)
	{
		write_p1(0x76, 0x80);
		write_p1(0x77, 0x09);
		write_p1(0x78, _b);							//b
		write_p1(0x79, _g);							//g
		write_p1(0x7a, _r);							//r
	}
	else
	{
		write_p1(0x76, 0x00);
	}
}

static uint8_t g_test_mode_pre = 0;
void display_test_mode(uint8_t _mode)
{
	if (_mode >= 0 && _mode <= TEST_MODE_FLAG_MAX)
	{
		switch (_mode)
		{
		case 0:
			//off
			display_test_screen(0, 0, 0, 0);
			g_test_mode_pre = 0;
			break;
		case 1:
			// balck
			display_test_screen(1, 0, 0, 0);
			g_test_mode_pre = 1;
			break;
		case 2:
			// white
			display_test_screen(1, 255, 255, 255);
			g_test_mode_pre = 2;
			break;
		case 3:
			// gray
			display_test_screen(1, 0xb3, 0xb3, 0xb3);
			g_test_mode_pre = 3;
			break;
		case 4:
			// red
			display_test_screen(1, 255, 0, 0);
			g_test_mode_pre = 4;
			break;
		case 5:
			// green
			display_test_screen(1, 0, 255, 0);
			g_test_mode_pre = 5;
			break;
		case 6:
			// blue
			display_test_screen(1, 0, 0, 255);
			g_test_mode_pre = 6;
			break;
		case 7:
			if (g_test_mode_pre > 1 && g_test_mode_pre < TEST_MODE_FLAG_MAX)
			{
				display_test_screen(1, 0, 0, 0);
				g_test_mode_pre = 1;
			}
			else if (g_test_mode_pre == 1)
			{
				display_test_screen(1, 255, 255, 255);
				g_test_mode_pre = 2;
			}
			break;
		}
	}
}
#endif


void osd_set_event_flag(uint8_t _event)
{
	osd_event_flag|=_event;
}
