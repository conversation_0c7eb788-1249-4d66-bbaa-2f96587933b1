# LDF文件分析文档

## 文件概述

**文件名**: ZSDB225100_EX1H_High_ZCL_LIN5_V5.ldf  
**LIN协议版本**: 2.1  
**语言版本**: 2.1  
**通信速度**: 19.2 kbps  
**通道名称**: ZCL_LIN5  

## 网络节点架构

### 主节点 (Master)
- **ZCL** (Zone Control Logic) - 区域控制逻辑单元

### 从节点 (Slaves)
1. **RLSM** (Rain Light Solar Module) - 雨量光照太阳能模块
2. **RMD** (Rear Mirror Display) - 后视镜显示器
3. **WMM** (Wiper Motor Module) - 雨刮电机模块

## 信号分析

### 1. 流媒体后视镜系统 (Streaming Mirror System)

#### 亮度控制信号
- **StrmMirrBri**: 流媒体后视镜亮度 (8位, 0-255)
- **StrmMirrBriEna**: 流媒体后视镜亮度使能 (1位)
- **StrmMirrBriEnaSts**: 流媒体后视镜亮度使能状态 (1位)

#### 图像控制信号
- **StrmMirrImg**: 流媒体后视镜图像 (2位)
- **StrmMirrImgEna**: 流媒体后视镜图像使能 (1位)
- **StrmMirrImgEnaSts**: 流媒体后视镜图像使能状态 (1位)

#### 模式控制信号
- **StrmMirrMod**: 流媒体后视镜模式 (2位)
- **StrmMirrModEna**: 流媒体后视镜模式使能 (1位)
- **StrmMirrModEnaSts**: 流媒体后视镜模式使能状态 (1位)

#### 位置控制信号
- **StrmMirrPos**: 流媒体后视镜位置 (8位)
- **StrmMirrPosEna**: 流媒体后视镜位置使能 (1位)
- **StrmMirrPosEnaSts**: 流媒体后视镜位置使能状态 (1位)

#### 后方警告信号
- **StrmMirrRearWarn**: 流媒体后视镜后方警告 (1位)
- **StrmMirrRearWarnEna**: 流媒体后视镜后方警告使能 (1位)
- **StrmMirrRearWarnEnaSts**: 流媒体后视镜后方警告使能状态 (1位)

### 2. 内后视镜调光系统 (Interior Mirror Dimming System)

#### 调光控制信号
- **IntrMirrDimEna**: 内后视镜调光使能 (1位)
- **IntrMirrDimSnvty**: 内后视镜调光灵敏度 (4位, 0-15)
- **IntrMirrDimSnvtyTyp**: 内后视镜调光灵敏度类型 (4位)

### 3. 雨刮系统 (Wiper System)

#### 雨刮速度控制
- **WipgSpd**: 雨刮速度 (3位)
- **WipgSpdIntlFromHmi**: 来自HMI的雨刮间歇速度 (4位)

#### 雨刮模式控制
- **WipgAutFrntMod**: 前雨刮自动模式 (2位)
- **WipgAutFrntModEna**: 前雨刮自动模式使能 (1位)
- **WipgAutFrntModEnaSts**: 前雨刮自动模式使能状态 (1位)

#### 雨刮角度信号
- **WipAg**: 雨刮角度 (8位)
- **WipAgOffs**: 雨刮角度偏移 (8位)

### 4. 雨量光照传感器系统 (Rain Light Sensor System)

#### 雨量传感器信号
- **RainSnsrThd**: 雨量传感器阈值 (8位)
- **AmntSnsr**: 环境传感器 (8位)

#### 光照传感器信号
- **LiIllmn**: 光照强度 (16位)
- **LiOperMod**: 光照操作模式 (2位)
- **TwliBriRaw**: 暮光亮度原始值 (10位)
- **OutdBriSts**: 室外亮度状态 (8位)

#### 太阳负荷传感器
- **Sunload2D**: 二维太阳负荷 (8位)

### 5. 车辆管理系统 (Vehicle Management System)

#### 电源管理
- **PowerModeState**: 电源模式状态 (4位)
- **MvBattSwVersAct**: 移动电池软件版本激活 (8位)

#### 车辆模式
- **CarModSts**: 车辆模式状态 (4位)

#### 湿度传感器
- **TByCmptmtSnsrRelHum**: 舱室传感器相对湿度 (8位)

### 6. 诊断信号 (Diagnostic Signals)

#### 主节点请求信号
- **MasterReqB0-B7**: 主节点请求字节0-7 (各8位)

#### 从节点响应信号
- **SlaveRespB0-B7**: 从节点响应字节0-7 (各8位)

#### 故障状态信号
- **FltStsSlaveBasc**: 从节点基础故障状态 (8位)
- **FltEgyCns**: 故障能耗 (2位)

### 7. 产品信息信号 (Product Information Signals)

#### 序列号信号
- **SerNr1-SerNr6**: 序列号1-6 (各8位)

#### 零件号信号
- **PartNr1-PartNr6**: 零件号1-6 (各8位)

## 通信帧结构详细定义

### ZCL主节点发送帧

#### ZCLZCLLin5Frame02 (ID: 0x05)
**帧长度**: 6字节  
**发送节点**: ZCL  
**功能**: 雨刮控制和流媒体后视镜控制

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| VehSpdForWipg | 0 | 8 | 0 | 雨刮用车速 |
| WipgPwrActvnSafeWipgPwrAcsyModSafe | 8 | 2 | 1 | 雨刮电源激活安全-附件模式安全 |
| WipgPwrActvnSafeWipgPwrDrvgModSafe | 10 | 2 | 1 | 雨刮电源激活安全-驾驶模式安全 |
| RainSensActvn | 12 | 1 | 1 | 雨量传感激活 |
| WshrLvrPosnSafe | 13 | 2 | 1 | 洗涤器杆位置安全 |
| StreamingMirrReWrnSwt | 15 | 1 | 1 | 流媒体后视镜后方警告开关 |
| WiprMotFrntLvrCmdSafeLvrInHiSpdPosnSafe | 16 | 2 | 1 | 雨刮电机前杆命令安全-杆在高速位置安全 |
| WiprMotFrntLvrCmdSafeLvrInLoSpdPosnSafe | 18 | 2 | 1 | 雨刮电机前杆命令安全-杆在低速位置安全 |
| WiprMotFrntLvrCmdSafeLvrInSnglStrokePos | 20 | 1 | 1 | 雨刮电机前杆命令安全-杆在单次行程位置 |
| WiprMotFrntLvrCmdSafeLvrInIntlPosn | 21 | 1 | 1 | 雨刮电机前杆命令安全-杆在间歇位置 |
| CbnGroupPwrReqforRMD | 22 | 2 | 1 | RMD舱室组电源请求 |
| WiprMotIntlCmd | 24 | 4 | 3 | 雨刮电机间歇命令 |
| WiprPosnForSrvReq | 27 | 1 | 1 | 服务请求用雨刮位置 |
| StreamingMirrModeSwt | 32 | 4 | 3 | 流媒体后视镜模式开关 |
| WiprMotFrntOffsAg | 40 | 4 | 0 | 雨刮电机前偏移角度 |
| StreamingMirrReWrnDis | 44 | 4 | 3 | 流媒体后视镜后方警告显示 |

#### ZCLZCLLin5Frame03 (ID: 0x07)
**帧长度**: 7字节  
**发送节点**: ZCL  
**功能**: 车辆管理和环境控制

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| AmbTForVisy | 0 | 8 | 0 | 能见度用环境温度 |
| VehModMngtGlbSafeChks8 | 8 | 8 | 0 | 车辆模式管理全局安全校验和8 |
| VehModMngtGlbSafeCntr4 | 16 | 4 | 0 | 车辆模式管理全局安全计数器4 |
| VehModMngtGlbSafeDataID4 | 20 | 4 | 0 | 车辆模式管理全局安全数据ID4 |
| VehModMngtGlbSafeEgyLvlElecMai | 24 | 4 | 0 | 车辆模式管理全局安全能量等级电气主 |
| VehModMngtGlbSafeEgyLvlElecSubtyp | 28 | 4 | 0 | 车辆模式管理全局安全能量等级电气子类型 |
| VehModMngtGlbSafePwrLvlElecMai | 32 | 4 | 0 | 车辆模式管理全局安全功率等级电气主 |
| VehModMngtGlbSafePwrLvlElecSubtyp | 36 | 4 | 0 | 车辆模式管理全局安全功率等级电气子类型 |
| VehModMngtGlbSafePwrModSts | 40 | 4 | 0 | 车辆模式管理全局安全电源模式状态 |
| VehModMngtGlbSafeCarModSts | 44 | 4 | 0 | 车辆模式管理全局安全车辆模式状态 |
| VehModMngtGlbSafeFltEgyCnsWdSts | 47 | 1 | 0 | 车辆模式管理全局安全故障能耗状态 |
| VehModMngtGlbSafeCarModSubtypWdCarModSubtyp | 48 | 3 | 0 | 车辆模式管理全局安全车辆模式子类型 |

#### ZCLZCLLin5Frame04 (ID: 0x9)
**帧长度**: 7字节  
**发送节点**: ZCL  
**功能**: 雨量传感器光照阈值

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| RainSnsrLiThd | 44 | 12 | 0 | 雨量传感器光照阈值 |

#### ZCLZCLLin5Frame05 (ID: 0xA)
**帧长度**: 7字节  
**发送节点**: ZCL  
**功能**: 离线监控使能

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| EnaOfflineMonitor | 17 | 1 | 0 | 使能离线监控 |

#### ZCLZCLLin5Frame06 (ID: 0x17)
**帧长度**: 8字节  
**发送节点**: ZCL  
**功能**: 雨量传感器灵敏度和风挡校正值

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| RainSnsrSnvtyForUsrSnvty0 | 0 | 4 | 0 | 雨量传感器用户灵敏度0 |
| RainSnsrSnvtyForUsrSnvty1 | 4 | 4 | 0 | 雨量传感器用户灵敏度1 |
| RainSnsrSnvtyForUsrSnvty2 | 8 | 4 | 0 | 雨量传感器用户灵敏度2 |
| RainSnsrSnvtyForUsrSnvty3 | 12 | 4 | 0 | 雨量传感器用户灵敏度3 |
| RainSnsrSnvtyForUsrSnvty4 | 16 | 4 | 0 | 雨量传感器用户灵敏度4 |
| RainSnsrSnvtyForUsrSnvty5 | 20 | 4 | 0 | 雨量传感器用户灵敏度5 |
| RainSnsrSnvtyForUsrSnvty6 | 24 | 4 | 0 | 雨量传感器用户灵敏度6 |
| WindCorrnValAmb | 32 | 8 | 0 | 风挡校正值环境 |
| WindCorrnValFrnt | 40 | 8 | 0 | 风挡校正值前向 |
| WindCorrnValHud | 48 | 8 | 0 | 风挡校正值HUD |
| ReAdaptReq | 60 | 1 | 0 | 重新适配请求 |

#### ZCLZCLLin5Frame07 (ID: 0x27)
**帧长度**: 1字节  
**发送节点**: ZCL  
**功能**: 雨刷状态

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| WiprActv | 4 | 1 | 0 | 雨刷激活 |
| WiprInPrkgPosnLo | 5 | 1 | 0 | 雨刷在停车位置低 |
| WiprInWipgAr | 6 | 1 | 0 | 雨刷在刮拭区域 |
| WshngCycActv | 7 | 1 | 0 | 清洗循环激活 |

#### ZCLZCLLin5Frame08 (ID: 0x2A)
**帧长度**: 8字节  
**发送节点**: ZCL  
**功能**: 内后视镜调光命令

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| IntrMirrDimCmdIntrMirrDimSnvty | 0 | 2 | 0 | 内后视镜调光灵敏度 |
| IntrMirrDimCmdDrvrSide | 2 | 1 | 0 | 内后视镜调光驾驶员侧 |
| IntrMirrDimCmdIntrMirrAsyFanCmpMag | 3 | 1 | 0 | 内后视镜总成风扇补偿幅度 |
| IntrMirrDimCmdIntrMirrDiagcRst | 4 | 1 | 0 | 内后视镜诊断复位 |
| IntrMirrDimCmdIntrMirrEna | 5 | 1 | 0 | 内后视镜使能 |
| IntrMirrDimCmdIntrMirrInhbDim | 6 | 1 | 0 | 内后视镜禁止调光 |
| IntrMirrDimCmdIntrMirrWindHeatrCmpMag | 7 | 1 | 0 | 内后视镜风挡加热器补偿幅度 |

#### ZCLZCLLin5Frame09 (ID: 0x2B)
**帧长度**: 8字节  
**发送节点**: ZCL  
**功能**: 流媒体后视镜控制

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| StreamingMirrBriAdjmt | 0 | 4 | 0 | 流媒体后视镜亮度调节 |
| StreamingMirrImgAdjmt | 4 | 4 | 0 | 流媒体后视镜图像调节 |
| StreamingMirrPosnAdjmt | 8 | 4 | 0 | 流媒体后视镜位置调节 |
| StreamingMirrSwt | 12 | 4 | 0 | 流媒体后视镜开关 |

### RLSM从节点响应帧

#### RLSMZCLLin5Frame02 (ID: 0x02)
**帧长度**: 8字节  
**发送节点**: RLSM  
**功能**: 雨量光照传感器数据

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| SolarSnsrErr | 0 | 1 | 0 | 太阳能传感器错误 |
| RainSnsrDiagcRainSnsrHiVoltDetd | 3 | 1 | 0 | 雨量传感器诊断-高电压检测 |
| RainSnsrDiagcRainSnsrHiTDetd | 4 | 1 | 0 | 雨量传感器诊断-高温检测 |
| LiOprnMod | 5 | 2 | 0 | 光照操作模式 |
| RainDetection | 7 | 1 | 0 | 雨量检测 |
| RainfallAmnt | 8 | 4 | 14 | 降雨量 |
| SolarSnsrLeValue | 16 | 8 | 0 | 太阳能传感器左值 |
| SolarSnsrRiValue | 24 | 8 | 0 | 太阳能传感器右值 |

#### RLSMZCLLin5Frame03 (ID: 0x15)
**帧长度**: 8字节  
**发送节点**: RLSM  
**功能**: 雨刮自动控制和室外亮度状态

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| AutWinWipgCmd | 0 | 3 | 0 | 自动雨刮命令 |
| RainSnsrErrCalErrActv | 8 | 1 | 0 | 雨量传感器错误校准错误激活 |
| RainSnsrErrCalErr | 9 | 1 | 0 | 雨量传感器错误校准错误 |
| RainSnsrErrRainDetnErr | 10 | 1 | 0 | 雨量传感器错误雨量检测错误 |
| RainSnsrErrRainDetnErrActv | 11 | 1 | 0 | 雨量传感器错误雨量检测错误激活 |
| WipgAutFrntMod | 12 | 2 | 0 | 前雨刮自动模式 |
| ErrRespRLSM | 15 | 1 | 0 | RLSM错误响应 |
| TwliBriRawQf | 24 | 2 | 1 | 暮光亮度原始值限定符 |
| TwliBriRaw1 | 26 | 14 | 0 | 暮光亮度原始值1 |
| OutdBriChks8 | 40 | 8 | 0 | 室外亮度校验和8 |
| OutdBriCntr4 | 48 | 4 | 0 | 室外亮度计数器4 |
| OutdBriDataID4 | 52 | 4 | 0 | 室外亮度数据ID4 |
| OutdBriSts | 56 | 2 | 0 | 室外亮度状态 |

#### RLSMZCLLin5Frame08 (ID: 0x2C)
**帧长度**: 8字节  
**发送节点**: RLSM  
**功能**: 舱室温度和湿度传感器数据

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| CmptFrntWindDewT | 0 | 11 | 400 | 舱室前风挡露点温度 |
| RelHumSnsrErr | 42 | 1 | 0 | 相对湿度传感器错误 |
| CmptFrntWindT | 45 | 11 | 400 | 舱室前风挡温度 |
| RelHumSnsrRelHum | 56 | 8 | 0 | 相对湿度传感器相对湿度 |

#### RLSMZCLLin5Frame09 (ID: 0x23)
**帧长度**: 8字节  
**发送节点**: RLSM  
**功能**: 环境光照前向状态和HUD传感器错误

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| AmbIllmnFwdStsChks8 | 0 | 8 | 0 | 环境光照前向状态校验和8 |
| AmbIllmnFwdStsCntr4 | 8 | 4 | 0 | 环境光照前向状态计数器4 |
| AmbIllmnFwdStsDataID4 | 12 | 4 | 0 | 环境光照前向状态数据ID4 |
| AmbIllmnFwdStsAmblillmn1 | 16 | 9 | 0 | 环境光照前向状态环境光照1 |
| AmbIllmnFwdStsAmblillmn2 | 32 | 8 | 0 | 环境光照前向状态环境光照2 |
| HudSnsrErrChks8 | 40 | 8 | 0 | HUD传感器错误校验和8 |
| HudSnsrErrCntr4 | 48 | 4 | 0 | HUD传感器错误计数器4 |
| HudSnsrErrDataID4 | 52 | 4 | 0 | HUD传感器错误数据ID4 |
| HudSnsrErrParChk | 56 | 1 | 0 | HUD传感器错误奇偶校验 |
| HudSnsrErrSnsrErr | 57 | 1 | 0 | HUD传感器错误传感器错误 |

#### RLSMZCLLin5Frame10 (ID: 0x24)
**帧长度**: 8字节  
**发送节点**: RLSM  
**功能**: 环境光照状态和前向光照状态

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| AMBStsChks8 | 0 | 8 | 0 | 环境状态校验和8 |
| AMBStsCntr4 | 8 | 4 | 0 | 环境状态计数器4 |
| AMBStsDataID4 | 12 | 4 | 0 | 环境状态数据ID4 |
| AMBStsAmbLiIllmn | 16 | 16 | 0 | 环境状态环境光照强度 |
| FWStsChks8 | 32 | 8 | 0 | 前向状态校验和8 |
| FWStsCntr4 | 40 | 4 | 0 | 前向状态计数器4 |
| FWStsDataID4 | 44 | 4 | 0 | 前向状态数据ID4 |
| FWStsAmbLiIllmn | 48 | 16 | 0 | 前向状态环境光照强度 |

### RMD从节点响应帧

#### RMDZCLLin5Frame01 (ID: 0x2D)
**帧长度**: 8字节  
**发送节点**: RMD  
**功能**: 流媒体后视镜状态反馈

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| StreamingMirrBri | 0 | 4 | 3 | 流媒体后视镜亮度 |
| StreamingMirrEnable | 4 | 1 | 1 | 流媒体后视镜使能 |
| StreamingMirrImg | 5 | 4 | 3 | 流媒体后视镜图像 |
| StreamingMirrPosn | 9 | 4 | 3 | 流媒体后视镜位置 |
| StreamingMirrReWrnSwtSts | 13 | 1 | 1 | 流媒体后视镜后方警告开关状态 |
| StreamingMirrModeSts | 16 | 4 | 3 | 流媒体后视镜模式状态 |
| ErrRespRMD | 63 | 1 | 0 | RMD错误响应 |

#### IRMMZCLLin5Frame01 (ID: 0x29)
**帧长度**: 8字节  
**发送节点**: RMD  
**功能**: 内后视镜调光响应

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| IntrMirrDimRespIntrMirrDimPerc | 0 | 8 | 0 | 内后视镜调光响应调光百分比 |
| IntrMirrDimRespIntrMirrIntFailr | 8 | 1 | 0 | 内后视镜调光响应内部故障 |
| IntrMirrDimRespResdBoolean | 9 | 1 | 0 | 内后视镜调光响应保留布尔值 |
| IntrMirrDimRespResdUInt6 | 10 | 6 | 0 | 内后视镜调光响应保留UInt6 |

### WMM从节点响应帧

#### WMMZCLLin5Frame03 (ID: 0x25)
**帧长度**: 4字节  
**发送节点**: WMM  
**功能**: 雨刷电机状态和诊断信息

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| WiprActvFromWMM | 8 | 2 | 0 | 来自WMM的雨刷激活状态 |
| WiprInPrkgPosnLoFromWMM | 10 | 1 | 0 | 来自WMM的雨刷在停车位置低 |
| WiprInWipgArFromWMM | 11 | 1 | 0 | 来自WMM的雨刷在刮拭区域 |
| WiprMotErrSafe | 13 | 2 | 0 | 雨刷电机错误安全 |
| ErrRespWMM | 15 | 1 | 0 | WMM错误响应 |
| WiprMotDiagcWiprMotHiVoltDetd | 16 | 2 | 0 | 雨刷电机诊断高电压检测 |
| WiprMotDiagcWiprMotLoVoltDetd | 18 | 2 | 0 | 雨刷电机诊断低电压检测 |
| WiprMotDiagcWiprMotOvldDetd | 20 | 2 | 0 | 雨刷电机诊断过载检测 |
| WshngCycActvFromWMM | 22 | 2 | 0 | 来自WMM的清洗循环激活 |
| WiprMotCrkAg | 24 | 8 | 0 | 雨刷电机曲柄角度 |

#### WMMZCLLin5PartNrFr01 (ID: 0x8)
**帧长度**: 7字节  
**发送节点**: WMM  
**功能**: WMM零件号帧1

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| WMMPartNoCmplNr1 | 0 | 8 | 0 | WMM零件号完整编号1 |
| WMMPartNoCmplNr2 | 8 | 8 | 0 | WMM零件号完整编号2 |
| WMMPartNoCmplNr3 | 16 | 8 | 0 | WMM零件号完整编号3 |
| WMMPartNoCmplNr4 | 24 | 8 | 0 | WMM零件号完整编号4 |
| WMMPartNoCmplEndSgn1 | 32 | 8 | 0 | WMM零件号完整结束标志1 |
| WMMPartNoCmplEndSgn2 | 40 | 8 | 0 | WMM零件号完整结束标志2 |
| WMMPartNoCmplEndSgn3 | 48 | 8 | 0 | WMM零件号完整结束标志3 |

#### WMMZCLLin5PartNrFr02 (ID: 0x19)
**帧长度**: 8字节  
**发送节点**: WMM  
**功能**: WMM零件号帧2

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| WMMPartNo10CmplNr1 | 0 | 8 | 0 | WMM零件号10完整编号1 |
| WMMPartNo10CmplNr2 | 8 | 8 | 0 | WMM零件号10完整编号2 |
| WMMPartNo10CmplNr3 | 16 | 8 | 0 | WMM零件号10完整编号3 |
| WMMPartNo10CmplNr4 | 24 | 8 | 0 | WMM零件号10完整编号4 |
| WMMPartNo10CmplNr5 | 32 | 8 | 0 | WMM零件号10完整编号5 |
| WMMPartNo10CmplEndSgn1 | 40 | 8 | 0 | WMM零件号10完整结束标志1 |
| WMMPartNo10CmplEndSgn2 | 48 | 8 | 0 | WMM零件号10完整结束标志2 |
| WMMPartNo10CmplEndSgn3 | 56 | 8 | 0 | WMM零件号10完整结束标志3 |

#### WMMZCLLin5SerNrFr01 (ID: 0x28)
**帧长度**: 7字节  
**发送节点**: WMM  
**功能**: WMM序列号帧

| 信号名称 | 起始位 | 位长度 | 默认值 | 功能描述 |
|---------|--------|--------|--------|----------|
| WMMSerNoNr1 | 0 | 8 | 0 | WMM序列号编号1 |
| WMMSerNoNr2 | 8 | 8 | 0 | WMM序列号编号2 |
| WMMSerNoNr3 | 16 | 8 | 0 | WMM序列号编号3 |
| WMMSerNoNr4 | 24 | 8 | 0 | WMM序列号编号4 |

### 产品信息帧

#### 零件号帧 (各节点)
- **IRMMZCLLin5PartNrFr01** (ID: 0x0B): RMD零件号帧1
- **IRMMZCLLin5PartNrFr02** (ID: 0x0C): RMD零件号帧2
- **RLSMZCLLin5PartNrFr01** (ID: 0x20): RLSM零件号帧1
- **RLSMZCLLin5PartNrFr02** (ID: 0x18): RLSM零件号帧2
- **WMMZCLLin5PartNrFr01** (ID: 0x08): WMM零件号帧1
- **WMMZCLLin5PartNrFr02** (ID: 0x19): WMM零件号帧2

#### 序列号帧 (各节点)
- **IRMMZCLLin5SerNrFr01** (ID: 0x0D): RMD序列号帧
- **RLSMZCLLin5SerNrFr01** (ID: 0x22): RLSM序列号帧
- **WMMZCLLin5SerNrFr01** (ID: 0x28): WMM序列号帧

### 诊断帧
- **MasterReq** (ID: 0x3C): 主节点诊断请求 (8字节)
- **SlaveResp** (ID: 0x3D): 从节点诊断响应 (8字节)

## 调度表配置

### ZCLLin5ScheduleSerNrPartNr
专用于序列号和零件号传输的调度表，包含：
- SerNr帧传输 (10ms间隔)
- PartNr帧传输 (10ms间隔)

### ZCLLin5ScheduleTable01
主要数据传输调度表，包含：
- ZCL_to_RLSM_01 (10ms)
- RLSM_to_ZCL_01 (20ms)
- ZCL_to_RMD_01 (30ms)
- RMD_to_ZCL_01 (40ms)
- ZCL_to_WMM_01 (50ms)
- WMM_to_ZCL_01 (60ms)

## 信号编码类型详细定义

### 数值编码类型

#### UInt4 (4位无符号整数)
- **范围**: 0-15
- **物理值**: 物理值 = 原始值 × 1 + 0
- **单位**: 无单位
- **应用信号**: 流媒体后视镜亮度、图像、模式、位置等控制信号

#### UInt6 (6位无符号整数)
- **范围**: 0-63
- **物理值**: 物理值 = 原始值 × 1 + 0
- **单位**: 无单位

#### UInt8 (8位无符号整数)
- **范围**: 0-255
- **物理值**: 物理值 = 原始值 × 1 + 0
- **单位**: 无单位
- **应用信号**: 零件号、序列号、校验和等

#### Nr5 (5位数值)
- **范围**: 0-7
- **物理值**: 物理值 = 原始值 × 1 + 0
- **单位**: 无单位

#### Liner32 (32级线性编码)
- **范围**: 0-511
- **物理值**: 物理值 = 原始值 × 1.0 + 0.0
- **单位**: 无单位
- **应用**: 环境光照前向状态

### 百分比编码类型

#### Perc2 (2位百分比)
- **范围**: 0-200
- **物理值**: 物理值 = 原始值 × 0.5 + 0.0
- **单位**: %
- **分辨率**: 0.5%
- **应用**: 相对湿度传感器

#### Perc5 (5位百分比)
- **范围**: 0-31
- **物理值**: 物理值 = 原始值 × 3.125 + 0.0
- **单位**: %
- **分辨率**: 3.125%
- **应用**: 内后视镜调光百分比

### 布尔编码类型

#### Boolean (标准布尔值)
- **0**: False
- **1**: True
- **应用**: 内后视镜调光响应、雨量检测、离线监控使能

#### OnOff1 (开关状态)
- **0**: "OnOff1_Off" (关闭)
- **1**: "OnOff1_On" (开启)
- **应用**: 雨量传感器诊断、流媒体后视镜使能、雨刮激活等

#### OnOffCrit1 (关键开关状态)
- **0**: "OnOffCrit1_NotVld1" (无效1)
- **1**: "OnOffCrit1_Off" (关闭)
- **2**: "OnOffCrit1_On" (开启)
- **3**: "OnOffCrit1_NotVld2" (无效2)
- **应用**: 雨刮电机诊断、电源激活安全等

#### OnOffNoReq (无请求开关状态)
- **0**: "OnOffNoReq_NoReq" (无请求)
- **1**: "OnOffNoReq_On" (开启)
- **2**: "OnOffNoReq_Off" (关闭)
- **应用**: 舱室组电源请求、流媒体后视镜开关

#### Flg1 (标志位)
- **0**: "Flg1_Rst" (复位)
- **1**: "Flg1_Set" (设置)
- **应用**: 内后视镜调光诊断复位

### 故障编码类型

#### FailrNoFailr1 (故障/无故障)
- **0**: 无故障
- **1**: 故障
- **应用**: 内后视镜内部故障

#### Err1 (错误状态)
- **0**: "Err1_NoErr" (无错误)
- **1**: "Err1_Err" (错误)
- **应用**: 太阳能传感器错误、相对湿度传感器错误

#### GenQf1 (通用限定符)
- **0**: "GenQf1_UndefindDataAccur" (未定义数据精度)
- **1**: "GenQf1_TmpUndefdData" (临时未定义数据)
- **2**: "GenQf1_DataAccurNotWithinSpcn" (数据精度不在规范内)
- **3**: "GenQf1_AccurData" (精确数据)
- **应用**: 暮光亮度原始值限定符

### 专用编码类型

#### LiOperMod (光照操作模式)
- **0**: "LiOperMod_Off" (关闭)
- **1**: "LiOperMod_Manual" (手动)
- **2**: "LiOperMod_Auto" (自动)
- **3**: "LiOperMod_Invalid" (无效)

#### WipgSpd2 (雨刮速度)
- **0**: "WipgSpd2_WipgSpd0Rpm" (0 RPM - 停止)
- **1**: "WipgSpd2_WipgSpd40Rpm" (40 RPM)
- **2**: "WipgSpd2_WipgSpd43Rpm" (43 RPM)
- **3**: "WipgSpd2_WipgSpd46Rpm" (46 RPM)
- **4**: "WipgSpd2_WipgSpd50Rpm" (50 RPM)
- **5**: "WipgSpd2_WipgSpd54Rpm" (54 RPM)
- **6**: "WipgSpd2_WipgSpd57Rpm" (57 RPM)
- **7**: "WipgSpd2_WipgSpd60Rpm" (60 RPM)

#### WipgAutFrntMod (前雨刮自动模式)
- **0**: "WipgAutFrntMod_Off" (关闭)
- **1**: "WipgAutFrntMod_ImdtMod" (立即模式)
- **2**: "WipgAutFrntMod_IntlMod" (间歇模式)
- **3**: "WipgAutFrntMod_ContnsMod" (连续模式)

#### WipgSpdIntlFromHmi (来自HMI的雨刮间歇速度)
- **0**: "WipgSpdIntlFromHmi_Posn0" (位置0)
- **1**: "WipgSpdIntlFromHmi_Posn1" (位置1)
- **2**: "WipgSpdIntlFromHmi_Posn2" (位置2)
- **3**: "WipgSpdIntlFromHmi_Posn3" (位置3)
- **4**: "WipgSpdIntlFromHmi_Posn4" (位置4)
- **5**: "WipgSpdIntlFromHmi_Posn5" (位置5)
- **6**: "WipgSpdIntlFromHmi_Posn6" (位置6)
- **7**: "WipgSpdIntlFromHmi_Posn7" (位置7)

#### CarModSts1 (车辆模式状态)
- **0**: "CarModSts1_CarModNorm" (正常模式)
- **1**: "CarModSts1_CarModTrnsp" (运输模式)
- **2**: "CarModSts1_CarModFcy" (工厂模式)
- **3**: "CarModSts1_CarModCrash" (碰撞模式)
- **5**: "CarModSts1_CarModDyno" (测功机模式)

#### PowerModeState (电源模式状态)
- **2**: "PowerModeState_IDLE" (空闲)
- **4**: "PowerModeState_ACTIVE" (激活)
- **8**: "PowerModeState_DRIVING" (驾驶)

#### IntrMirrDimSnvtyTyp (内后视镜调光灵敏度类型)
- **0**: "IntrMirrDimSnvtyTyp_Normal" (正常)
- **1**: "IntrMirrDimSnvtyTyp_Dark" (暗)
- **2**: "IntrMirrDimSnvtyTyp_Light" (亮)
- **3**: "IntrMirrDimSnvtyTyp_Inhibit" (禁止)

#### RiLeTyp (左右类型)
- **0**: "RiLeTyp_Right" (右)
- **1**: "RiLeTyp_Left" (左)
- **应用**: 内后视镜调光命令驾驶员侧

#### OutdBriSts (室外亮度状态)
- **0**: "OutdBriSts_Ukwn" (未知)
- **1**: "OutdBriSts_Night" (夜晚)
- **2**: "OutdBriSts_Day" (白天)
- **3**: "OutdBriSts_Invld" (无效)

#### FltEgyCns1 (故障能耗)
- **0**: "FltEgyCns1_NoFlt" (无故障)
- **1**: "FltEgyCns1_Flt" (故障)

#### FltStsSlaveBasc (从节点基础故障状态)
- **0**: "FltStsSlaveBasc_FltStsTestPassd" (故障状态测试通过)
- **1**: "FltStsSlaveBasc_FltStsTestFaild" (故障状态测试失败)

#### ParChks1 (奇偶校验)
- **0**: "ParChks1_Unevennrof1" (奇数个1)
- **1**: "ParChks1_Evennrof1" (偶数个1)

### 物理值编码类型

#### AmntSnsr (环境传感器)
- **范围**: 0-15
- **逻辑值定义**:
  - 0-13: "AmntSnsr_Amnt0" 到 "AmntSnsr_Amnt13" (数量0-13)
  - 14: "AmntSnsr_InitValue" (初始值)
  - 15: "AmntSnsr_Error" (错误)

#### Sunload2D (二维太阳负荷)
- **范围**: 0-255
- **物理值**: 物理值 = 原始值 × 5.0 + 0.0
- **单位**: W/m²
- **应用**: 太阳能传感器左值、右值

#### LiIllmn (光照强度)
- **范围**: 0-65534
- **物理值**: 物理值 = 原始值 × 1 + 0
- **单位**: 无单位
- **应用**: 环境光照强度、前向环境光照强度

#### TwliBriRaw1 (暮光亮度原始值)
- **范围**: 0-10000
- **物理值**: 物理值 = 原始值 × 1 + 0
- **单位**: 无单位

#### TByCmptmtSnsrRelHum (舱室传感器相对湿度)
- **范围**: 0-2047
- **物理值**: 物理值 = 原始值 × 0.1 - 40.0
- **单位**: °C
- **应用**: 舱室前风挡露点温度、舱室前风挡温度

#### WipAg (雨刮角度)
- **范围**: 0-255
- **物理值**: 物理值 = 原始值 × 1 + 0
- **单位**: 无单位
- **应用**: 雨刮电机曲柄角度

#### WipAgOffs (雨刮角度偏移)
- **范围**: 0-15
- **物理值**: 物理值 = 原始值 × 1 + 0
- **单位**: 度 (Deg)
- **应用**: 雨刮电机前偏移角度

#### RainSnsrThd (雨量传感器阈值)
- **范围**: 0-15
- **物理值**: 物理值 = 原始值 × 5.0 - 40.0
- **单位**: %
- **应用**: 雨量传感器光阈值

#### MvBattSwVersAct (移动电池软件版本激活)
- **范围**: 0-255
- **物理值**: 物理值 = 原始值 × 1.0 + 0.0
- **单位**: 无单位
- **应用**: 环境光照前向状态环境光照2

## 节点属性配置

### RLSM节点属性
- **LIN协议版本**: "2.1"
- **NAD**: 0x01
- **产品ID**: 0x001E, 0x0001, 0x01
- **响应错误**: SlaveRespB0
- **可配置帧**: RLSM_to_ZCL_01, SerNr, PartNr

### RMD节点属性
- **LIN协议版本**: "2.1"
- **NAD**: 0x02
- **产品ID**: 0x001E, 0x0002, 0x01
- **响应错误**: SlaveRespB0
- **可配置帧**: RMD_to_ZCL_01, SerNr, PartNr

### WMM节点属性
- **LIN协议版本**: "2.1"
- **NAD**: 0x03
- **产品ID**: 0x001E, 0x0003, 0x01
- **响应错误**: SlaveRespB0
- **可配置帧**: WMM_to_ZCL_01, SerNr, PartNr

## 系统特性总结

### 功能特点
1. **多功能集成**: 集成了流媒体后视镜、内后视镜调光、雨刮控制、雨量光照传感等多个系统
2. **智能控制**: 支持自动模式和手动模式切换
3. **状态反馈**: 每个控制信号都有对应的状态反馈
4. **诊断支持**: 完整的诊断帧结构，支持故障检测和状态监控
5. **产品追溯**: 支持序列号和零件号管理

### 技术特点
1. **标准LIN协议**: 遵循LIN 2.1协议标准
2. **高效调度**: 优化的帧调度，确保实时性
3. **灵活编码**: 多种信号编码类型，适应不同数据需求
4. **可扩展性**: 预留诊断和配置接口，便于系统扩展

### 应用场景
该LIN网络主要应用于高端车型的智能后视镜系统，提供：
- 流媒体后视镜显示控制
- 自动防眩目功能
- 智能雨刮控制
- 环境光照自适应
- 系统诊断和维护支持

---

*文档生成时间: 2024年*  
*基于文件: ZSDB225100_EX1H_High_ZCL_LIN5_V5.ldf*