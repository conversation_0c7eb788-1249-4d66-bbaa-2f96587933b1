
# Input Enable
$BE,6A,FF,FF,FF,00,00,FF
# Driving Select
$BE,70,A6,00,00,00,80,70
# Pin Function Select
$BE,77,17,C2,00,E5,00,00
# MIPI Rx
$BE,20,47		# Soft-Reset PHY/CSI/FIFO
$BE,10,30,F1,55,55,55,00,08,00,0F
$BE,1D,FF,FF,98
$BE,21,14,01,F0,10,3C,12
$BE,2D,07,19,09,17,02
$BE,20,40
# Main Path Digital Video Input
$B8,00,00		# VXi_Access_Sel = Main Path
$B8,01,0C,80,C7,80,A1,15,03,00,3F,32,86,41,A0,04,20,00,00,00,00,80,07,80,01,10,33,28,68,00,00,00
# Scaling Ratio
$BA,02,00,00,01,00,00,01
$BA,0C,40,00
# Pattern Gen.
$BA,76,00,39,00,00,00
# LineBuffer Timing
$BA,8C,6D,29,05
# Panel Output Timing
$BA,90,0A,00,20,00,80,07,F0,07,02,00,14,00,80,01,F7,01
# Panel Control
$BA,A9,2C,00,00,00,00,28
# LVDS Tx1
$BA,BB,01		# LvTx_Cfg_Sel = 01b: LvT1 only
$BA,BC,B9,03,74,00
# DPLL
$BE,90,41,07,02,88,46,40,66,10
# Panel Type Select
$BE,7D,01
# Power Down
$B8,F6,B1
# Reset TXLVDS PLL
$BA,BB,01,B9		# set LvT1_pll_rst