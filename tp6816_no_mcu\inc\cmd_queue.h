/*
 * @file      cmd_queue.h
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:50 created
 * $Id: cmd_queue.h 1.1 $
 */

#ifndef CMD_QUEUE_H_
#define CMD_QUEUE_H_

#include "stdint.h"
#include "tp6806.h"
/*
#define uint8_t unsigned char
#define uint16_t unsigned short
#define uint32_t unsigned long
#define int8_t char
#define int16_t short
#define int32_t long
*/
#define CQ_BASE                 (0xE0)
#define CQ_CTRL                 (0x00+CQ_BASE)
    #define CQ_TRIG_BIT         BIT(7)
	#define CQ_BUSY_BIT         BIT(7)
    #define CQ_CUR_BIT          BIT(6)
	#define BFN_TRIG_ST         4
    #define BFN_TRIG_LEN        2
		#define CQ_SW_TRIG		0
		#define CQ_VDE_TRIG		1
		#define CQ_VS_TRIG		2
		#define CQ_OSD_TRIG		3
	#define CQ_LIMIT_BIT		BIT(2)
    #define CQ_DELAY_BIT		BIT(1)
	#define CQ_PTRRST_BIT		BIT(0)
#define CQ_DPORT             	(0x01+CQ_BASE)	// FF94XXh
#define CQ_P_PTR               	(0x02+CQ_BASE)
#define CQ_E_PTR               	(0x03+CQ_BASE)
#define CQ_DELAY_L              (0x04+CQ_BASE)
#define CQ_DELAY_H            	(0x05+CQ_BASE)
#define CQ_LIMIT_L            	(0x06+CQ_BASE)
#define CQ_LIMIT_H              (0x07+CQ_BASE)
#define CQ_TIMEOUT              (0x08+CQ_BASE)
#define CQ_CTRL2                (0x09+CQ_BASE)
    #define CQ_LCNT_BIT         BIT(2)
    #define CQ_LOCK_BIT         BIT(1)
	#define CQ_RST_BIT          BIT(0)
#define CQ_LCNT_L            	(0x0A+CQ_BASE)
#define CQ_LCNT_H              	(0x0B+CQ_BASE)
#define CQ_INT             		(0x0C+CQ_BASE)
    #define CQ_TOUT_IS_BIT		BIT(5)
    #define CQ_DONE_IS_BIT		BIT(4)
    #define CQ_TOUT_IM_BIT    	BIT(1)
    #define CQ_DONE_IM_BIT      BIT(0)

#define CQ_SHORT_CODE		BIT(7)
#define CQ_SHORT_LEN(len)	(((len - 1) & 0x7) << 4)
#define CQ_ADDR_INC			BIT(3)
#define CQ_SELECT_VD		BIT(2)
#define CQ_PAGE_ADDR(page)	(page & 0x3)
#define CQ_LONG_CODE		(0x01 << 6)
#define CQ_LONG_LEN_9_8(len)	(((len - 1) & 0x300) >> 4)
#define CQ_LONG_LEN_7_0(len)	((len - 1) & 0xff)

#define CQ_LOOPS_CODE	0x10
#define CQ_LOOPE_CODE	0x10
#define CQ_FINISH_CODE	0x02

typedef struct {
	uint8_t page_addr	:2;
	uint8_t sel_vd    	:1;
    uint8_t addr_inc    :1;
    uint8_t len     	:3;
    uint8_t hcode		:1;
} cq_shead_t;

typedef struct {
	cq_shead_t	head;
    uint8_t    	addr;
    uint8_t     *wdata;
} cq_short_t;

typedef struct {
	uint8_t page_addr	:2;
	/* TODO: page_addr 2 -> 3bits page_addr[2] == sel_vd */
	uint8_t sel_vd    	:1;
    uint8_t addr_inc    :1;
    uint8_t len9_8     	:2;
    uint8_t hcode		:2;
} cq_lhead_t;

typedef struct {
	cq_lhead_t	head;
	uint8_t		leng7_0;
    uint8_t    	addr;
    uint8_t     *wdata;
} cq_long_t;

extern void cq_init(void);
extern void cq_set_trig_mode(uint8_t mode);
extern void cq_ptr_rst(void);
extern void cq_cfg_short(cq_short_t *ptbl, uint8_t page, uint8_t addr, uint8_t inc, uint8_t len, uint8_t *wdata);
extern void cq_cfg_long(cq_long_t *ptbl, uint8_t page, uint8_t addr, uint8_t inc, uint8_t len, uint8_t *wdata);
extern void cq_wr_long(cq_long_t *ptbl);
extern void cq_wr_short(cq_short_t *ptbl);
extern void cq_wr_finish(void);
extern bit cq_issue(void);
extern bit cq_pause_issue(short ms);
extern void cq_wr_data(unsigned char val);

#define cq_loops(cnt) 	cq_wr_data((CQ_LOOPS_CODE | (cnt & 0xf)))
#define cq_loope() 		cq_wr_data(CQ_LOOPE_CODE)

#define CQ_M_PA_CODE	0x08
#define cq_maniplate(page, addr, dat) \
	do { \
		cq_wr_data((CQ_M_PA_CODE | (page & 0x3))); \
		cq_wr_data(addr); \
		cq_wr_data(dat); \
	} while(0);

#define CQ_PAUSE_CODE	0x20
#define cq_pause(cnt)	cq_wr_data((CQ_PAUSE_CODE | (cnt & 0x1f)))

#define CQ_MAX_CNT	256
#define check_cq_cnt(cnt)	(((CQ_MAX_CNT - cq_buf_ptr) > cnt)? 0 : 1)

extern unsigned char cq_buf_ptr;

#include "tp6823.h"
#if defined(_EX_MCU_VERSION)
extern void cq_wr_ptr_data(unsigned char ptr, unsigned char val);

#define CQ_CFG_1B_ISSUE(vd_sel, page, idx, val) \
	do { \
        write_p0(CQ_P_PTR, 0); \
		write_p0(CQ_DPORT, (CQ_SHORT_CODE | (vd_sel? CQ_SELECT_VD : 0) | page)); \
		write_p0(CQ_DPORT, idx); \
		write_p0(CQ_DPORT, val); \
        write_p0(CQ_DPORT, CQ_FINISH_CODE); \
        cq_issue(); \
	} while(0);
#define CQ_CFG_2B_ISSUE(vd_sel, page, idx, val) \
	do { \
		write_p0(CQ_P_PTR, 0); \
		write_p0(CQ_DPORT, (CQ_SHORT_CODE | (vd_sel? CQ_SELECT_VD : 0) | (1 << 4) | page)); \
		write_p0(CQ_DPORT, idx); \
		write_p0(CQ_DPORT, (val & 0xff)); \
        write_p0(CQ_DPORT, ((val >> 8) & 0xff)); \
        write_p0(CQ_DPORT, CQ_FINISH_CODE); \
        cq_issue(); \
	} while(0);  
#define CQ_CFG_1B_(vd_sel, page, idx, val) \
	do { \
		write_p0(CQ_DPORT, (CQ_SHORT_CODE | (vd_sel? CQ_SELECT_VD : 0) | page)); \
		write_p0(CQ_DPORT, idx); \
		write_p0(CQ_DPORT, val); \
	} while(0);
#define CQ_CFG_2B_(vd_sel, page, idx, val) \
	do { \
		write_p0(CQ_DPORT, (CQ_SHORT_CODE | (vd_sel? CQ_SELECT_VD : 0) | (1 << 4) | page)); \
		write_p0(CQ_DPORT, idx); \
		write_p0(CQ_DPORT, (val & 0xff)); \
        write_p0(CQ_DPORT, ((val >> 8) & 0xff)); \
	} while(0); 
#define CQ_CNT_START()      write_p0(CQ_P_PTR, 0);
#define CQ_FINISH_ISSUE() \
    do { \
		write_p0(CQ_DPORT, CQ_FINISH_CODE); \
        cq_issue(); \
	} while(0);
	
#define CQ_PAUSE_FRAME(cnt)	\
    do { \
		write_p0(CQ_DPORT, (CQ_PAUSE_CODE | (cnt & 0x1f))); \
	} while(0);

#define CQ_LOOP_START(cnt)	\
    do { \
		write_p0(CQ_DPORT, (CQ_LOOPS_CODE | (cnt & 0xf))); \
	} while(0);

#define CQ_LOOP_END(cnt)	\
    do { \
		write_p0(CQ_DPORT, CQ_LOOPE_CODE); \
	} while(0);

#define CQ_MANIPLATE_CTRL(page, addr, dat) \
	do { \
		write_p0(CQ_DPORT, (CQ_M_PA_CODE | (page & 0x3))); \
		write_p0(CQ_DPORT, addr); \
		write_p0(CQ_DPORT, dat); \
	} while(0);

#define CQ_SET_FINISH() \
    do { \
		write_p0(CQ_DPORT, CQ_FINISH_CODE); \
	} while(0);

#define CQ_PUT_DAT(dat)		write_p0(CQ_DPORT, dat)

#define CQ_CFG_HEAD_SHORT_(vd_sel, page, idx, len) \
	do { \
        write_p0(CQ_DPORT, (CQ_SHORT_CODE | CQ_SHORT_LEN(len) | CQ_ADDR_INC | (vd_sel? CQ_SELECT_VD : 0) | page)); \
		write_p0(CQ_DPORT, idx); \
	} while(0);
#define CQ_CFG_HEAD_LONG_(vd_sel, page, idx, len) \
	do { \
        write_p0(CQ_DPORT, (CQ_LONG_CODE | CQ_LONG_LEN_9_8(len) | CQ_ADDR_INC | (vd_sel? CQ_SELECT_VD : 0) | page)); \
		write_p0(CQ_DPORT, CQ_LONG_LEN_7_0(len)); \
		write_p0(CQ_DPORT, idx); \
	} while(0);

#define CQ_CFG_BURST_HEADER_P2(idx, cnt) \
	{ \
		write_p0(CQ_DPORT, (CQ_SHORT_CODE | ((cnt - 1) << 4) | 0x08 | 2)); \
		write_p0(CQ_DPORT, idx); \
	}

#define CQ_PUT_AUTO_SWITCH_START_() \
	{ \
		write_p0(CQ_P_PTR, 0);	\
		write_p0(CQ_DPORT, 0x89); \
		write_p0(CQ_DPORT, 0xB4); \
		write_p0(CQ_DPORT, 0x80); \
		write_p0(CQ_DPORT, 0x88); \
		write_p0(CQ_DPORT, 0xE0); \
		write_p0(CQ_DPORT, 0x14); \
		write_p0(CQ_DPORT, 0x21); \
		cq_buf_ptr = 7;	\
	}
#define CQ_PUT_AUTO_SWITCH_END_(ptr) \
	{ \
		write_p0(CQ_P_PTR, ptr + 6);	\
		write_p0(CQ_DPORT, 0x89); \
		write_p0(CQ_DPORT, 0xB4); \
		write_p0(CQ_DPORT, 0x40); \
		write_p0(CQ_DPORT, CQ_FINISH_CODE); \
	}


#else	/* defined(!_EX_MCU_VERSION) */
#define cq_wr_ptr_data(ptr, val)	\
	{ \ 
		CQ_BUFFER[ptr] = val;	\
	}

#define CQ_CFG_1B_ISSUE(vd_sel, page, idx, val) \
	do { \
        CQ_BUFFER[0] = (CQ_SHORT_CODE | (vd_sel? CQ_SELECT_VD : 0) | page); \
		CQ_BUFFER[1] = idx; \
		CQ_BUFFER[2] = val; \
        CQ_BUFFER[3] = CQ_FINISH_CODE; \
        cq_issue(); \
	} while(0);
#define CQ_CFG_2B_ISSUE(vd_sel, page, idx, val) \
	do { \
		CQ_BUFFER[0] = (CQ_SHORT_CODE | (vd_sel? CQ_SELECT_VD : 0) | (1 << 4) | page); \
		CQ_BUFFER[1] = idx; \
		CQ_BUFFER[2] = (val & 0xff); \
        CQ_BUFFER[3] = ((val >> 8) & 0xff); \
        CQ_BUFFER[4] = CQ_FINISH_CODE; \
        cq_issue(); \
	} while(0);  
#define CQ_CFG_1B_(vd_sel, page, idx, val) \
	do { \
        CQ_BUFFER[cq_buf_ptr++] = (CQ_SHORT_CODE | (vd_sel? CQ_SELECT_VD : 0) | page); \
		CQ_BUFFER[cq_buf_ptr++] = idx; \
		CQ_BUFFER[cq_buf_ptr++] = val; \
	} while(0);
#define CQ_CFG_2B_(vd_sel, page, idx, val) \
	do { \
		CQ_BUFFER[cq_buf_ptr++] = (CQ_SHORT_CODE | (vd_sel? CQ_SELECT_VD : 0) | (1 << 4) | page); \
		CQ_BUFFER[cq_buf_ptr++] = idx; \
		CQ_BUFFER[cq_buf_ptr++] = (val & 0xff); \
        CQ_BUFFER[cq_buf_ptr++] = ((val >> 8) & 0xff); \
	} while(0); 
#define CQ_CNT_START()      cq_buf_ptr = 0;
#define CQ_FINISH_ISSUE() \
    do { \
		CQ_BUFFER[cq_buf_ptr++] = CQ_FINISH_CODE; \
        cq_issue(); \
	} while(0);
	
#define CQ_PAUSE_FRAME(cnt)	\
    do { \
		CQ_BUFFER[cq_buf_ptr++] = (CQ_PAUSE_CODE | (cnt & 0x1f)); \
	} while(0);

#define CQ_LOOP_START(cnt)	\
    do { \
		CQ_BUFFER[cq_buf_ptr++] = (CQ_LOOPS_CODE | (cnt & 0xf)); \
	} while(0);

#define CQ_LOOP_END(cnt)	\
    do { \
		CQ_BUFFER[cq_buf_ptr++] = (CQ_LOOPE_CODE); \
	} while(0);

#define CQ_MANIPLATE_CTRL(page, addr, dat) \
	do { \
		CQ_BUFFER[cq_buf_ptr++] = ((CQ_M_PA_CODE | (page & 0x3))); \
		CQ_BUFFER[cq_buf_ptr++] = (addr); \
		CQ_BUFFER[cq_buf_ptr++] = (dat); \
	} while(0);

#define CQ_SET_FINISH() \
    do { \
		CQ_BUFFER[cq_buf_ptr++] = CQ_FINISH_CODE; \
	} while(0);

#define CQ_CFG_HEAD_SHORT_(vd_sel, page, idx, len) \
	do { \
        CQ_BUFFER[cq_buf_ptr++] = (CQ_SHORT_CODE | CQ_SHORT_LEN(len) | CQ_ADDR_INC | (vd_sel? CQ_SELECT_VD : 0) | page); \
		CQ_BUFFER[cq_buf_ptr++] = idx; \
	} while(0);
#define CQ_CFG_HEAD_LONG_(vd_sel, page, idx, len) \
	do { \
        CQ_BUFFER[cq_buf_ptr++] = (CQ_LONG_CODE | CQ_LONG_LEN_9_8(len) | CQ_ADDR_INC | (vd_sel? CQ_SELECT_VD : 0) | page); \
		CQ_BUFFER[cq_buf_ptr++] = CQ_LONG_LEN_7_0(len); \
		CQ_BUFFER[cq_buf_ptr++] = idx; \
	} while(0);
#define CQ_PUT_DAT(dat)		CQ_BUFFER[cq_buf_ptr++] = dat;

#define CQ_CFG_BURST_HEADER_P2(idx, cnt) \
	{ \
		CQ_BUFFER[cq_buf_ptr++] = (CQ_SHORT_CODE | ((cnt - 1) << 4) | 0x08 | 2); \
		CQ_BUFFER[cq_buf_ptr++] = idx; \
	}

#define CQ_PUT_AUTO_SWITCH_START_() \
	{ \
		CQ_BUFFER[0] = 0x89; \
		CQ_BUFFER[1] = 0xB4; \
		CQ_BUFFER[2] = 0x80; \
		CQ_BUFFER[3] = 0x88; \
		CQ_BUFFER[4] = 0xE0; \
		CQ_BUFFER[5] = 0x14; \
		CQ_BUFFER[6] = 0x21; \
		cq_buf_ptr = 7;	\
	}
#define CQ_PUT_AUTO_SWITCH_END_(ptr) \
	{ \
		CQ_BUFFER[ptr + 6] = 0x89; \
		CQ_BUFFER[ptr + 7] = 0xB4; \
		CQ_BUFFER[ptr + 8] = 0x40; \
		CQ_BUFFER[ptr + 9] = CQ_FINISH_CODE; \
	}

#endif	/* defined(!_EX_MCU_VERSION) */

#define CQ_SCALER_1B_ISSUE(page, idx, val)    CQ_CFG_1B_ISSUE(0, page, idx, val)
#define CQ_SCALER_2B_ISSUE(page, idx, val)    CQ_CFG_2B_ISSUE(0, page, idx, val)
#define CQ_P0_1B_ISSUE(idx, val)              CQ_SCALER_1B_ISSUE(0, idx, val)
#define CQ_P0_2B_ISSUE(idx, val)              CQ_SCALER_2B_ISSUE(0, idx, val)
#define CQ_P1_1B_ISSUE(idx, val)              CQ_SCALER_1B_ISSUE(1, idx, val)
#define CQ_P1_2B_ISSUE(idx, val)              CQ_SCALER_2B_ISSUE(1, idx, val)
#define CQ_P2_1B_ISSUE(idx, val)              CQ_SCALER_1B_ISSUE(2, idx, val)
#define CQ_P2_2B_ISSUE(idx, val)              CQ_SCALER_2B_ISSUE(2, idx, val)
#define CQ_P3_1B_ISSUE(idx, val)              CQ_SCALER_1B_ISSUE(3, idx, val)
#define CQ_P3_2B_ISSUE(idx, val)              CQ_SCALER_2B_ISSUE(3, idx, val)
#define CQ_VD_1B_ISSUE(idx, val)              CQ_CFG_1B_ISSUE(1, 0, idx, val)
#define CQ_VD_2B_ISSUE(idx, val)              CQ_CFG_2B_ISSUE(1, 0, idx, val)
#define CQ_SCALER_1B_(page, idx, val)    CQ_CFG_1B_(0, page, idx, val)
#define CQ_SCALER_2B_(page, idx, val)    CQ_CFG_2B_(0, page, idx, val)
#define CQ_P0_1B_(idx, val)              CQ_SCALER_1B_(0, idx, val)
#define CQ_P0_2B_(idx, val)              CQ_SCALER_2B_(0, idx, val)
#define CQ_P1_1B_(idx, val)              CQ_SCALER_1B_(1, idx, val)
#define CQ_P1_2B_(idx, val)              CQ_SCALER_2B_(1, idx, val)
#define CQ_P2_1B_(idx, val)              CQ_SCALER_1B_(2, idx, val)
#define CQ_P2_2B_(idx, val)              CQ_SCALER_2B_(2, idx, val)
#define CQ_P3_1B_(idx, val)              CQ_SCALER_1B_(3, idx, val)
#define CQ_P3_2B_(idx, val)              CQ_SCALER_2B_(3, idx, val)
#define CQ_VD_1B_(idx, val)              CQ_CFG_1B_(1, 0, idx, val)
#define CQ_VD_2B_(idx, val)              CQ_CFG_2B_(1, 0, idx, val)
#define CQ_SCALER_HEAD_SHORT_(page, idx, len)	CQ_CFG_HEAD_SHORT_(0, page, idx, len)
#define CQ_SCALER_HEAD_LONG_(page, idx, len)	CQ_CFG_HEAD_LONG_(0, page, idx, len)
#define CQ_P0_HEAD_SHORT_(idx, len)             CQ_CFG_HEAD_SHORT_(0, idx, len)
#define CQ_P0_HEAD_LONG_(idx, len)              CQ_CFG_HEAD_LONG_(0, idx, len)
#define CQ_P1_HEAD_SHORT_(idx, len)             CQ_CFG_HEAD_SHORT_(1, idx, len)
#define CQ_P1_HEAD_LONG_(idx, len)              CQ_CFG_HEAD_LONG_(1, idx len)
#define CQ_P2_HEAD_SHORT_(idx, len)             CQ_CFG_HEAD_SHORT_(2, idx len)
#define CQ_P2_HEAD_LONG_(idx, len)              CQ_CFG_HEAD_LONG_(2, idx len)
#define CQ_P3_HEAD_SHORT_(idx, len)             CQ_CFG_HEAD_SHORT_(3, idx, len)
#define CQ_P3_HEAD_LONG_(idx, len)              CQ_CFG_HEAD_LONG_(3, idx, len)
#define CQ_VD_HEAD_SHORT_(idx, len)             CQ_CFG_HEAD_SHORT_(1, 0, idx, len)
#define CQ_VD_HEAD_LONG_(idx, len)              CQ_CFG_HEAD_LONG_(1, 0, idx, len)


#endif /* CMD_QUEUE_H_ */
