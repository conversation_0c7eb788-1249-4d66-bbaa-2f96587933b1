/*
 * @file      osd_func.h
 * @brief     
 * <AUTHOR>
 * @date      2020-06-12 12:59:50 created
 * $Id: osd_func.h 1.1 $
 */

#ifndef OSD_FUNC_H_
#define OSD_FUNC_H_

#include "tp6823.h"

#define OSD_RAM_SIZE		(0x8000)//(32 * 1024)
#define OSD_MLUT_STA_PTR    0x0000
#define OSD_BGC_LUT_STA_PTR 0x0100
#define OSD_FGC_LUT_STA_PTR 0x0110
#define OSD_C2REMAP_STA_PTR 0x0120
#define OSD_WIN_MAT_LUT_STA_PTR 0x0130
#define OSD_RLG0_START_PTR  0x0140
#define OSD_RLG1_START_PTR  0x0150
#define OSD_RLG2_START_PTR  0x0160
#define OSD_RLG3_START_PTR  0x0170
#define OSD_TOTAL_LUT_SIZE  (256 + 128)
#define OSD_FATT_START_PTR  0x0200
#define OSD_FATT_RAM_SIZE   (512)

#define OSD_REG_INDEX   0x00
#define OSD_REG_DATA    0x01
#define OSD_CTRL_REG    0x03
    #define OSD_CTRL_EN (1 << 7)
    #define OSD_ROW_EN  (1 << 5)
    #define OSD_RLG_EN  (1 << 4)
    #define OSD_RLS_EN  (1 << 3)
    #define OSD_WIN_EN  (1 << 2)
    #define OSD_MAT_EN  (1 << 1)
#define ORAM_MANIPULATE_7_0     0x08
#define ORAM_MANIPULATE_15_8    0x09
#define ORAM_MANIPULATE_23_16   0x0A
#define ORAM_MANIPULATE_31_24   0x0B
#define ORAM_GATE_BIT_7_0       0x0C
#define ORAM_GATE_BIT_15_8      0x0D
#define ORAM_GATE_BIT_23_16     0x0E
#define ORAM_GATE_BIT_31_24     0x0F
#define OSD_ORAM_PTR_L  0x10
#define OSD_ORAM_PTR_M  0x11
#define OSD_ACCESS_TYPE 0x12
    #define OSD_ACCESS_ORAM     0x00
    #define OSD_ACCESS_LUT      0x01    
    /* LUT data in SPI must DW boundary aligned; FAtt data (6Bytes) must be Word aligned */
    #define OSD_BYTE_W_1    (0 << 4)
    #define OSD_BYTE_W_2    (1 << 4)
    #define OSD_BYTE_W_4    (2 << 4)
    #define OSD_BYTE_W_6    (3 << 4)
    #define OSD_GATE_AUTO       (0 << 1)
    #define OSD_GATE_ALL        (1 << 1)
    #define OSD_GATE_11_0       (2 << 1)
    #define OSD_GATE_23_12      (3 << 1)
    #define OSD_GATE_27_16      (4 << 1)
    #define OSD_GATE_24_16P8_0  (5 << 1)// Char_Att's Index only
    #define OSD_GATE_31_25P15_9 (6 << 1)// Char_Att's Fg/Bg/Blink only
    #define OSD_GATE_SPECIAL    (7 << 1)
    #define OSD_ACCESS_WRITE    (0 << 6)
    #define OSD_ACCESS_READ     (1 << 6)
    #define OSD_ACCESS_REPLACE  (2 << 6)// LUT/FAtt has no RdBack piror Wr
    #define OSD_ACCESS_ADD      (3 << 6)
#define OSD_DATAPORT            0x13
#define OSD_BASS_PTR_ADDEND_7_0     0x14
#define OSD_BASS_PTR_ADDEND_15_8    0x15
#define OSD_BASS_PTR_ADDEND_23_16   0x16
#define OSD_BASS_PTR_ADDEND_26_24   0x17 // [26:0] for RLsAtt, [15:0] for FAtt/RowAtt/RLgAtt
    #define ADDEND_TYPE_DISABLE     (0 << 4) // add 0
    #define ADDEND_TYPE_FATT        (1 << 4)
    #define ADDEND_TYPE_ROWATT      (2 << 4)
    #define ADDEND_TYPE_RLGATT      (3 << 4)
    #define ADDEND_TYPE_RLSATT      (4 << 4)
#define OSD_SPI_ADDRESS_7_0         0x18
#define OSD_SPI_ADDRESS_15_8        0x19
#define OSD_SPI_ADDRESS_23_16       0x1A
#define OSD_SPI_ADDRESS_31_24       0x1B
#define OSD_SPI_LOAD_CNT_L          0x1C
#define OSD_SPI_LOAD_CNT_H          0x1D
    #define OSD_GO_BLOCK_F          (1 << 6)
    #define OSD_GO_LOAD             (1 << 7)

#define OSD_GLOBAL_STRENGTH         0xC7

#define OSD_PRELD_LNCNT_INDEX       0x64    // [11:0] (unit: Line, before V_Active; must <= (Panel_VS + Panel_VBP) )
#define OSD_A_FULLUP_INDEX          0x66    // Alpha blending Full Up
#define write_osd_reg(idx, dat)    \
	do { \
        write_p2(OSD_REG_INDEX, idx); \
        write_p2(OSD_REG_DATA, dat); \
	} while(0);

extern unsigned short cur_oram_ptr;
extern void osd_dump_oram(unsigned short ptr, unsigned int size);
extern void osd_clear_lut (void);
extern void osd_clear_fatt (void);
extern void osd_clear_oram (void);
extern void osd_init (void);
extern void osd_enable (unsigned char en);
extern void osd_disable (unsigned char en);
extern void osd_wr_en (unsigned char en);
#define osd_disable_all() osd_wr_en(0);
#define osd_enable_all() osd_wr_en(OSD_CTRL_EN|OSD_ROW_EN|OSD_RLG_EN|OSD_RLS_EN|OSD_WIN_EN|OSD_MAT_EN);
extern void oram_access_sp1_new(unsigned short ptr, unsigned char val, unsigned char mask, unsigned char type, bit use_cq);
extern void oram_access_sp2_new(unsigned short ptr, unsigned short val, unsigned short mask, unsigned char type, bit use_cq);
extern void oram_access_sp4_new(unsigned short ptr, unsigned long val, unsigned long mask, unsigned char type, bit use_cq);
#define oram_access_sp1(v1, v2, v3, v4)	oram_access_sp1_new(v1, v2, v3, v4, 1)
#define oram_access_sp2(v1, v2, v3, v4)	oram_access_sp2_new(v1, v2, v3, v4, 1)
#define oram_access_sp4(v1, v2, v3, v4)	oram_access_sp4_new(v1, v2, v3, v4, 1)
extern void oram_access_all(unsigned short ptr, unsigned long val, unsigned char type);
extern void oram_access_11_0(unsigned short ptr, unsigned short val, unsigned char type);
extern void oram_access_23_12(unsigned short ptr, unsigned short val, unsigned char type);
extern void oram_access_27_16(unsigned short ptr, unsigned short val, unsigned char type);
extern void oram_access_lut(unsigned short ptr, unsigned char lut, unsigned char type);
extern void oram_access_fidx(unsigned short ptr, unsigned short idx, unsigned char type);

/**
 * @func    char_lut_replace1
 * @brief   change CharAtt C13_FgC/C2_Remap/C4_Lut
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned char lut   C13_FgC/C2_Remap/C4_Lut Index
 * @return  none
 */
#define char_lut_replace1(ptr, lut) oram_access_sp1(ptr, lut, 0xfe, OSD_ACCESS_REPLACE)

/**
 * @func    char_lut_replace2
 * @brief   change 2 CharAtt C13_FgC/C2_Remap/C4_Lut
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned char lut   C13_FgC/C2_Remap/C4_Lut Index
 * @return  none
 */
#define char_lut_replace2(ptr, lut) oram_access_lut(ptr, lut, OSD_ACCESS_REPLACE)

/**
 * @func    char_fidx_replace1
 * @brief   change CharAtt Font Index
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned char idx   Font Index
 * @return  none
 */
#define char_fidx_replace1(ptr, idx) oram_access_sp2(ptr, idx, 0xfe, OSD_ACCESS_REPLACE)

/**
 * @func    char_fidx_replace2
 * @brief   change 2 CharAtt Font Index
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned char idx   Font Index
 * @return  none
 */
#define char_fidx_replace2(ptr, idx) oram_access_fidx(ptr, idx, OSD_ACCESS_REPLACE)

/**
 * @func    row_bg_replace
 * @brief   change RowAtt option1 Row_UinBgC
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned char bg    BgC Index
 * @return  none
 */
#define row_bg_replace(ptr, bg) oram_access_sp1(ptr, ((bg & 0xf) << 4), 0xf0, OSD_ACCESS_REPLACE)

/**
 * @func    win_width_replace
 * @brief   change WinAtt Width
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned short val  Width
 * @return  none
 */
#define win_width_replace(ptr, val) oram_access_11_0(ptr, (val - 1), OSD_ACCESS_REPLACE)

/**
 * @func    rlg_code_addr_replace
 * @brief   change image base address in ORAM
 * @param   unsigned short ptr	Att pointer in ORAM
 *          unsigned short addr  base address in ORAM
 * @return  none
 */
#define rlg_code_addr_replace(ptr, val) oram_access_sp2(ptr, val, 0xfffe, OSD_ACCESS_REPLACE)

/**
 * @func    rls_code_addr_replace
 * @brief   change image base address in SPI NOR Flash
 * @param   unsigned short ptr	Att pointer in ORAM
 *          unsigned long val  base address in SPI NOR Flash
 * @return  none
 */
#define rls_code_addr_replace(ptr, val) oram_access_sp4(ptr, val, 0xFFFFFFL, OSD_ACCESS_REPLACE)
#define rls_code_addr_replace2(ptr, val) oram_access_sp4_new(ptr, val, 0xFFFFFFL, OSD_ACCESS_REPLACE, 0)

#define rls_com_en_code_addr(ptr, en, val)		oram_access_sp4(ptr, (en ? (val | 0x40000000L) : val), 0x40FFFFFFL, OSD_ACCESS_REPLACE)
#define rls_att_en_codd_addr(ptr, size, en, val)	rls_com_en_code_addr((ptr + (size - 4)), en, val)

#define win_core_color_replace(ptr, idx)	oram_access_sp1_new(ptr, idx, 0x0f, OSD_ACCESS_REPLACE, 0)
#define win_height_replace(ptr, val)		oram_access_23_12(ptr, (val - 1), OSD_ACCESS_REPLACE)
#define win_att_highlights_replace(ptr, ccolor, bcolor, type)     \
											oram_access_sp4_new( (ptr + 4), ( ((ccolor & 0x0000000FL) << 8) | \
																		((bcolor & 0x0000000FL) << 12) | \
																		((type & 0x00000003L) << 18) ), \
																		0x000CFF00L, OSD_ACCESS_REPLACE, 0 )
#define win_att_highlight_id(ptr, id)		oram_access_sp1_new((ptr + 8 + 3), (id & 0x0f), 0x0f, OSD_ACCESS_REPLACE, 0)
#define win_x_add(ptr, add)					oram_access_11_0(ptr, add, OSD_ACCESS_ADD,)
#define win_y_add(ptr, add)					oram_access_23_12(ptr, (add << 4), OSD_ACCESS_ADD)
#define win_x_replace(ptr, val)				oram_access_11_0(ptr, val, OSD_ACCESS_REPLACE)
#define win_y_replace(ptr, val)				oram_access_23_12(ptr, (val << 4), OSD_ACCESS_REPLACE)
#define win_xy_replace(ptr, x, y)			oram_access_sp4_new(ptr, ((((y) & 0x00000FFFL) << 12)|((x) & 0x00000FFFL)), 0xFFFFFFL, OSD_ACCESS_REPLACE, 0)
#define rlg_x_add(ptr, add)					oram_access_11_0(ptr, add, OSD_ACCESS_ADD)
#define rlg_y_add(ptr, add)					oram_access_23_12(ptr, (add << 4), OSD_ACCESS_ADD)
#define rlg_x_replace(ptr, val)				oram_access_11_0(ptr, val, OSD_ACCESS_REPLACE)
#define rlg_y_replace(ptr, val)				oram_access_23_12(ptr, (val << 4), OSD_ACCESS_REPLACE)
#define rlg_xy_replace(ptr, x, y)			oram_access_sp4_new(ptr, ((((y) & 0x00000FFFL) << 12)|((x) & 0x00000FFFL)), 0xFFFFFFL, OSD_ACCESS_REPLACE, 0)
#define rlg_addr_replace(ptr, addr)			oram_access_sp2(ptr, addr, 0xFFFC, OSD_ACCESS_REPLACE)
#define rlg_strength_replace(ptr, level)	oram_access_sp1(ptr, level, 0x0F, OSD_ACCESS_REPLACE)
#define rls_x_add(ptr, add)					oram_access_11_0(ptr, add, OSD_ACCESS_ADD)
#define rls_y_add(ptr, add)					oram_access_23_12(ptr, (add << 4), OSD_ACCESS_ADD)
#define rls_x_replace(ptr, val)				oram_access_11_0(ptr, val, OSD_ACCESS_REPLACE)
#define rls_y_replace(ptr, val)				oram_access_23_12(ptr, (val << 4), OSD_ACCESS_REPLACE)
#define rls_xy_replace(ptr, x, y)			oram_access_sp4(ptr, ((((y) & 0x00000FFFL) << 12)|((x) & 0x00000FFFL)), 0xFFFFFFL, OSD_ACCESS_REPLACE)
#define rls_com_enable(ptr)					oram_access_sp1(ptr, 0x40, 0x40, OSD_ACCESS_REPLACE)
#define rls_com_disable(ptr)				oram_access_sp1(ptr, 0x00, 0x40, OSD_ACCESS_REPLACE)
#define rls_com_en(ptr, en) (en? rls_com_enable(ptr) : rls_com_disable(ptr))
#define rls_com_last_yes(ptr) oram_access_sp1(ptr, 0x08, 0x08, OSD_ACCESS_REPLACE)
#define rls_com_last_no(ptr) oram_access_sp1(ptr, 0x00, 0x08, OSD_ACCESS_REPLACE)
#define rls_com_last(ptr, yes) (yes? rls_com_last_yes(ptr) : rls_com_last_no(ptr))
#define rls_layer_replace(ptr, layer) oram_access_sp1(ptr, ((layer & 0x3) << 4), 0x30, OSD_ACCESS_REPLACE)
#define rls_com_strength(ptr, level) oram_access_sp1(ptr, level, 0x0f, OSD_ACCESS_REPLACE)
#define rls_lutldmode_replace(ptr, ldmode) oram_access_sp1(ptr, (ldmode << 4), 0x30, OSD_ACCESS_REPLACE)
#define rlx_com_maskrb_offset(ptr, offset)  oram_access_11_0(ptr, offset, OSD_ACCESS_REPLACE)
#define rlx_com_masklt_offset(ptr, offset)  oram_access_23_12(ptr, (offset << 4), OSD_ACCESS_REPLACE)
#define row_h_replace(ptr, h) oram_access_27_16(ptr, h, OSD_ACCESS_REPLACE)
#define row_x_replace(ptr, x) oram_access_27_16(ptr, x, OSD_ACCESS_REPLACE)
#define row_y_replace(ptr, y) oram_access_27_16(ptr, y, OSD_ACCESS_REPLACE)
#define row_gap_replace(ptr, gap) oram_access_27_16(ptr, gap, OSD_ACCESS_REPLACE)
//#define row_uinfg_replace(ptr, idx) oram_access_sp2(ptr, (0x0100|(idx & 0xf)), 0x010F, OSD_ACCESS_REPLACE)

#define win_att_width_replace(ptr, size, val)		win_width_replace((ptr + (size - size)), val)
#define rlg_att_code_addr_replace(ptr, size, val)	rlg_code_addr_replace((ptr + (size - 8)), val)
#define rls_att_code_addr_replace(ptr, size, val)	rls_code_addr_replace((ptr + (size - 4)), val)
#define rls_att_code_addr_replace2(ptr, size, val)	rls_code_addr_replace2((ptr + (size - 4)), val)
#define win_att_core_color_replace(ptr, size, idx)	win_core_color_replace((ptr + (size - 8)), idx)
#define win_att_height_replace(ptr, size, val)		win_height_replace((ptr + (size - 4)), val)

#define win_att_x_add(ptr, size, add)				win_x_add((ptr + (size - 4)), add)
#define win_att_y_add(ptr, size, add)				win_y_add((ptr + (size - 4)), add)
#define win_att_x_replace(ptr, size, val)			win_x_replace((ptr + (size - 4)), val)
#define win_att_y_replace(ptr, size, val)			win_y_replace((ptr + (size - 4)), val)
#define win_att_xy_replace(ptr, size, x, y)			win_xy_replace((ptr + (size - 4)), x, y)
#define rlg_att_x_add(ptr, size, add)				rlg_x_add((ptr + (size - 4)), add)
#define rlg_att_y_add(ptr, size, add)				rlg_y_add((ptr + (size - 4)), add)
#define rlg_att_x_replace(ptr, size, val)			rlg_x_replace((ptr + (size - 4)), val)
#define rlg_att_y_replace(ptr, size, val)			rlg_y_replace((ptr + (size - 4)), val)
#define rlg_att_xy_replace(ptr, size, x, y)			rlg_xy_replace((ptr + (size - 4)), x, y)
#define rlg_att_addr_replace(ptr, size, addr)		rlg_addr_replace((ptr + (size - 4)), addr)
#define rlg_att_strength_replace(ptr, size, level)	rlg_strength_replace((ptr + (size - 8)), level)
#define rls_att_x_add(ptr, size, add)				rls_x_add((ptr + (size - 8)), add)
#define rls_att_y_add(ptr, size, add)				rls_y_add((ptr + (size - 8)), add)
#define rls_att_x_replace(ptr, size, val)			rls_x_replace((ptr + (size - 8)), val)
#define rls_att_y_replace(ptr, size, val)			rls_y_replace((ptr + (size - 8)), val)
#define rls_att_xy_replace(ptr, size, x, y)			rls_xy_replace((ptr + (size - 8)), x, y)
#define rls_att_enable(ptr, size)					rls_com_enable((ptr + (size - 1)))
#define rls_att_disable(ptr, size)					rls_com_disable((ptr + (size - 1)))
#define rls_att_en(ptr, size, en)					rls_com_en((ptr + (size - 1)), en)
#define rls_att_last_yes(ptr, size)					rls_com_last_yes((ptr + (size - 1)))
#define rls_att_last_no(ptr, size)					rls_com_last_no((ptr + (size - 1)))
#define rls_att_last(ptr, size, yes)				rls_com_last((ptr + (size - 1)), yes)
#define rls_att_strength(ptr, size, level)			rls_com_strength((ptr + (size - 5)), level)
#define rls_att_lutldmode_replace(ptr, size, ldmode) rls_lutldmode_replace((ptr + (size - 1)), ldmode)
#define rlx_att_maskrb_offset(ptr,size, offset)  rlx_com_maskrb_offset((ptr + (size - size)), offset)
#define rlx_att_masklt_offset(ptr,size, offset)  rlx_com_masklt_offset((ptr + (size - size)), offset)
#define row_att_h_replace(ptr, size, h) row_h_replace((ptr + (size - 14)), h)
#define row_att_x_replace(ptr, size, x) row_x_replace((ptr + (size - 10)), x)
#define row_att_y_replace(ptr, size, y) row_y_replace((ptr + (size - 6)), y)
#define row_att_gap_replace(ptr, size, gap)   row_y_replace((ptr + (size - 6)), y)


extern void oram_access_cq_start(void);
extern void oram_access_sp1_cq(unsigned short ptr, unsigned char val, unsigned char mask, unsigned char type);
extern void oram_access_sp4_cq(unsigned short ptr, unsigned long val, unsigned long mask, unsigned char type);
extern void oram_access_cq_finish(void);
#define rls_att_cq_start()		oram_access_cq_start()
#define rls_att_cq_finish()		oram_access_cq_finish()
#define rls_com_enable_cq(ptr)	oram_access_sp1_cq(ptr, 0x40, 0x40, OSD_ACCESS_REPLACE)
#define rls_com_disable_cq(ptr)	oram_access_sp1_cq(ptr, 0x00, 0x40, OSD_ACCESS_REPLACE)
#define rls_com_en_cq(ptr, en)			(en ? rls_com_enable_cq(ptr) : rls_com_disable_cq(ptr))
#define rls_att_en_cq(ptr, size, en)	rls_com_en_cq((ptr + (size - 1)), en)
#define rls_code_addr_cq(ptr, val)		oram_access_sp4_cq(ptr, val, 0xFFFFFFL, OSD_ACCESS_REPLACE)
#define rls_att_code_addr_cq(ptr, size, val)	rls_code_addr_cq((ptr + (size - 4)), val)
#define rls_com_en_code_addr_cq(ptr, en, val)		oram_access_sp4_cq(ptr, (en ? (val | 0x40000000L) : val), 0x40FFFFFFL, OSD_ACCESS_REPLACE)
#define rls_att_en_codd_addr_cq(ptr, size, en, val)	rls_com_en_code_addr_cq((ptr + (size - 4)), en, val)

enum SPI_LOAD_TYPE {
	LOAD_TYPE_RLg = 0,
	LOAD_TYPE_Row,
	LOAD_TYPE_RLs,
	LOAD_TYPE_ORAM,
	LOAD_TYPE_LUT,
	LOAD_TYPE_FATT
};
#define SPI_LOAD_MAX	(16 * 1024)
extern bit spi_load_type(unsigned short oram_addr, unsigned long nor_addr, unsigned short count, unsigned long addend, unsigned char type);
extern bit spi_dma(unsigned char *xram_addr, unsigned long nor_addr, unsigned short count);

//#define spi_load_oram(o_addr, n_addr, count)			spi_load_type(o_addr, n_addr, count, 0, LOAD_TYPE_ORAM)
#define spi_load_rlg(o_addr, n_addr, count, addend)		spi_load_type(o_addr, n_addr, count, addend, LOAD_TYPE_RLg)
#define spi_load_row(o_addr, n_addr, count, addend)		spi_load_type(o_addr, n_addr, count, addend, LOAD_TYPE_Row)
#define spi_load_rls(o_addr, n_addr, count, addend)		spi_load_type(o_addr, n_addr, count, addend, LOAD_TYPE_RLs)
#if defined(_EX_MCU_VERSION) && defined(_NON_SPI_FLASH)
extern void i2c_load_lut(unsigned short oram_addr, unsigned long nor_addr, 
					unsigned short count);
extern void i2c_load_fatt(unsigned short oram_addr, unsigned long nor_addr, 
					unsigned short count, unsigned char offset);
extern void i2c_load_oram(unsigned short oram_addr, unsigned long nor_addr, 
					unsigned short count);
#define spi_load_oram(o_addr, n_addr, count)            i2c_load_oram(o_addr, n_addr, count)
#define spi_load_fatt(o_addr, n_addr, count, addend)	i2c_load_fatt(o_addr, n_addr, count, addend)
#define spi_load_lut(o_addr, n_addr, count)				i2c_load_lut(o_addr, n_addr, count)
#else
extern bit spi_load_oram_new(unsigned short oram_addr, unsigned long nor_addr, unsigned short count, bit use_cq);
#define spi_load_oram(v1, v2, v3)	spi_load_oram_new(v1, v2, v3, 0)
#define spi_load_fatt(o_addr, n_addr, count, addend)	spi_load_type(o_addr, n_addr, count, addend, LOAD_TYPE_FATT)
#define spi_load_lut(o_addr, n_addr, count)				spi_load_type(o_addr, n_addr, count, 0, LOAD_TYPE_LUT)
#endif
// Row Component
#define ROW_REG_CONFIG_COUNT	14
#define ROW_REG_START_INDEX		0x80
#define ROW_REG_ENABLE_7_0		0x80
#define ROW_REG_ENABLE_15_8		0x81
#define ROW_REG_ENABLE_23_16	0x82
#define ROW_REG_ENABLE_31_24	0x83
#define ROW_OSD_PTR_INDEX		0x40
#define ROW_OSD_POS_INDEX		0x00
#define ROW_REG_HILT_INDEX		0x86
	#define ROW_HILT_EN			0x20
#define ROW_REG_STRENG_INDEX	0x87	
extern void row_config(unsigned char offset, unsigned long nor_addr);
#define row_A_config(addr)	row_config(0, addr)
#define row_B_config(addr)	row_config(1, addr)
#define row_C_config(addr)	row_config(2, addr)
#define row_D_config(addr)	row_config(3, addr)
extern void row_set_hilt_sel(unsigned char offset, unsigned char sel);
#define row_A_set_hilt_sel(sel)	row_set_hilt_sel(0, sel)
#define row_B_set_hilt_sel(sel)	row_set_hilt_sel(8, sel)
#define row_C_set_hilt_sel(sel)	row_set_hilt_sel(16, sel)
#define row_D_set_hilt_sel(sel)	row_set_hilt_sel(24, sel)
extern void row_set_en(unsigned char offset, unsigned long en);
#define row_A_set_en(en)			row_set_en(0, en)
#define row_B_set_en(en)			row_set_en(8, en)
#define row_C_set_en(en)			row_set_en(16, en)
#define row_D_set_en(en)			row_set_en(24, en)
extern unsigned long row_get_en(unsigned char offset);
#define row_A_get_en()			row_get_en(0)
#define row_B_get_en()			row_get_en(8)
#define row_C_get_en()			row_get_en(16)
#define row_D_get_en()			row_get_en(24)
#define ROW_REG_EN_CTRL	0x87
extern void row_enable(unsigned char offset);
#define row_A_enable()	row_enable(0)
#define row_B_enable()	row_enable(8)
#define row_C_enable()	row_enable(16)
#define row_D_enable()	row_enable(24)
extern void row_disable(unsigned char offset);
#define row_A_disable()	row_disable(0)
#define row_B_disable()	row_disable(8)
#define row_C_disable()	row_disable(16)
#define row_D_disable()	row_disable(24)
extern void row_set_strength(unsigned char offset, unsigned char level);
#define	row_A_set_strength(level)		row_set_strength(0,level)
#define	row_B_set_strength(level)		row_set_strength(8,level)
#define	row_C_set_strength(level)		row_set_strength(16,level)
#define	row_D_set_strength(level)		row_set_strength(24,level)
extern void row_char_blink(unsigned char mode);

// RLg Component
#define RLG_REG_CONFIG_COUNT	16
#define RLG_REG_START_INDEX     0xA0
#define RLG_OSD_PTR_INDEX       0x48
#define RLG_OSD_POS_INDEX       0x10
extern void rlg_config(unsigned char offset, unsigned long nor_addr);
#define rlg_A_config(addr)	rlg_config(0, addr)
#define rlg_B_config(addr)	rlg_config(1, addr)
#define rlg_C_config(addr)	rlg_config(2, addr)
#define rlg_D_config(addr)	rlg_config(3, addr)
#define RLG_REG_EN_CTRL     0xA7
extern void rlg_enable(unsigned char offset);
#define rlg_A_enable()	rlg_enable(0)
#define rlg_B_enable()	rlg_enable(8)
#define rlg_C_enable()	rlg_enable(16)
#define rlg_D_enable()	rlg_enable(24)
extern void rlg_disable(unsigned char offset);
#define rlg_A_disable()	rlg_disable(0)
#define rlg_B_disable()	rlg_disable(8)
#define rlg_C_disable()	rlg_disable(16)
#define rlg_D_disable()	rlg_disable(24)
#define RLG_REG_ENABLE      0xA4
extern void rlg_set_en(unsigned char offset, unsigned short en);
#define rlg_A_set_en(en)	rlg_set_en(0, en)
#define rlg_B_set_en(en)	rlg_set_en(8, en)
#define rlg_C_set_en(en)	rlg_set_en(16, en)
#define rlg_D_set_en(en)	rlg_set_en(24, en)
extern void rlg_set_strength(unsigned char offset, unsigned char level);
#define rlg_A_set_strength(level)	rlg_set_strength(0, level)
#define rlg_B_set_strength(level)	rlg_set_strength(8, level)
#define rlg_C_set_strength(level)	rlg_set_strength(16, level)
#define rlg_D_set_strength(level)	rlg_set_strength(24, level)
#define OSD_MAX_STRENGTH_LEV  0xf
extern void rlg_set_ptr(unsigned char offset, unsigned short ptr);
#define rlg_A_set_ptr(optr)	rlg_set_ptr(0, optr)
#define rlg_B_set_ptr(optr)	rlg_set_ptr(1, optr)
#define rlg_C_set_ptr(optr)	rlg_set_ptr(2, optr)
#define rlg_D_set_ptr(optr)	rlg_set_ptr(3, optr)

extern void rlg_swap_image(unsigned short att_ptr, unsigned short oram_ptr, unsigned long nor_addr,
							unsigned short length, bit use_cq);

// RLs Component
#define RLS_REG_EN_CTRL 0xCF
    #define RLS_D_EN    (1 << 7)
    #define RLS_C_EN    (1 << 6)
    #define RLS_B_EN    (1 << 5)
    #define RLS_A_EN    (1 << 4)
extern void osd_rls_enable (unsigned char en);
extern void osd_rls_disable (unsigned char en);
#define rls_D_enable() 	osd_rls_enable(RLS_D_EN)
#define rls_C_enable() 	osd_rls_enable(RLS_C_EN)
#define rls_B_enable() 	osd_rls_enable(RLS_B_EN)
#define rls_A_enable() 	osd_rls_enable(RLS_A_EN)
#define rls_D_disable() osd_rls_disable(RLS_D_EN)
#define rls_C_disable() osd_rls_disable(RLS_C_EN)
#define rls_B_disable() osd_rls_disable(RLS_B_EN)
#define rls_A_disable() osd_rls_disable(RLS_A_EN)
extern void osd_wr_rls_en (unsigned char en);
// Rev. B: Fixed load LUT issue when DClko slower than the XClk
extern void osd_rls_lut_ws (unsigned char waits);
    #define LUT_WS_0T   (0)
    #define LUT_WS_1T   (1 << 2)    // in HClk
    #define LUT_WS_HSK  (2 << 2)    // Handshake
// BurstMore: Increase Burst Length for ~(RLsD & 8/16BP)
extern void osd_rls_burstmore (unsigned char en);
// Threshold of FiFo 2/4/8/16BP trig ragnge: 0 ~ 0xf
#define RLS_REG_FIFO_42BP 0xCC
#define RLS_REG_FIFO_168BP 0xCD
extern void rls_fifo8bp_trig (unsigned char thd);
extern void rls_fifo4bp_trig (unsigned char thd);
extern void rls_fifo2bp_trig (unsigned char thd);
// RLs Preload Line Count, before VACtive, must <= (Panel_VS + Panel_VBP)
#define RLS_OREG_PL_LNCNT   0x64
extern void rls_preload_lncnt (unsigned char lncnt);
// RLs 
#define RLS_REG_CONFIG_COUNT	8//14
#define RLS_REG_START_INDEX     0xD0
#define RLS_OSD_PTR_INDEX       0x58
#define RLS_OSD_POS_INDEX       0x30
extern void rls_config(unsigned char offset, unsigned long nor_addr);
#define rls_A_config(addr)	rls_config(0, addr)
#define rls_B_config(addr)	rls_config(1, addr)
#define rls_C_config(addr)	rls_config(2, addr)
#define rls_D_config(addr)	rls_config(3, addr)
extern void rls_set_ptr(unsigned char offset, unsigned short optr);
#define rls_A_set_ptr(optr)	rls_set_ptr(0, optr)
#define rls_B_set_ptr(optr)	rls_set_ptr(1, optr)
#define rls_C_set_ptr(optr)	rls_set_ptr(2, optr)
#define rls_D_set_ptr(optr)	rls_set_ptr(3, optr)

// Mat Component
#define MAT_REG_INDEX   0x50
#define MAT_REG_CONFIG_COUNT	0x0C
#define MAT_A_REG_OFFSET	(MAT_REG_CONFIG_COUNT * 0)
#define MAT_B_REG_OFFSET	(MAT_REG_CONFIG_COUNT * 1)
#define MAT_C_REG_OFFSET	(MAT_REG_CONFIG_COUNT * 2)
#define MAT_D_REG_OFFSET	(MAT_REG_CONFIG_COUNT * 3)
extern void mat_reg_config(unsigned char offset, unsigned char *buf);
#define mat_A_reg_config(buf)	mat_reg_config(MAT_A_REG_OFFSET, buf)
#define mat_B_reg_config(buf)	mat_reg_config(MAT_B_REG_OFFSET, buf)
#define mat_C_reg_config(buf)	mat_reg_config(MAT_C_REG_OFFSET, buf)
#define mat_D_reg_config(buf)	mat_reg_config(MAT_D_REG_OFFSET, buf)
extern void mat_load_config(unsigned char offset, unsigned long nor_addr);
#define mat_A_load_config(addr)	mat_load_config(MAT_A_REG_OFFSET, addr)
#define mat_B_load_config(addr)	mat_load_config(MAT_B_REG_OFFSET, addr)
#define mat_C_load_config(addr)	mat_load_config(MAT_C_REG_OFFSET, addr)
#define mat_D_load_config(addr)	mat_load_config(MAT_D_REG_OFFSET, addr)
#define MAT_REG_EN_CTRL     0x5B
extern void mat_disable(unsigned char offset);
#define mat_A_disable()	mat_disable(MAT_A_REG_OFFSET)
#define mat_B_disable()	mat_disable(12)
#define mat_C_disable()	mat_disable(24)
#define mat_D_disable()	mat_disable(36)
extern void mat_enable(unsigned char offset);
#define mat_A_enable()	mat_enable(MAT_A_REG_OFFSET)
#define mat_B_enable()	mat_enable(MAT_B_REG_OFFSET)
#define mat_C_enable()	mat_enable(MAT_C_REG_OFFSET)
#define mat_D_enable()	mat_enable(MAT_D_REG_OFFSET)

// Win Component
#define WIN_REG_START_INDEX  0x40
#define WIN_OSD_PTR_INDEX   0x50
#define WIN_OSD_POS_INDEX   0x20
#define WIN_REG_ENABLE_L    0x40
#define WIN_REG_ENABLE_M    0x41
#define WIN_REG_HIGHLIGHT   0x42
    #define WIN_HILT_EN     0x10
#define WIN_REG_STRENGTH    0x43
#define WIN_D_REG_ENABLE_L  0x4C
#define WIN_D_REG_ENABLE_M  0x4D
#define WIN_D_REG_HIGHLIGHT 0x4E
#define WIN_D_REG_STRENGTH  0x4F
    #define WIN_D_EN    (1 << 7)
#define WIN_D_OSD_POS   (WIN_OSD_POS_INDEX + 4 * 3)
#define WIN_D_OSD_PTR   (WIN_OSD_PTR_INDEX + 2 * 3)
#define LOAD_WIN_LUT_MAX_COUNT	(16 * 4)
#define WIN_REG_CONFIG_COUNT	6
extern void win_config(unsigned char offset, unsigned char *buf);
#define WIN_ATT_LEN		(4 * 3)
#define WIN_ATT_0		0x40
#define WIN_ATT_1		0x60
#define WIN_ATT_2		0x80
extern void win_load_lut(unsigned char *buf, unsigned char count);
#define win_A_config(buf)	win_config(0, buf)
#define win_B_config(buf)	win_config(1, buf)
#define win_C_config(buf)	win_config(2, buf)
#define win_D_config(buf)	win_config(3, buf)
#define WIN_REG_LOAD_CONFIG_COUNT	12
extern void win_load_config(unsigned char offset, unsigned long nor_addr);
#define win_A_load_config(addr)	win_load_config(0, addr)
#define win_B_load_config(addr)	win_load_config(1, addr)
#define win_C_load_config(addr)	win_load_config(2, addr)
#define win_D_load_config(addr)	win_load_config(3, addr)
extern void win_set_hilt_sel(unsigned char offset, unsigned char sel);
#define win_A_set_hilt_sel(sel)	win_set_hilt_sel(0, sel)
#define win_B_set_hilt_sel(sel)	win_set_hilt_sel(4, sel)
#define win_C_set_hilt_sel(sel)	win_set_hilt_sel(8, sel)
#define win_D_set_hilt_sel(sel)	win_set_hilt_sel(12, sel)
extern void win_hilt_enable(unsigned char offset, bit en);
#define win_A_hilt_enalbe()		win_hilt_enable(0, 1)
#define win_B_hilt_enalbe()		win_hilt_enable(4, 1)
#define win_C_hilt_enalbe()		win_hilt_enable(8, 1)
#define win_D_hilt_enalbe()		win_hilt_enable(12, 1)
#define win_A_hilt_disable()	win_hilt_enable(0, 0)
#define win_B_hilt_disable()	win_hilt_enable(4, 0)
#define win_C_hilt_disable()	win_hilt_enable(8, 0)
#define win_D_hilt_disable()	win_hilt_enable(12, 0)
extern void win_set_en(unsigned char offset, unsigned short en);
#define win_A_set_en(en)	win_set_en(0, en)
#define win_B_set_en(en)	win_set_en(4, en)
#define win_C_set_en(en)	win_set_en(8, en)
#define win_D_set_en(en)	win_set_en(12, en)
extern void win_enable(unsigned char offset, bit en);
#define win_A_enable(en)	win_enable(0, en)
#define win_B_enable(en)	win_enable(4, en)
#define win_C_enable(en)	win_enable(8, en)
#define win_D_enable(en)	win_enable(12, en)
#define win_A_disable()	win_enable(0, 0)
#define win_B_disable()	win_enable(4, 0)
#define win_C_disable()	win_enable(8, 0)
#define win_D_disable()	win_enable(12, 0)
extern void win_set_ptr(unsigned char offset, unsigned short optr);
#define win_A_set_ptr(optr)	win_set_ptr(0, optr)
#define win_B_set_ptr(optr)	win_set_ptr(1, optr)
#define win_C_set_ptr(optr)	win_set_ptr(2, optr)
#define win_D_set_ptr(optr)	win_set_ptr(3, optr)
extern void win_set_strength(unsigned char offset, unsigned char level);
#define win_A_set_strength(level)	win_set_strength(0, level)
#define win_B_set_strength(level)	win_set_strength(4, level)
#define win_C_set_strength(level)	win_set_strength(8, level)
#define win_D_set_strength(level)	win_set_strength(12, level)
extern void win_add_lut(unsigned char offset, unsigned char *buf, unsigned char count);

/**
 * @func    change_string_lut
 * @brief   change CharAtt C13_FgC/C2_Remap/C4_Lut
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned char lut   C13_FgC/C2_Remap/C4_Lut Index
 *          unsigned char *str  string point
 *          unsigned char size  char size
 * @return  none
 */
extern void change_string_lut(unsigned short ptr, unsigned char lut,
                            unsigned char *str, unsigned char size);
/**
 * @func    put_string
 * @brief   put CharAtts
 * @param   unsigned short ptr	ORAM Pointer
 *          unsigned char *str  string point
 *          unsigned char size  char size
 * @return  none
 */
extern void put_string(unsigned short ptr, unsigned char *str, unsigned char size);

extern void osd_original_x(unsigned short x);
extern void osd_original_y(unsigned short y);
extern void osd_strength(unsigned char stren);

/* for font database */
#define OWL_FONTDB_HEADER_SIZE      16
#define OWL_FONTDB_FATT_OFFSET      0
#define OWL_FONTDB_BMPLEN_OFFSET    6L
#define OWL_FONTDB_UCODE_OFFSET     8L
#define OWL_FONTDB_BMPADDR_OFFSET   10
extern unsigned long find_code_addr (unsigned long addr, 
                    unsigned short cnt, unsigned short uc);


void osd_en_error_detection(void);
extern void osd_isr(void);

#endif  /* OSD_FUNC_H_ */
