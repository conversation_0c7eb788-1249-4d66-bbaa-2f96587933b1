/*
 * bsp_isp.h
 *
 *  Created on: 2024Äê1ÔÂ19ÈÕ
 *      Author: Lenovo
 */

#ifndef BSP_ISP_H_
#define BSP_ISP_H_

#define EV_ISP_WRITE_ANGLE     0x1
#define EV_ISP_WRITE_VIEW      0x2
#define EV_ISP_WRITE_LAWS	   0x04
#define EV_ISP_READ_VER        0x08
#if (LIN_ISP_OTHER_QUERY==1)
#define EV_ISP_READ_ANGLE      0x10
#define EV_ISP_READ_VIEW       0x20
#define EV_ISP_READ_LAWS       0x40
#endif
#if (ISP_VEHICLE_TYPE_QUERY==1)
#define EV_ISP_READ_VEHICLE_TYPE 0x80
#endif

void isp_event_post(uint32_t ev);
void isp_process(void);
void isp_init(void);
#endif /* BSP_ISP_H_ */
